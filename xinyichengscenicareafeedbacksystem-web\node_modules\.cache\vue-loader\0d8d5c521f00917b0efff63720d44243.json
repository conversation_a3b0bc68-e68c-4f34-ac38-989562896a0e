{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Header.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Header.vue", "mtime": 1747056915467}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Header.vue"], "names": [], "mappings": ";AA8DA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACV,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;OACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;;EAEF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC;;;;IAIF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;IACF,CAAC;EACH,CAAC;AACH,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/components/Header.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <header id=\"topnav\">\r\n            <nav class=\"navbar-custom\">\r\n                <ul class=\"list-unstyled topbar-right-menu float-right mb-0\">\r\n                  \r\n                    <li class=\"dropdown notification-list\">\r\n                        <a class=\"nav-link dropdown-toggle nav-user\" data-toggle=\"dropdown\" href=\"#\" role=\"button\"\r\n                            aria-haspopup=\"false\" aria-expanded=\"false\" @click=\"toggleShowExist\">\r\n                            <img src=\"../assets/images/users/avatar-1.jpg\" alt=\"user\" class=\"rounded-circle\"> <span class=\"ml-1\">\r\n                                【<b style=\"color: red;\">{{role}}</b>】{{userLname}}<i class=\"mdi mdi-chevron-down\"></i> </span>\r\n                        </a>\r\n                        <div class=\"dropdown-menu dropdown-menu-right dropdown-menu-animated profile-dropdown \" v-show=\"showexist\">\r\n                            <!-- item-->\r\n                            <div class=\"dropdown-item noti-title\">\r\n                                <h6 class=\"text-overflow m-0\">Welcome !</h6>\r\n                            </div>\r\n\r\n<!--\r\n                           \r\n                            <a href=\"/\" class=\"dropdown-item notify-item\" target=\"_blank\">\r\n                                <i class=\"dripicons-home\"></i> <span>网站首页</span>\r\n                            </a>\r\n-->\r\n\r\n\r\n                            <!-- item-->\r\n                          \r\n                              <router-link to=\"/Password\" class=\"dropdown-item notify-item\">\r\n                              <i class=\"dripicons-lock\"></i> <span>修改密码</span>\r\n                            </router-link>\r\n\r\n                            <!-- item-->\r\n                            <a href=\"javascript:void(0);\" class=\"dropdown-item notify-item\" @click=\"exit\">\r\n                                <i class=\"dripicons-power\"></i> <span>退出登录</span>\r\n                            </a>\r\n\r\n                        </div>\r\n                    </li>\r\n                   \r\n                </ul>\r\n\r\n                <ul class=\"list-unstyled menu-left mb-0\">\r\n                    <li class=\"float-left\">\r\n                        <a href=\"/main\" class=\"logo\">\r\n                            <span class=\"logo-lg\" style=\"font-size: 18px;color: #fff;\">\r\n                                <img src=\"../assets/images/logo_sm.png\" alt=\"\" height=\"30\">\r\n                                心怡程景区意见反馈系统\r\n                            </span>\r\n                            <span class=\"logo-sm\">\r\n                                <img src=\"../assets/images/logo_sm.png\" alt=\"\" height=\"28\">\r\n                            </span>\r\n                        </a>\r\n                    </li>\r\n               \r\n                 \r\n                </ul>\r\n            </nav>\r\n            <!-- end navbar-custom -->\r\n        </header>      \r\n\r\n</template>\r\n<script>\r\nimport $ from \"jquery\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      activeIndex: \"1\",\r\n      activeIndex2: \"1\",\r\n      showexist: false,\r\n      userLname: \"\",\r\n      role: \"\",\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");  \r\n\r\n    //判断是否登录\r\n    if(this.userLname == null){     \r\n       this.$router.push(\"/login\");\r\n    }\r\n\r\n  },\r\n  methods: {\r\n    handleSelect(key, keyPath) {\r\n      console.log(key, keyPath);\r\n    },\r\n    toggleShowExist() {    \r\n      this.showexist = !this.showexist;\r\n\r\n      if(this.showexist){\r\n        $(\".dropdown-menu\").removeClass(\"show\");\r\n      }else{\r\n        $(\".dropdown-menu\").addClass(\"show\");\r\n      }\r\n\r\n   \r\n\r\n    },\r\n\r\n    exit: function() {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem(\"userLname\");\r\n          sessionStorage.removeItem(\"role\");\r\n          _this.$router.push(\"/login\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    toggleFullScreen() {\r\n      const elem = document.documentElement;\r\n      if (elem.requestFullscreen) {\r\n        elem.requestFullscreen();\r\n      } else if (elem.mozRequestFullScreen) {\r\n        elem.mozRequestFullScreen();\r\n      } else if (elem.webkitRequestFullscreen) {\r\n        elem.webkitRequestFullscreen();\r\n      } else if (elem.msRequestFullscreen) {\r\n        elem.msRequestFullscreen();\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n"]}]}