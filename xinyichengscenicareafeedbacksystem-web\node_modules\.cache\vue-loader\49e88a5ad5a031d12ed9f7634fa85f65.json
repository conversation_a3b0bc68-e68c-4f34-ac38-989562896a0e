{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\TopMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\TopMenu.vue", "mtime": 1748954728813}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAiLi4vLi4vdXRpbHMvaHR0cCI7DQppbXBvcnQgIi4uL2Fzc2V0cy9jc3MvcWJvb3RzdHJhcC5jc3MiDQppbXBvcnQgIi4uL2Fzc2V0cy9jc3MvcWJ4c2xpZGVyLmNzcyINCmltcG9ydCAiLi4vYXNzZXRzL2Nzcy9xc3R5bGUuY3NzIg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiVG9wTWVudSIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGlzbG9naW46IHRydWUsDQogICAgICBsbmFtZTogJycsDQogICAgICBpc2hvdzogZmFsc2UsDQogICAgICBrZXk6ICcnLA0KICAgICAgDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmxuYW1lID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgibG5hbWUiKTsNCiAgICBpZiAodGhpcy5sbmFtZSkgew0KICAgICAgdGhpcy5pc2xvZ2luID0gZmFsc2U7DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7ICAgIA0KICAgICAgIA0KICAgIA0KICAgIGV4aXQ6IGZ1bmN0aW9uICgpIHsNCiAgICAgIHZhciBfdGhpcyA9IHRoaXM7DQogICAgICB0aGlzLiRjb25maXJtKCLnoa7orqTpgIDlh7rlkJc/IiwgIuaPkOekuiIsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgfSkNCiAgICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgICBzZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKCJsbmFtZSIpOw0KICAgICAgICAgICAgbG9jYXRpb24uaHJlZj0iL2luZGV4IjsNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5jYXRjaCgoKSA9PiB7IH0pOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\TopMenu.vue"], "names": [], "mappings": ";AAmFA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAET,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;;IAGP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;UACG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACvB,CAAC;EACH,CAAC;AACH,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/components/TopMenu.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  \r\n   <header>\r\n             <div id=\"top-bar\" class=\"top-bar\">\r\n                 <div class=\"container\">\r\n                     <div class=\"row\">\r\n                         <div class=\"col-md-6\">\r\n                             <div class=\"top-left\">\r\n                                 <ul>\r\n                                     <li><i class=\"glyphicon glyphicon-map-marker\"></i>沈河区沈阳路171号</li>\r\n                                     <li><a href=\"mailto:<EMAIL>\" title=\"email\"><i class=\"glyphicon glyphicon-envelope\"></i><EMAIL></a></li>\r\n                                     <li><a href=\"\" title=\"Call\"><i class=\"glyphicon glyphicon-phone-alt\"></i>024-24843001</a></li>\r\n                                 </ul>\r\n                             </div>\r\n                         </div>\r\n                         <div class=\"col-md-3\">\r\n                             <div class=\"top-right\" style=\"margin-top: -8px;\">\r\n                                 <form action=\"/newsList\" method=\"get\" class=\"search-form\">\r\n                                     <input type=\"text\" name=\"keys\" placeholder=\"搜索...\" aria-label=\"搜索\" style=\"height: 33px; color: #333;\" />\r\n                                     <button type=\"submit\" class=\"btn btn-primary\">搜索</button>\r\n                                 </form>\r\n                             </div>\r\n                         </div>\r\n                         <div class=\"col-md-3 text-right\">\r\n                             <div class=\"top-right\">                               \r\n                                 \r\n         \r\n\t\t\t\t    <li v-if=\"islogin\">\r\n\t\t\t\t\t<a href=\"/Ureg\" style=\"display: contents; color: #fff;\">用户注册</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                <a href=\"/Ulogin\" style=\"display: contents; color: #fff;\">用户登录</a>\r\n\t\t\t\t</li>\r\n\t\t\t\t  <block v-else>\r\n            欢迎您：<a href=\"javascript:void(0);\" style=\"color: #fff;\">{{ lname }}</a>\r\n            &nbsp;|&nbsp;\r\n            <a href=\"javascript:void(0);\" @click=\"exit\" style=\"color: #fff;\">退出登录</a>\r\n          </block>\r\n\t\t\t\t\r\n\r\n                             </div>\r\n                         </div>\r\n                     </div>\r\n                 </div>\r\n             </div>\r\n             <!-- Fixed navbar -->\r\n             <nav class=\"navbar navbar-default navbar-fixed-top\">\r\n                 <div class=\"container\">\r\n                     <div class=\"navbar-header\">\r\n                         <button type=\"button\" class=\"navbar-toggle collapsed\" data-toggle=\"collapse\" data-target=\"#navbar\" aria-expanded=\"false\" aria-controls=\"navbar\"><span class=\"sr-only\">导航菜单</span><span class=\"icon-bar\"></span><span class=\"icon-bar\"></span><span class=\"icon-bar\"></span></button>\r\n                         <a href=\"/index\"><img src=\"../assets/images/logo.png\" style=\"height: 80px;\" class=\"logo\"></a>\r\n                     </div>\r\n                     <div id=\"navbar\" class=\"navbar-collapse collapse\">\r\n                         <ul class=\"nav navbar-nav\">\r\n                             <li class=''><a href=\"/index\">首 页</a> </li>\r\n                             <li class=\" dropdown\"><a href=\"/newsList\">新闻资讯</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/ticketInfoList\">票务信息</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/board\">意见反馈</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/bbs\">互动交流</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/test\">问卷调查</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/uweclome\">个人中心</a>  </li>\r\n\r\n                             <!-- <li class=\" dropdown\">\r\n                                 <a href=\"cpzx.html\">产品中心</a>\r\n                                 <a id=\"app_menudown\" class=\"dropdown-toggle\" data-toggle=\"dropdown\" role=\"button\" aria-expanded=\"false\"><span class=\"glyphicon glyphicon-menu-down btn-xs\"></span></a><ul class=\"dropdown-menu nav_small\" role=\"menu\">\r\n     \r\n                                     <li><a href=\"cpzx.html\">产品分类1</a></li>\r\n     \r\n                                     <li><a href=\"cpzx.html\">产品分类2</a></li>\r\n     \r\n                                     <li><a href=\"cpzx.html\">产品分类3</a></li>\r\n     \r\n                                 </ul>\r\n                             </li> -->\r\n                          \r\n                         </ul>\r\n                     </div>\r\n                     <!--/.nav-collapse -->\r\n                 </div>\r\n             </nav>\r\n         </header>    \r\n\r\n \r\n</template>\r\n<script>\r\nimport request, { base } from \"../../utils/http\";\r\nimport \"../assets/css/qbootstrap.css\"\r\nimport \"../assets/css/qbxslider.css\"\r\nimport \"../assets/css/qstyle.css\"\r\nexport default {\r\n  name: \"TopMenu\",\r\n  data() {\r\n    return {\r\n      islogin: true,\r\n      lname: '',\r\n      ishow: false,\r\n      key: '',\r\n      \r\n    };\r\n  },\r\n  mounted() {\r\n    this.lname = sessionStorage.getItem(\"lname\");\r\n    if (this.lname) {\r\n      this.islogin = false;\r\n    }\r\n  },\r\n  methods: {    \r\n       \r\n    \r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n          .then(() => {\r\n            sessionStorage.removeItem(\"lname\");\r\n            location.href=\"/index\";\r\n          })\r\n          .catch(() => { });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n\r\n</style>\r\n\r\n\r\n"]}]}