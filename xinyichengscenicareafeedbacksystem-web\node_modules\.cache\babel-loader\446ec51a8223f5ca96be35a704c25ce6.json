{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Uweclome.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Uweclome.vue", "mtime": 1749041120264}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uL3V0aWxzL2h0dHAiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlV3ZWNsb21lIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG5hbWU6ICIiLAogICAgICBmb3JtRGF0YToge30sCiAgICAgIC8v6KGo5Y2V5pWw5o2uICAKICAgICAgdGltZTogJycgLy/lvZPliY3ml7bpl7QKICAgIH07CiAgfSwKCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMubG5hbWUgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCJsbmFtZSIpOwogICAgdGhpcy5nZXREYXRhcygpOwogICAgdGhpcy50aW1lID0gbmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygpOwogIH0sCiAgbWV0aG9kczogewogICAgLy/ojrflj5bliJfooajmlbDmja4KICAgIGdldERhdGFzKCkgewogICAgICBsZXQgcGFyYSA9IHt9OwogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3VzZXJzL2dldD9pZD0iICsgdGhpcy5sbmFtZTsKICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZm9ybURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHJlcy5yZXNkYXRhKSk7CiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["request", "base", "name", "data", "lname", "formData", "time", "created", "sessionStorage", "getItem", "getDatas", "Date", "toLocaleString", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Uweclome.vue"], "sourcesContent": ["<template>\r\n  \r\n \r\n\r\n<table  style=\"width:100%; text-align:center;line-height:28px; text-indent: 10px;margin-top: 50px;\">\r\n    <tr><td  align=\"right\" width=\"30%\">\r\n        <img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' + formData.avatar\"  style=\"width: 150px;height: 150px;border-radius: 70px;\" />\r\n    </td><td class=\"style1\" valign=\"top\" style=\"\r\n    vertical-align: middle;\r\n    padding-left: 20px;\r\n\">\r\n        <table>\r\n            <tr><td class=\"style1\">\r\n                您好：<b style=\"color:red;\">\r\n                {{lname}}\r\n            </b>\r\n            </td></tr>\r\n            <tr><td class=\"style1\">\r\n                </td></tr>\r\n            <tr><td class=\"style1\">\r\n                登录时间：{{time}}</td></tr>\r\n            <tr><td class=\"style1\">\r\n            </td></tr>\r\n        </table>\r\n    </td></tr>\r\n\r\n</table>\r\n\r\n\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"Uweclome\",\r\n  data() {\r\n    return {\r\n      lname: \"\",\r\n      formData: {}, //表单数据  \r\n      time: '', //当前时间\r\n\r\n    };\r\n  },\r\n  created() {\r\n    this.lname = sessionStorage.getItem(\"lname\");\r\n      this.getDatas();\r\n      this.time = new Date().toLocaleString();\r\n\r\n  },\r\n  methods: {  \r\n    //获取列表数据\r\ngetDatas() {\r\n    let para = {\r\n    };\r\n    this.listLoading = true;\r\n    let url = base + \"/users/get?id=\" + this.lname;\r\n    request.post(url, para).then((res) => {\r\n        this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n        this.listLoading = false;\r\n    });\r\n},\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n\r\n\r\n"], "mappings": "AAiCA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,IAAI,EAAE,EAAE,CAAE;IAEZ,CAAC;EACH,CAAC;;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACH,KAAI,GAAII,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;IAC1C,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACJ,IAAG,GAAI,IAAIK,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;EAE3C,CAAC;EACDC,OAAO,EAAE;IACP;IACJH,QAAQA,CAAA,EAAG;MACP,IAAII,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIf,IAAG,GAAI,gBAAe,GAAI,IAAI,CAACG,KAAK;MAC9CJ,OAAO,CAACiB,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAI,CAACd,QAAO,GAAIe,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;MAC5B,CAAC,CAAC;IACN;EAEE;AACF,CAAC"}]}