{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Left.vue?vue&type=template&id=3c83f0b7", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Left.vue", "mtime": 1747058115006}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIA0KDQo8ZGl2IGNsYXNzPSJjb2wteHMtMTIgY29sLXNtLTQgY29sLW1kLTMiPg0KICAgICAgIDxkaXYgY2xhc3M9ImxlZnRfY29udGFjdCI+DQogICAgPGgzIGNsYXNzPSJsZWZ0X2gzIiBzdHlsZT0ibWFyZ2luLWJvdHRvbToyMHB4OyI+PHNwYW4+5paw6Ze76LWE6K6vPC9zcGFuPjwvaDM+DQogICAgIDx1bCBjbGFzcz0icHJvZHVjdC1saXN0Ij4NCg0KICAgICAgICA8bGkgY2xhc3M9InByb2R1Y3QtaXRlbSIgIHYtZm9yPSIoaXRlbSxpbmRleCkgaW4gbGlzdDEiIDprZXk9ImluZGV4IiA+DQogICAgICAgICAgICA8YSA6aHJlZj0iJ25ld3NWaWV3P2lkPScraXRlbS5pZCI+DQogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0icHJvZHVjdC1pbWFnZS13cmFwcGVyIj4NCiAgICAgICAgICAgICAgICAgICAgPGltZyA6c3JjPSInaHR0cDovL2xvY2FsaG9zdDo4MDg4L3hpbnlpY2hlbmdzY2VuaWNhcmVhZmVlZGJhY2tzeXN0ZW0vJyAraXRlbS5uaW1hZ2UiIGNsYXNzPSJwcm9kdWN0LWltYWdlIiBzdHlsZT0id2lkdGg6IDIzMHB4O2hlaWdodDogMTQwcHg7IiAvPg0KICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InByb2R1Y3QtaW5mbyI+DQogICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJwcm9kdWN0LW5hbWUiPnt7aXRlbS50aXRsZX19PC9zcGFuPg0KICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDwvYT4NCiAgICAgICAgPC9saT4gICAgICAgDQoNCiAgICA8L3VsPg0KPC9kaXY+DQo8ZGl2IGNsYXNzPSJsZWZ0X25ld3MiPg0KICAgIDxoMyBjbGFzcz0ibGVmdF9oMyI+PHNwYW4+56Wo5Yqh5L+h5oGvPC9zcGFuPjwvaDM+DQogICAgPHVsIGNsYXNzPSJsZWZ0X25ld3MiPg0KICAgICAgICA8bGkgIHYtZm9yPSIoaXRlbSxpbmRleCkgaW4gbGlzdDIiIDprZXk9ImluZGV4IiA+PGEgOmhyZWY9Iid0aWNrZXRJbmZvVmlldz9pZD0nK2l0ZW0udGlja2V0aWQiPnt7aXRlbS50bmFtZX19PC9hPjwvbGk+ICAgICAgIA0KDQogICAgPC91bD4NCjwvZGl2Pg0KDQogPC9kaXY+DQogDQo="}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Left.vue"], "names": [], "mappings": ";;;AAGA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACpE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACjE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpJ,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEnD,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC;;IAER,CAAC,CAAC,CAAC,CAAC;AACR,CAAC,CAAC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEzH,CAAC,CAAC,CAAC,CAAC;AACR,CAAC,CAAC,CAAC,CAAC,CAAC;;CAEJ,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/components/Left.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  \r\n\r\n<div class=\"col-xs-12 col-sm-4 col-md-3\">\r\n       <div class=\"left_contact\">\r\n    <h3 class=\"left_h3\" style=\"margin-bottom:20px;\"><span>新闻资讯</span></h3>\r\n     <ul class=\"product-list\">\r\n\r\n        <li class=\"product-item\"  v-for=\"(item,index) in list1\" :key=\"index\" >\r\n            <a :href=\"'newsView?id='+item.id\">\r\n                <div class=\"product-image-wrapper\">\r\n                    <img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' +item.nimage\" class=\"product-image\" style=\"width: 230px;height: 140px;\" />\r\n                </div>\r\n                <div class=\"product-info\">\r\n                    <span class=\"product-name\">{{item.title}}</span>\r\n              \r\n                </div>\r\n            </a>\r\n        </li>       \r\n\r\n    </ul>\r\n</div>\r\n<div class=\"left_news\">\r\n    <h3 class=\"left_h3\"><span>票务信息</span></h3>\r\n    <ul class=\"left_news\">\r\n        <li  v-for=\"(item,index) in list2\" :key=\"index\" ><a :href=\"'ticketInfoView?id='+item.ticketid\">{{item.tname}}</a></li>       \r\n\r\n    </ul>\r\n</div>\r\n\r\n </div>\r\n \r\n</template>\r\n<script>\r\nimport request, { base } from \"../../utils/http\";\r\nexport default {\r\n  name: \"Left\",\r\n  data() {\r\n    return {\r\n    list1:\"\",\r\n    list2:\"\",\r\n\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getlist1();\r\n    this.getlist2();\r\n\r\n  },\r\n  methods: {\r\n\r\n    // 获取新闻资讯\r\n    getlist1() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/news/list?currentPage=1&pageSize=6\";\r\n        request.post(url, para).then((res) => {         \r\n            this.list1 = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n    // 获取票务信息\r\n    getlist2() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/ticketInfo/list?currentPage=1&pageSize=6\";\r\n        request.post(url, para).then((res) => {         \r\n            this.list2 = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n  },\r\n};\r\n</script>\r\n\r\n\r\n\r\n\r\n"]}]}