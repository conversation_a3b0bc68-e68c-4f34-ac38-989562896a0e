{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Ureg.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Ureg.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "data", "formData", "add<PERSON><PERSON>", "account", "required", "message", "trigger", "password", "password2", "validator", "rule", "value", "callback", "Error", "uname", "gender", "phone", "pattern", "email", "address", "avatar", "btnLoading", "uploadVisible", "created", "methods", "save", "$refs", "validate", "valid", "url", "post", "then", "res", "code", "$message", "type", "offset", "$router", "push", "msg", "showUpload", "hideUpload", "handleRemove", "file", "fileList", "handlePreview", "console", "log", "handleExceed", "files", "duration", "fileListChange", "extendFileName", "extendFileNames", "split", "regExpRules", "i", "length", "RegExp", "fileNames", "that", "for<PERSON>ach", "key", "val", "ret", "test", "indexOf", "multiFiles", "handleConfirm", "filePath", "FormData", "append", "raw", "furl", "resdata"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Ureg.vue"], "sourcesContent": ["<template>\r\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"账号\" prop=\"account\">\r\n<el-input v-model=\"formData.account\" placeholder=\"账号\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"密码\" prop=\"password\">\r\n<el-input type=\"password\" v-model=\"formData.password\" placeholder=\"密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"确认密码\" prop=\"password2\">\r\n<el-input type=\"password\" v-model=\"formData.password2\" placeholder=\"确认密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"姓名\" prop=\"uname\">\r\n<el-input v-model=\"formData.uname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"性别\" prop=\"gender\">\r\n<el-radio-group v-model=\"formData.gender\">\r\n<el-radio label=\"男\">\r\n男\r\n</el-radio>\r\n<el-radio label=\"女\">\r\n女\r\n</el-radio>\r\n</el-radio-group>\r\n</el-form-item>\r\n<el-form-item label=\"年龄\" prop=\"age\">\r\n<el-input v-model=\"formData.age\" placeholder=\"年龄\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"手机号码\" prop=\"phone\">\r\n<el-input v-model=\"formData.phone\" placeholder=\"手机号码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"电子邮箱\" prop=\"email\">\r\n<el-input v-model=\"formData.email\" placeholder=\"电子邮箱\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系地址\" prop=\"address\">\r\n<el-input v-model=\"formData.address\" placeholder=\"联系地址\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item prop=\"avatar\" label=\"个人头像\"  min-width=\"20%\">\r\n<el-input v-model=\"formData.avatar\" placeholder=\"个人头像\"  readonly=\"true\" style=\"width:50%;\" ></el-input>\r\n<el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\"  size=\"small\" @click=\"save\" :loading=\"btnLoading\"   icon=\"el-icon-upload\" >注 册</el-button>\r\n</el-form-item>\r\n</el-form>\r\n     <el-dialog\n        v-model=\"uploadVisible\"\n        title=\"附件上传\"\n        custom-class=\"el-dialog-widthSmall\"\n        @close=\"closeDialog\"\n      >\n        <div>\n          <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n        </div>\n        <el-upload\n          action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n          style=\"\n            margin: auto;\n            margin-top: 10px;\n            border: 1px solid #dcdfe6;\n            border-radius: 4px;\n          \"\n          drag\n          :limit=\"1\"\n          :on-preview=\"handlePreview\"\n          :on-remove=\"handleRemove\"\n          :file-list=\"fileList\"\n          :on-exceed=\"handleExceed\"\n          :auto-upload=\"false\"\n          name=\"file\"\n          :on-change=\"fileListChange\"\n        >\n          <i class=\"el-icon-upload\"></i>\n          <div class=\"el-upload__text\">\n            将数据文件拖到此处，或<em>点击上传</em>\n          </div>\n          <div class=\"el-upload__tip\">\n            <div\n              style=\"display: inline; color: #d70000; font-size: 14px\"\n              class=\"uploadFileWarning\"\n              id=\"uploadFileWarning\"\n            ></div>\n          </div>\n        </el-upload>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"hideUpload\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n        </span>\n      </el-dialog>\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"Ureg\",\r\n  data() {\r\n    return {\r\n      formData:{},\r\n\r\n      addrules: {\r\n          account: [{ required: true, message: '请输入账号', trigger: 'blur' },],\r\n          password: [{ required: true, message: '请输入密码', trigger: 'blur' }],\n          password2: [{ required: true, message: '请输入密码', trigger: 'blur' },{ validator: (rule, value, callback) => { if (value !== this.formData.password) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\r\n          uname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\r\n          gender: [{ required: true, message: '请输入性别', trigger: 'blur' },],\r\n          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },        { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },],\r\n          email: [{ required: true, message: '请输入电子邮箱', trigger: 'blur' },        { pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/, message: '电子邮箱格式不正确', trigger: 'blur' },],\r\n          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },],\r\n          avatar: [{ required: true, message: '请上传个人头像', trigger: 'blur' }],\r\n      },\r\n\r\n\r\n      btnLoading: false, //按钮是否在加载中\r\n      uploadVisible: false, //上传弹出框\r\n\r\n    };\r\n  },\r\n  created() {\r\n    \r\n  },\r\n  methods: {  \r\n    //注册\nsave() {\n    //表单验证\n    this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n            let url = base + \"/users/add\"; //请求地址\n            this.btnLoading = true; //按钮加载状态\n            request.post(url, this.formData).then((res) => { //请求接口             \n                if (res.code == 200) {\n                    this.$message({\n                        message: \"恭喜您，注册成功！\",\n                        type: \"success\",\n                        offset: 320,\n                    });\n                    this.$router.push(\"/ulogin\");\n                }\n                else if (res.code == 201) {\n                    this.$message({\n                        message: res.msg,\n                        type: \"error\",\n                        offset: 320,\n                    });\n                    this.btnLoading = false;\n                }\n                else {\n                    this.$message({\n                        message: \"服务器错误\",\n                        type: \"error\",\n                        offset: 320,\n                    });\n                    this.btnLoading = false;\n                }\n            });\n        }\n    });\n},\r\n    //显示上传框\n    showUpload() {\n      this.uploadVisible = true;\n    },\n\n    //隐藏上传框\n    hideUpload() {\n      this.uploadVisible = false;\n    },\n    //上传\n    handleRemove(file, fileList) {\n      this.fileList = fileList;\n    },\n    handlePreview(file) {\n      console.log(file);\n    },\n    handleExceed(files, fileList) {\n      this.$message({\n        duration: 1000,\n        message: \"只能上传一个文件\",\n        type: \"error\",\n        offset: 320,\n      });\n    },\n    // 判断上传文件后缀\n    fileListChange(file, fileList) {\n      let extendFileName = \"png,jpg\";\n      let extendFileNames = extendFileName.split(\",\");\n      let regExpRules = [];\n      for (let i = 0; i < extendFileNames.length; i++) {\n        regExpRules.push(\n          new RegExp(\"(.*).(\" + extendFileNames[i] + \")$\", \"gim\")\n        );\n      }\n      let fileNames = [];\n      let files = [];\n      let that = this;\n      fileList.forEach(function (key, val) {\n        let ret = false;\n        for (let i = 0; i < regExpRules.length; i++) {\n          ret = ret || regExpRules[i].test(key[\"name\"]);\n        }\n        if (!ret) {\n          console.log(key[\"name\"] + \":\" + ret);\n          that.$message({\n            duration: 1000,\n            message: \"上传的文件后缀必须为\" + extendFileName + \"格式！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        if (fileNames.indexOf(key[\"name\"]) != -1) {\n          that.$message({\n            duration: 1000,\n            message: \"上传的文件重复！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        //只能上传一个文件，用最后上传的覆盖\n        if (!that.multiFiles) {\n          files = [];\n          fileNames = [];\n        }\n        files.push(key);\n        fileNames.push(key[\"name\"]);\n        if (fileNames !== \"\") {\n          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n        }\n        //$(\".uploadFileWarning\").text(\"\");\n      });\n      this.files = fileNames;\n      this.fileList = files;\n    },\n    /**\n     * 确认按钮\n     */\n    handleConfirm() {\n      let filePath = this.fileList;\n      if (filePath.length === 0) {\n        this.$message({\n          duration: 1000,\n          message: \"请选择文件！\",\n          type: \"error\",\n          offset: 320,\n        });\n        return false;\n      }\n      let formData = new FormData();\n      this.fileList.forEach((file) => {\n        formData.append(\"file\", file.raw, file.raw.name);\n      });\n      let url = base + \"/common/uploadFile\";\n      console.log(\"url=\" + url);\n      request.post(url, formData).then((res) => {\n        console.log(res);\n        let furl = res.resdata.filePath;\n        this.formData.avatar = furl;  // 上传文件的路径\n        this.hideUpload();\n        console.log(res);\n      });\n    },\n\r\n\r\n\r\n\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n\r\n\r\n"], "mappings": ";;AA4FA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,eAAe;EACbC,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAC,CAAC,CAAC;MAEXC,QAAQ,EAAE;QACNC,OAAO,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACjEC,QAAQ,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACjEE,SAAS,EAAE,CAAC;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,EAAC;UAAEG,SAAS,EAAEA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,KAAK;YAAE,IAAID,KAAI,KAAM,IAAI,CAACV,QAAQ,CAACM,QAAQ,EAAE;cAAEK,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;YAAE,OAAO;cAAED,QAAQ,CAAC,CAAC;YAAE;UAAE,CAAC;UAAEN,OAAO,EAAE;QAAO,CAAC,CAAE;QACpOQ,KAAK,EAAE,CAAC;UAAEV,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QAC/DS,MAAM,EAAE,CAAC;UAAEX,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QAChEU,KAAK,EAAE,CAAC;UAAEZ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EAAS;UAAEW,OAAO,EAAE,mBAAmB;UAAEZ,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACjJY,KAAK,EAAE,CAAC;UAAEd,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EAAS;UAAEW,OAAO,EAAE,qDAAqD;UAAEZ,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACnLa,OAAO,EAAE,CAAC;UAAEf,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACnEc,MAAM,EAAE,CAAC;UAAEhB,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MACpE,CAAC;MAGDe,UAAU,EAAE,KAAK;MAAE;MACnBC,aAAa,EAAE,KAAK,CAAE;IAExB,CAAC;EACH,CAAC;;EACDC,OAAOA,CAAA,EAAG,CAEV,CAAC;EACDC,OAAO,EAAE;IACP;IACJC,IAAIA,CAAA,EAAG;MACH;MACA,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAE1C,IAAIA,KAAK,EAAE;UACP,IAAIC,GAAE,GAAI/B,IAAG,GAAI,YAAY,EAAE;UAC/B,IAAI,CAACuB,UAAS,GAAI,IAAI,EAAE;UACxBxB,OAAO,CAACiC,IAAI,CAACD,GAAG,EAAE,IAAI,CAAC5B,QAAQ,CAAC,CAAC8B,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC7C,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACjB,IAAI,CAACC,QAAQ,CAAC;gBACV7B,OAAO,EAAE,WAAW;gBACpB8B,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACZ,CAAC,CAAC;cACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;YAChC,OACK,IAAIN,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACtB,IAAI,CAACC,QAAQ,CAAC;gBACV7B,OAAO,EAAE2B,GAAG,CAACO,GAAG;gBAChBJ,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACZ,CAAC,CAAC;cACF,IAAI,CAACf,UAAS,GAAI,KAAK;YAC3B,OACK;cACD,IAAI,CAACa,QAAQ,CAAC;gBACV7B,OAAO,EAAE,OAAO;gBAChB8B,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACZ,CAAC,CAAC;cACF,IAAI,CAACf,UAAS,GAAI,KAAK;YAC3B;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN,CAAC;IACG;IACAmB,UAAUA,CAAA,EAAG;MACX,IAAI,CAAClB,aAAY,GAAI,IAAI;IAC3B,CAAC;IAED;IACAmB,UAAUA,CAAA,EAAG;MACX,IAAI,CAACnB,aAAY,GAAI,KAAK;IAC5B,CAAC;IACD;IACAoB,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MAC3B,IAAI,CAACA,QAAO,GAAIA,QAAQ;IAC1B,CAAC;IACDC,aAAaA,CAACF,IAAI,EAAE;MAClBG,OAAO,CAACC,GAAG,CAACJ,IAAI,CAAC;IACnB,CAAC;IACDK,YAAYA,CAACC,KAAK,EAAEL,QAAQ,EAAE;MAC5B,IAAI,CAACV,QAAQ,CAAC;QACZgB,QAAQ,EAAE,IAAI;QACd7C,OAAO,EAAE,UAAU;QACnB8B,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IACD;IACAe,cAAcA,CAACR,IAAI,EAAEC,QAAQ,EAAE;MAC7B,IAAIQ,cAAa,GAAI,SAAS;MAC9B,IAAIC,eAAc,GAAID,cAAc,CAACE,KAAK,CAAC,GAAG,CAAC;MAC/C,IAAIC,WAAU,GAAI,EAAE;MACpB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIH,eAAe,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/CD,WAAW,CAACjB,IAAI,CACd,IAAIoB,MAAM,CAAC,QAAO,GAAIL,eAAe,CAACG,CAAC,IAAI,IAAI,EAAE,KAAK,CACxD,CAAC;MACH;MACA,IAAIG,SAAQ,GAAI,EAAE;MAClB,IAAIV,KAAI,GAAI,EAAE;MACd,IAAIW,IAAG,GAAI,IAAI;MACfhB,QAAQ,CAACiB,OAAO,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;QACnC,IAAIC,GAAE,GAAI,KAAK;QACf,KAAK,IAAIR,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,WAAW,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3CQ,GAAE,GAAIA,GAAE,IAAKT,WAAW,CAACC,CAAC,CAAC,CAACS,IAAI,CAACH,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C;QACA,IAAI,CAACE,GAAG,EAAE;UACRlB,OAAO,CAACC,GAAG,CAACe,GAAG,CAAC,MAAM,IAAI,GAAE,GAAIE,GAAG,CAAC;UACpCJ,IAAI,CAAC1B,QAAQ,CAAC;YACZgB,QAAQ,EAAE,IAAI;YACd7C,OAAO,EAAE,YAAW,GAAI+C,cAAa,GAAI,KAAK;YAC9CjB,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA,IAAIuB,SAAS,CAACO,OAAO,CAACJ,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;UACxCF,IAAI,CAAC1B,QAAQ,CAAC;YACZgB,QAAQ,EAAE,IAAI;YACd7C,OAAO,EAAE,UAAU;YACnB8B,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA;QACA,IAAI,CAACwB,IAAI,CAACO,UAAU,EAAE;UACpBlB,KAAI,GAAI,EAAE;UACVU,SAAQ,GAAI,EAAE;QAChB;QACAV,KAAK,CAACX,IAAI,CAACwB,GAAG,CAAC;QACfH,SAAS,CAACrB,IAAI,CAACwB,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAIH,SAAQ,KAAM,EAAE,EAAE;UACpB;QAAA;QAEF;MACF,CAAC,CAAC;;MACF,IAAI,CAACV,KAAI,GAAIU,SAAS;MACtB,IAAI,CAACf,QAAO,GAAIK,KAAK;IACvB,CAAC;IACD;;;IAGAmB,aAAaA,CAAA,EAAG;MACd,IAAIC,QAAO,GAAI,IAAI,CAACzB,QAAQ;MAC5B,IAAIyB,QAAQ,CAACZ,MAAK,KAAM,CAAC,EAAE;QACzB,IAAI,CAACvB,QAAQ,CAAC;UACZgB,QAAQ,EAAE,IAAI;UACd7C,OAAO,EAAE,QAAQ;UACjB8B,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;QACF,OAAO,KAAK;MACd;MACA,IAAInC,QAAO,GAAI,IAAIqE,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAC1B,QAAQ,CAACiB,OAAO,CAAElB,IAAI,IAAK;QAC9B1C,QAAQ,CAACsE,MAAM,CAAC,MAAM,EAAE5B,IAAI,CAAC6B,GAAG,EAAE7B,IAAI,CAAC6B,GAAG,CAACzE,IAAI,CAAC;MAClD,CAAC,CAAC;MACF,IAAI8B,GAAE,GAAI/B,IAAG,GAAI,oBAAoB;MACrCgD,OAAO,CAACC,GAAG,CAAC,MAAK,GAAIlB,GAAG,CAAC;MACzBhC,OAAO,CAACiC,IAAI,CAACD,GAAG,EAAE5B,QAAQ,CAAC,CAAC8B,IAAI,CAAEC,GAAG,IAAK;QACxCc,OAAO,CAACC,GAAG,CAACf,GAAG,CAAC;QAChB,IAAIyC,IAAG,GAAIzC,GAAG,CAAC0C,OAAO,CAACL,QAAQ;QAC/B,IAAI,CAACpE,QAAQ,CAACmB,MAAK,GAAIqD,IAAI,EAAG;QAC9B,IAAI,CAAChC,UAAU,CAAC,CAAC;QACjBK,OAAO,CAACC,GAAG,CAACf,GAAG,CAAC;MAClB,CAAC,CAAC;IACJ;EAMF;AACF,CAAC"}]}