{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\TicketInfoList.vue?vue&type=style&index=0&id=2a8a97ed&lang=css", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\TicketInfoList.vue", "mtime": 1747063086545}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749040033032}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749040034646}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749040033671}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogLm5ld3MtY29udGFpbmVyMSB7ICANCiAgICAgICAgbWF4LXdpZHRoOiAxMDAlOyAgDQogICAgICAgIG1hcmdpbjogMCBhdXRvOyAgDQogICAgfSAgDQogICAgLm5ld3MtaXRlbTEgeyAgDQogICAgICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlOyAgDQogICAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7ICANCiAgICAgICAgcGFkZGluZzogMTVweDsgIA0KICAgICANCiAgICAgICAgYm94LXNoYWRvdzogMCAycHggNXB4IHJnYmEoMCwwLDAsMC4xKTsgIA0KICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlOyAgDQogICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjYzNjMGMwOw0KICAgIH0gIA0KICAgIC5uZXdzLWl0ZW0xOmhvdmVyIHsgIA0KICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7ICANCiAgICB9ICANCg0KICAgIC5uZXdzLXRpdGxlMSB7ICANCiAgICAgICAgZm9udC1zaXplOiAxNXB4OyAgDQogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOyAgDQogICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7ICANCiAgICB9ICANCiAgICAubmV3cy1pdGVtMSBhIHsgIA0KICAgICAgICBjb2xvcjogIzMzMzsgIA0KICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7ICANCiAgICB9ICANCiAgICAubmV3cy1pdGVtMSBhOmhvdmVyIHsgIA0KICAgICAgICBjb2xvcjogIzAwN2JmZjsgIA0KICAgICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTsgIA0KICAgIH0gIA0KICAgIC5uZXdzLWNvbnRlbnQxIHsgIA0KICAgICAgICBmb250LXNpemU6IDE0cHg7ICANCiAgICAgICAgY29sb3I6ICMzMzM7ICANCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsgIA0KICAgIH0gIA0KICAgIC5uZXdzLWRhdGUxIHsgIA0KICAgICAgICBmb250LXNpemU6IDEycHg7ICANCiAgICAgICAgY29sb3I6IHJlZDsgIA0KICAgIH0gIA0K"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\TicketInfoList.vue"], "names": [], "mappings": ";CAqEC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/TicketInfoList.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n   <div class=\"news-container1\">  \r\n  \r\n        <div class=\"news-item1\" v-for=\"item in tilist\" :key=\"item.ticketid\">  \r\n            <a :href=\"'ticketInfoView?id='+item.ticketid\">  <div class=\"news-title1\">{{item.tname}}</div>  \r\n            <div class=\"news-content1\">{{item.description.length > 70 ? item.description.substring(0,70):item.description}}</div>  \r\n            <div class=\"news-date1\">发布日期：{{item.addtime}}</div>  \r\n        </a>\r\n        </div>  \r\n  \r\n    </div> \r\n<el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\nbackground layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\r\nstyle=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"TicketInfoList\",\r\n  data() {\r\n    return {\r\n          page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 10, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n    },\r\n    tilist: \"\",\r\n \r\n    };\r\n  },\r\n  created() {\r\n    \r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {  \r\n    \r\n    // 分页\r\n    handleCurrentChange(val) {\r\n        this.page.currentPage = val;\r\n        this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n        let para = {\r\n                status:'上架',\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/ticketInfo/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\r\n        request.post(url, para).then((res) => {\r\n            if (res.resdata.length > 0) {\r\n                this.isPage = true;\r\n            } else {\r\n                this.isPage = false;\r\n            }\r\n            this.page.totalCount = res.count;\r\n            this.tilist = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n .news-container1 {  \r\n        max-width: 100%;  \r\n        margin: 0 auto;  \r\n    }  \r\n    .news-item1 {  \r\n        background-color: white;  \r\n        margin-bottom: 20px;  \r\n        padding: 15px;  \r\n     \r\n        box-shadow: 0 2px 5px rgba(0,0,0,0.1);  \r\n        transition: transform 0.3s ease;  \r\n        border-bottom: 1px solid #c3c0c0;\r\n    }  \r\n    .news-item1:hover {  \r\n        transform: translateY(-5px);  \r\n    }  \r\n\r\n    .news-title1 {  \r\n        font-size: 15px;  \r\n        font-weight: bold;  \r\n        margin-bottom: 10px;  \r\n    }  \r\n    .news-item1 a {  \r\n        color: #333;  \r\n        text-decoration: none;  \r\n    }  \r\n    .news-item1 a:hover {  \r\n        color: #007bff;  \r\n        text-decoration: underline;  \r\n    }  \r\n    .news-content1 {  \r\n        font-size: 14px;  \r\n        color: #333;  \r\n        margin-bottom: 10px;  \r\n    }  \r\n    .news-date1 {  \r\n        font-size: 12px;  \r\n        color: red;  \r\n    }  \r\n</style>\r\n\r\n\r\n\r\n"]}]}