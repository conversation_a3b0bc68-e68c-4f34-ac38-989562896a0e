{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\NewsList.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\NewsList.vue", "mtime": 1747229072564}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAiLi4vLi4vLi4vdXRpbHMvaHR0cCI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJOZXdzTGlzdCIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgICAgICBwYWdlOiB7DQogICAgICAgIGN1cnJlbnRQYWdlOiAxLCAvLyDlvZPliY3pobUNCiAgICAgICAgcGFnZVNpemU6IDEyLCAvLyDmr4/pobXmmL7npLrmnaHnm67kuKrmlbANCiAgICAgICAgdG90YWxDb3VudDogMCwgLy8g5oC75p2h55uu5pWwDQogICAgfSwNCiAgICBuZWxpc3Q6ICIiLA0KIA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgDQogICAgdGhpcy5nZXREYXRhcygpOw0KDQogIH0sDQogIG1ldGhvZHM6IHsgIA0KICAgIA0KICAgIC8vIOWIhumhtQ0KICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7DQogICAgICAgIHRoaXMucGFnZS5jdXJyZW50UGFnZSA9IHZhbDsNCiAgICAgICAgdGhpcy5nZXREYXRhcygpOw0KICAgIH0sDQoNCiAgICAvL+iOt+WPluWIl+ihqOaVsOaNrg0KICAgIGdldERhdGFzKCkgew0KICAgICAgICB2YXIga2V5cyA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmtleXM7DQogICAgICAgIHZhciBzcWw9IiI7DQogICAgICAgIGlmKGtleXMpew0KICAgICAgICAgICAgc3FsPSIgYW5kIHRpdGxlIGxpa2UgJyUiK2tleXMrIiUnIjsNCiAgICAgICAgfQ0KICAgICAgICBsZXQgcGFyYSA9IHsNCiAgICAgICAgICAgIGNvbmRpdGlvbjpzcWwNCiAgICAgICAgfTsNCiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7DQogICAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9uZXdzL2xpc3Q/Y3VycmVudFBhZ2U9IiArIHRoaXMucGFnZS5jdXJyZW50UGFnZSsgIiZwYWdlU2l6ZT0iICsgdGhpcy5wYWdlLnBhZ2VTaXplOw0KICAgICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMucmVzZGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgdGhpcy5pc1BhZ2UgPSB0cnVlOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICB0aGlzLmlzUGFnZSA9IGZhbHNlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5wYWdlLnRvdGFsQ291bnQgPSByZXMuY291bnQ7DQogICAgICAgICAgICB0aGlzLm5lbGlzdCA9IHJlcy5yZXNkYXRhOw0KICAgICAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9KTsNCiAgICB9LCAgICANCg0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\NewsList.vue"], "names": [], "mappings": ";AAuBA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAEV,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEP,CAAC,EAAE,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC;QACA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;gBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YACtB,EAAE,CAAC,CAAC,CAAC,EAAE;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB;YACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC;IACN,CAAC;;EAEH,CAAC;AACH,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/NewsList.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n   <div class=\"container222\">\r\n   \r\n    <div class=\"card222\" v-for=\"item in nelist\" :key=\"item.id\">  \r\n        <img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' + item.nimage\" style=\"height: 230px;\">  \r\n        <div class=\"card-content222\">  \r\n            <h2><a :href=\"'newsView?id='+item.id\">{{item.title}}</a></h2>  \r\n            <div class=\"card-footer222\">  \r\n                <span>发布时间：{{item.publishtime}}</span>           \r\n            </div>            \r\n            <p>{{item.content.length > 70 ? item.content.substring(0,70):item.content}}</p>  \r\n            <a :href=\"'newsView?id='+item.id\" class=\"read-more222\">查看更多</a>  \r\n        </div>  \r\n    </div>  \r\n\r\n</div> \r\n<el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\nbackground layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\r\nstyle=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"NewsList\",\r\n  data() {\r\n    return {\r\n          page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 12, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n    },\r\n    nelist: \"\",\r\n \r\n    };\r\n  },\r\n  created() {\r\n    \r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {  \r\n    \r\n    // 分页\r\n    handleCurrentChange(val) {\r\n        this.page.currentPage = val;\r\n        this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n        var keys = this.$route.query.keys;\r\n        var sql=\"\";\r\n        if(keys){\r\n            sql=\" and title like '%\"+keys+\"%'\";\r\n        }\r\n        let para = {\r\n            condition:sql\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/news/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\r\n        request.post(url, para).then((res) => {\r\n            if (res.resdata.length > 0) {\r\n                this.isPage = true;\r\n            } else {\r\n                this.isPage = false;\r\n            }\r\n            this.page.totalCount = res.count;\r\n            this.nelist = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n\r\n    .container222 {\r\n        display: grid;\r\n        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n        gap: 20px;\r\n        width: 100%;\r\n    }\r\n    .card222 {  \r\n        background-color: white;  \r\n        border-radius: 10px;  \r\n        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);  \r\n        overflow: hidden;  \r\n        transition: transform 0.3s, box-shadow 0.3s;  \r\n    } \r\n    .card222 a{\r\n        text-decoration: none;\r\n    }\r\n    .card222:hover {  \r\n        transform: translateY(-5px);  \r\n        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);  \r\n    }  \r\n    .card222 img {  \r\n        width: 100%;  \r\n        height: 200px;  \r\n        object-fit: cover;  \r\n    }  \r\n    .card-content222 {  \r\n        padding: 15px;  \r\n    }  \r\n    .card-content222 h2 {  \r\n        font-size: 18px;  \r\n        margin: 10px 0;  \r\n    }  \r\n    .card-content222 p {  \r\n        color: #555;  \r\n        font-size: 14px;  \r\n        margin: 10px 0;  \r\n    }  \r\n    .card-footer222 {  \r\n        display: flex;  \r\n        justify-content: space-between;  \r\n        font-size: 12px;  \r\n        color: #888;  \r\n    }  \r\n    .read-more222 {  \r\n        color: #007BFF;  \r\n        text-decoration: none;  \r\n        font-weight: bold;  \r\n        font-size: 12px;\r\n    }  \r\n    .read-more222:hover {  \r\n        text-decoration: underline;  \r\n    }  \r\n\r\n\r\n\r\n</style>\r\n\r\n\r\n\r\n"]}]}