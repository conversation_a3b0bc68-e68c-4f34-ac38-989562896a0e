{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue", "mtime": 1749041655057}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue"], "names": [], "mappings": ";AAqEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;QACF,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,EAAE,CAAC,CAAC,CAAC;MACR,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC;QACJ;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAElB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;MAEjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;gBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,EAAE,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;kBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC3C,CAAC,CAAC;gBACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;kBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;kBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,EAAE,CAAC,CAAC,CAAC,EAAE;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B;YACF;UACF,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B;UACF,CAAC,CAAC;MACN,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/Test.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"questionnaire-container\">\r\n    <h2 class=\"questionnaire-title\">调查问卷</h2>\r\n\r\n    <!-- 未登录提示 -->\r\n    <div v-if=\"!isLoggedIn\" class=\"login-prompt\">\r\n      <el-alert\r\n        title=\"请先登录\"\r\n        description=\"您需要登录后才能参与问卷调查\"\r\n        type=\"warning\"\r\n        show-icon\r\n        :closable=\"false\">\r\n      </el-alert>\r\n      <div class=\"login-actions\">\r\n        <el-button type=\"primary\" @click=\"goToLogin\">去登录</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 已完成问卷提示 -->\r\n    <div v-else-if=\"hasCompleted\" class=\"completed-prompt\">\r\n      <el-alert\r\n        title=\"您已完成问卷调查\"\r\n        description=\"感谢您的参与！您可以查看统计结果或重新填写问卷\"\r\n        type=\"success\"\r\n        show-icon\r\n        :closable=\"false\">\r\n      </el-alert>\r\n      <div class=\"completed-actions\">\r\n        <el-button type=\"primary\" @click=\"viewStatistics\">查看统计结果</el-button>\r\n        <el-button type=\"warning\" @click=\"retakeQuestionnaire\">重新填写问卷</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 问卷表单 -->\r\n    <div v-else-if=\"showQuestionnaire\">\r\n      <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n\r\n      <div v-else>\r\n        <div v-for=\"(question, index) in questionsList\" :key=\"question.qid\" class=\"question-item\">\r\n          <div class=\"question-text\">{{ index + 1 }}. {{ question.question }}</div>\r\n          <div class=\"options\">\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"非常满意\" /> 非常满意\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"满意\" /> 满意\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"一般\" /> 一般\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"不满意\" /> 不满意\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"submit-container\">\r\n          <button @click=\"submitQuestionnaire\" class=\"submit-btn\">提交问卷</button>\r\n        </div>\r\n\r\n        <div v-if=\"submitMessage\" class=\"message\" :class=\"{ 'success': submitSuccess, 'error': !submitSuccess }\">\r\n          {{ submitMessage }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"QuestionnaireView\",\r\n  data() {\r\n    return {\r\n      questionsList: [],\r\n      answers: {},\r\n      loading: true,\r\n      submitMessage: '',\r\n      submitSuccess: false,\r\n      isLoggedIn: false,\r\n      hasCompleted: false,\r\n      showQuestionnaire: false,\r\n      userAccount: ''\r\n    };\r\n  },\r\n  created() {\r\n    this.checkLoginStatus();\r\n  },\r\n  methods: {\r\n    // 检查登录状态\r\n    checkLoginStatus() {\r\n      this.userAccount = sessionStorage.getItem(\"lname\");\r\n      this.isLoggedIn = !!this.userAccount;\r\n\r\n      if (this.isLoggedIn) {\r\n        this.checkCompletionStatus();\r\n      }\r\n    },\r\n\r\n    // 检查用户是否已完成问卷\r\n    checkCompletionStatus() {\r\n      let url = base + \"/results/checkCompleted?account=\" + this.userAccount;\r\n\r\n      request.post(url, {}).then((res) => {\r\n        if (res.code === 200) {\r\n          this.hasCompleted = res.resdata;\r\n          if (!this.hasCompleted) {\r\n            this.showQuestionnaire = true;\r\n            this.getQuestions();\r\n          }\r\n        } else {\r\n          // 检查失败，默认显示问卷\r\n          this.showQuestionnaire = true;\r\n          this.getQuestions();\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"检查完成状态失败:\", error);\r\n        // 检查失败，默认显示问卷\r\n        this.showQuestionnaire = true;\r\n        this.getQuestions();\r\n      });\r\n    },\r\n\r\n    // 去登录\r\n    goToLogin() {\r\n      this.$router.push('/ulogin');\r\n    },\r\n\r\n    // 查看统计结果\r\n    viewStatistics() {\r\n      // 创建一个新的路由来显示统计结果\r\n      this.$router.push('/questionnaireStatistics');\r\n    },\r\n\r\n    // 重新填写问卷\r\n    retakeQuestionnaire() {\r\n      this.$confirm('确定要重新填写问卷吗？这将清除您之前的答案。', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.hasCompleted = false;\r\n        this.showQuestionnaire = true;\r\n        this.getQuestions();\r\n      }).catch(() => {\r\n        // 用户取消\r\n      });\r\n    },\r\n\r\n    // 获取问卷问题列表\r\n    getQuestions() {\r\n      this.loading = true;\r\n      let url = base + \"/questions/list\";\r\n      let para = {};\r\n      \r\n      request.post(url, para, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\r\n        if (res.resdata) {\r\n          this.questionsList = res.resdata;\r\n          // 初始化答案对象\r\n          this.questionsList.forEach(question => {\r\n            this.$set(this.answers, question.qid, '');\r\n          });\r\n        }\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error(\"获取问卷问题失败:\", error);\r\n        this.loading = false;\r\n      });\r\n    },\r\n    \r\n    // 提交问卷\r\n    submitQuestionnaire() {\r\n      // 检查是否所有问题都已回答\r\n      const unansweredQuestions = this.questionsList.filter(q => !this.answers[q.qid]);\r\n      \r\n      if (unansweredQuestions.length > 0) {\r\n        this.submitMessage = `请回答所有问题后再提交`;\r\n        this.submitSuccess = false;\r\n        return;\r\n      }\r\n      \r\n      // 准备提交数据\r\n      const results = [];\r\n      \r\n      // 将每个问题的答案转换为提交格式\r\n      this.questionsList.forEach(question => {\r\n        results.push({\r\n          qid: question.qid,\r\n          question: question.question,\r\n          status: this.answers[question.qid],\r\n          account: sessionStorage.getItem(\"lname\"),\r\n        });\r\n      });\r\n      \r\n      // 显示提交中状态\r\n      this.submitMessage = \"正在提交...\";\r\n      this.submitSuccess = true;\r\n      \r\n      // 一次性提交所有答案\r\n      let successCount = 0;\r\n      let failCount = 0;\r\n      \r\n      // 逐个提交答案并检查每个请求的响应\r\n      results.forEach(result => {\r\n        request.post(base + \"/results/add\", result)\r\n          .then(res => {\r\n            successCount++;\r\n            // 如果所有请求都已完成\r\n            if (successCount + failCount === results.length) {\r\n              if (failCount === 0) {\r\n                this.submitMessage = \"问卷提交成功，感谢您的反馈！\";\r\n                this.submitSuccess = true;\r\n                // 清空答案\r\n                this.answers = {};\r\n                this.questionsList.forEach(question => {\r\n                  this.$set(this.answers, question.qid, '');\r\n                });\r\n                // 更新完成状态\r\n                setTimeout(() => {\r\n                  this.hasCompleted = true;\r\n                  this.showQuestionnaire = false;\r\n                }, 2000);\r\n              } else {\r\n                this.submitMessage = `部分提交成功，${failCount}个问题提交失败`;\r\n                this.submitSuccess = false;\r\n              }\r\n            }\r\n          })\r\n          .catch(error => {\r\n            failCount++;\r\n            console.error(\"提交问题失败:\", error);\r\n            if (successCount + failCount === results.length) {\r\n              this.submitMessage = `部分提交成功，${failCount}个问题提交失败`;\r\n              this.submitSuccess = false;\r\n            }\r\n          });\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.questionnaire-container {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.questionnaire-title {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  font-size: 24px;\r\n}\r\n\r\n.loading {\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 16px;\r\n}\r\n\r\n.question-item {\r\n  margin-bottom: 25px;\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.question-text {\r\n  font-size: 16px;\r\n  margin-bottom: 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-left: 20px;\r\n}\r\n\r\n.option {\r\n  margin-right: 30px;\r\n  margin-bottom: 10px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.option input {\r\n  margin-right: 5px;\r\n}\r\n\r\n.submit-container {\r\n  text-align: center;\r\n  margin-top: 30px;\r\n}\r\n\r\n.submit-btn {\r\n  padding: 10px 30px;\r\n  background-color: #409EFF;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n}\r\n\r\n.submit-btn:hover {\r\n  background-color: #66b1ff;\r\n}\r\n\r\n.message {\r\n  text-align: center;\r\n  margin-top: 20px;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.success {\r\n  background-color: #f0f9eb;\r\n  color: #67c23a;\r\n  border: 1px solid #c2e7b0;\r\n}\r\n\r\n.error {\r\n  background-color: #fef0f0;\r\n  color: #f56c6c;\r\n  border: 1px solid #fbc4c4;\r\n}\r\n\r\n.login-prompt, .completed-prompt {\r\n  text-align: center;\r\n  padding: 30px;\r\n}\r\n\r\n.login-actions, .completed-actions {\r\n  margin-top: 20px;\r\n}\r\n\r\n.login-actions .el-button, .completed-actions .el-button {\r\n  margin: 0 10px;\r\n}\r\n</style>\r\n\r\n\r\n\r\n"]}]}