{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\Main.vue?vue&type=template&id=c1f1971a&scoped=true", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\Main.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgY3JlYXRlVk5vZGUgYXMgX2NyZWF0ZVZOb2RlLCBjcmVhdGVDb21tZW50Vk5vZGUgYXMgX2NyZWF0ZUNvbW1lbnRWTm9kZSwgY3JlYXRlRWxlbWVudFZOb2RlIGFzIF9jcmVhdGVFbGVtZW50Vk5vZGUsIHdpdGhDdHggYXMgX3dpdGhDdHgsIG9wZW5CbG9jayBhcyBfb3BlbkJsb2NrLCBjcmVhdGVCbG9jayBhcyBfY3JlYXRlQmxvY2ssIHB1c2hTY29wZUlkIGFzIF9wdXNoU2NvcGVJZCwgcG9wU2NvcGVJZCBhcyBfcG9wU2NvcGVJZCB9IGZyb20gInZ1ZSI7CmNvbnN0IF93aXRoU2NvcGVJZCA9IG4gPT4gKF9wdXNoU2NvcGVJZCgiZGF0YS12LWMxZjE5NzFhIiksIG4gPSBuKCksIF9wb3BTY29wZUlkKCksIG4pOwpjb25zdCBfaG9pc3RlZF8xID0gewogIGlkOiAid3JhcHBlciIKfTsKY29uc3QgX2hvaXN0ZWRfMiA9IHsKICBjbGFzczogImNvbnRlbnQtcGFnZSIKfTsKY29uc3QgX2hvaXN0ZWRfMyA9IHsKICBjbGFzczogImNvbnRlbnQiCn07CmNvbnN0IF9ob2lzdGVkXzQgPSB7CiAgY2xhc3M6ICJjb250YWluZXItZmx1aWQiCn07CmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgY29uc3QgX2NvbXBvbmVudF9IZWFkZXIgPSBfcmVzb2x2ZUNvbXBvbmVudCgiSGVhZGVyIik7CiAgY29uc3QgX2NvbXBvbmVudF9MZWZ0TWVudSA9IF9yZXNvbHZlQ29tcG9uZW50KCJMZWZ0TWVudSIpOwogIGNvbnN0IF9jb21wb25lbnRfcm91dGVyX3ZpZXcgPSBfcmVzb2x2ZUNvbXBvbmVudCgicm91dGVyLXZpZXciKTsKICBjb25zdCBfY29tcG9uZW50X2VsX2NvbmZpZ19wcm92aWRlciA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1jb25maWctcHJvdmlkZXIiKTsKICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlQmxvY2soX2NvbXBvbmVudF9lbF9jb25maWdfcHJvdmlkZXIsIHsKICAgIGxvY2FsZTogJGRhdGEubG9jYWxlCiAgfSwgewogICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzEsIFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9IZWFkZXIpLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9MZWZ0TWVudSksIF9jcmVhdGVDb21tZW50Vk5vZGUoIiBQYWdlIENvbnRlbnQgU3RhcnQgIiksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzIsIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF8zLCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfNCwgW19jcmVhdGVWTm9kZShfY29tcG9uZW50X3JvdXRlcl92aWV3KV0pXSldKSwgX2NyZWF0ZUNvbW1lbnRWTm9kZSgiIEVuZCBQYWdlIENvbnRlbnQiKV0pXSksCiAgICBfOiAxIC8qIFNUQUJMRSAqLwogIH0sIDggLyogUFJPUFMgKi8sIFsibG9jYWxlIl0pOwp9"}, {"version": 3, "names": ["id", "class", "_createBlock", "_component_el_config_provider", "locale", "$data", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_Header", "_component_LeftMenu", "_createCommentVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_component_router_view"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\Main.vue"], "sourcesContent": ["<template>\r\n\r\n<el-config-provider :locale=\"locale\">\r\n \r\n    <div id=\"wrapper\">\r\n\r\n <Header />\r\n\r\n<LeftMenu />\r\n\r\n<!-- Page Content Start -->\r\n<div class=\"content-page\">\r\n    <div class=\"content\">\r\n        <div class=\"container-fluid\">\r\n            <router-view />        \r\n\r\n        </div>\r\n    </div>\r\n</div>\r\n<!-- End Page Content-->\r\n\r\n\r\n\r\n</div>\r\n\r\n\r\n\r\n</el-config-provider>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"../components/Header\";\r\nimport LeftMenu from \"../components/LeftMenu\";\r\nimport { ElConfigProvider } from \"element-plus\";\r\nimport zhCn from \"element-plus/lib/locale/lang/zh-cn\";\r\n\r\nexport default {\r\n  name: \"MainLayout\",\r\n  components: {\r\n    Header,\r\n    LeftMenu,\r\n    [ElConfigProvider.name]: ElConfigProvider,\r\n  },\r\n  data() {\r\n    return {\r\n      locale: zhCn,\r\n    };\r\n  },\r\n  mounted() {\r\n\r\n\r\n\r\n\r\n  },\r\n\r\n  methods: {\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n\r\n<style scoped>\r\n@import url(../assets/libs/@mdi/font/css/materialdesignicons.min.css);\r\n@import url(../assets/libs/dripicons/webfont/webfont.css);\r\n@import url(../assets/css/app.css);\r\n</style>\r\n\r\n"], "mappings": ";;;EAISA,EAAE,EAAC;AAAS;;EAOhBC,KAAK,EAAC;AAAc;;EAChBA,KAAK,EAAC;AAAS;;EACXA,KAAK,EAAC;AAAiB;;;;;;uBAXpCC,YAAA,CAyBqBC,6BAAA;IAzBAC,MAAM,EAAEC,KAAA,CAAAD;EAAM;sBAE/B,MAmBE,CAnBFE,mBAAA,CAmBE,OAnBFC,UAmBE,GAjBLC,YAAA,CAAUC,iBAAA,GAEXD,YAAA,CAAYE,mBAAA,GAEZC,mBAAA,wBAA2B,EAC3BL,mBAAA,CAOM,OAPNM,UAOM,GANFN,mBAAA,CAKM,OALNO,UAKM,GAJFP,mBAAA,CAGM,OAHNQ,UAGM,GAFFN,YAAA,CAAeO,sBAAA,E,OAK3BJ,mBAAA,qBAAwB,C"}]}