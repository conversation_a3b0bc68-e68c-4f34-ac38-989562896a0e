{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionsStatistics.vue?vue&type=template&id=7c7ec2f8&scoped=true", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionsStatistics.vue", "mtime": 1749041539479}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "href", "id", "style", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "$route", "meta", "title", "_hoisted_5", "_hoisted_6", "$data", "loading", "_createElementBlock", "_hoisted_7", "_createVNode", "_component_el_loading", "text", "_hoisted_8", "statisticsData", "length", "_hoisted_9", "_component_el_empty", "description", "_hoisted_10", "_Fragment", "_renderList", "question", "index", "key", "qid", "_hoisted_11", "_normalizeStyle", "chartStyle"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionsStatistics.vue"], "sourcesContent": ["<template>\n  <div class=\"page-title-box\">\n    <ol class=\"breadcrumb float-right\">\n      <li class=\"breadcrumb-item\"><a href=\"javascript:void(0);\" id=\"title1\">调查问卷管理</a></li>\n      <li class=\"breadcrumb-item active\" id=\"title2\">{{ this.$route.meta.title }}</li>\n    </ol>\n    <h4 class=\"page-title\" id=\"title3\">{{ this.$route.meta.title }}</h4>\n  </div>\n\n  <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n    <div v-if=\"loading\" class=\"loading\">\n      <el-loading text=\"正在加载统计数据...\"></el-loading>\n    </div>\n    \n    <div v-else>\n      <div v-if=\"statisticsData.length === 0\" class=\"no-data\">\n        <el-empty description=\"暂无问卷统计数据\"></el-empty>\n      </div>\n      \n      <div v-else>\n        <div v-for=\"(question, index) in statisticsData\" :key=\"question.qid\" class=\"chart-container\">\n          <h3 class=\"question-title\">{{ index + 1 }}. {{ question.question }}</h3>\n          <div :id=\"'chart-' + question.qid\" class=\"chart\" :style=\"chartStyle\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nimport * as echarts from \"echarts\";\n\nexport default {\n  name: 'QuestionsStatistics',\n  data() {\n    return {\n      loading: true,\n      statisticsData: [],\n      chartStyle: { \n        width: \"100%\", \n        height: \"400px\",\n        marginBottom: \"30px\"\n      },\n      charts: [] // 存储图表实例\n    };\n  },\n  \n  mounted() {\n    this.getStatisticsData();\n  },\n  \n  beforeDestroy() {\n    // 销毁所有图表实例\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.dispose();\n      }\n    });\n  },\n  \n  methods: {\n    // 获取统计数据\n    getStatisticsData() {\n      this.loading = true;\n      let url = base + \"/results/statistics\";\n      \n      request.post(url, {}).then((res) => {\n        if (res.code === 200 && res.resdata) {\n          this.statisticsData = res.resdata;\n          this.$nextTick(() => {\n            this.initAllCharts();\n          });\n        } else {\n          this.$message({\n            message: \"获取统计数据失败\",\n            type: \"error\",\n            offset: 320,\n          });\n        }\n        this.loading = false;\n      }).catch(error => {\n        console.error(\"获取统计数据失败:\", error);\n        this.$message({\n          message: \"获取统计数据失败\",\n          type: \"error\",\n          offset: 320,\n        });\n        this.loading = false;\n      });\n    },\n    \n    // 初始化所有图表\n    initAllCharts() {\n      this.statisticsData.forEach((question, index) => {\n        this.initChart(question, index);\n      });\n    },\n    \n    // 初始化单个图表\n    initChart(questionData, index) {\n      const chartId = 'chart-' + questionData.qid;\n      const chartDom = document.getElementById(chartId);\n      \n      if (!chartDom) {\n        console.error(`Chart container not found: ${chartId}`);\n        return;\n      }\n      \n      const chart = echarts.init(chartDom);\n      this.charts.push(chart);\n      \n      // 准备饼图数据\n      const pieData = questionData.options || [];\n      const legendData = pieData.map(item => item.name);\n      \n      const option = {\n        title: {\n          text: `问题 ${index + 1} 统计`,\n          left: 'center',\n          top: 20,\n          textStyle: {\n            fontSize: 16,\n            fontWeight: 'bold'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 'left',\n          top: 'middle',\n          data: legendData\n        },\n        series: [\n          {\n            name: '选项统计',\n            type: 'pie',\n            radius: ['40%', '70%'],\n            center: ['60%', '50%'],\n            avoidLabelOverlap: false,\n            label: {\n              show: true,\n              position: 'outside',\n              formatter: '{b}: {c} ({d}%)'\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: '16',\n                fontWeight: 'bold'\n              }\n            },\n            labelLine: {\n              show: true\n            },\n            data: pieData\n          }\n        ]\n      };\n      \n      chart.setOption(option);\n      \n      // 响应式调整\n      window.addEventListener('resize', () => {\n        chart.resize();\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.loading {\n  text-align: center;\n  padding: 50px;\n}\n\n.no-data {\n  text-align: center;\n  padding: 50px;\n}\n\n.chart-container {\n  margin-bottom: 50px;\n  padding: 20px;\n  border: 1px solid #e6e6e6;\n  border-radius: 8px;\n  background-color: #fff;\n}\n\n.question-title {\n  margin-bottom: 20px;\n  color: #333;\n  font-size: 18px;\n  font-weight: bold;\n  text-align: center;\n}\n\n.chart {\n  margin: 0 auto;\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAgB;;EACrBA,KAAK,EAAC;AAAwB;gEAChCC,mBAAA,CAAqF;EAAjFD,KAAK,EAAC;AAAiB,I,aAACC,mBAAA,CAAoD;EAAjDC,IAAI,EAAC,qBAAqB;EAACC,EAAE,EAAC;GAAS,QAAM,E;;EACxEH,KAAK,EAAC,wBAAwB;EAACG,EAAE,EAAC;;;EAEpCH,KAAK,EAAC,YAAY;EAACG,EAAE,EAAC;;;EAGvBC,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;;;EACtCJ,KAAK,EAAC;;;;;;;EAKgBA,KAAK,EAAC;;;;;;EAMtCA,KAAK,EAAC;AAAgB;;;;;6DApBlCC,mBAAA,CAMM,OANNI,UAMM,GALJJ,mBAAA,CAGK,MAHLK,UAGK,GAFHC,UAAqF,EACrFN,mBAAA,CAAgF,MAAhFO,UAAgF,EAAAC,gBAAA,MAAzBC,MAAM,CAACC,IAAI,CAACC,KAAK,iB,GAE1EX,mBAAA,CAAoE,MAApEY,UAAoE,EAAAJ,gBAAA,MAAzBC,MAAM,CAACC,IAAI,CAACC,KAAK,iB,GAG9DX,mBAAA,CAiBM,OAjBNa,UAiBM,GAhBOC,KAAA,CAAAC,OAAO,I,cAAlBC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAA4CC,qBAAA;IAAhCC,IAAI,EAAC;EAAa,G,oBAGhCJ,mBAAA,CAWM,OAAAK,UAAA,GAVOP,KAAA,CAAAQ,cAAc,CAACC,MAAM,U,cAAhCP,mBAAA,CAEM,OAFNQ,UAEM,GADJN,YAAA,CAA4CO,mBAAA;IAAlCC,WAAW,EAAC;EAAU,G,oBAGlCV,mBAAA,CAKM,OAAAW,WAAA,I,kBAJJX,mBAAA,CAGMY,SAAA,QAAAC,WAAA,CAH2Bf,KAAA,CAAAQ,cAAc,GAAlCQ,QAAQ,EAAEC,KAAK;yBAA5Bf,mBAAA,CAGM;MAH4CgB,GAAG,EAAEF,QAAQ,CAACG,GAAG;MAAElC,KAAK,EAAC;QACzEC,mBAAA,CAAwE,MAAxEkC,WAAwE,EAAA1B,gBAAA,CAA1CuB,KAAK,QAAO,IAAE,GAAAvB,gBAAA,CAAGsB,QAAQ,CAACA,QAAQ,kBAChE9B,mBAAA,CAA2E;MAArEE,EAAE,aAAa4B,QAAQ,CAACG,GAAG;MAAElC,KAAK,EAAC,OAAO;MAAEI,KAAK,EAAAgC,eAAA,CAAErB,KAAA,CAAAsB,UAAU"}]}