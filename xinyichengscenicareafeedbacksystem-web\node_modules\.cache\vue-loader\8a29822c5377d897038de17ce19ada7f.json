{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue?vue&type=template&id=0fb93bb2&scoped=true", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue", "mtime": 1749041655057}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAExC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1E,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE/C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACtG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/Test.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"questionnaire-container\">\r\n    <h2 class=\"questionnaire-title\">调查问卷</h2>\r\n\r\n    <!-- 未登录提示 -->\r\n    <div v-if=\"!isLoggedIn\" class=\"login-prompt\">\r\n      <el-alert\r\n        title=\"请先登录\"\r\n        description=\"您需要登录后才能参与问卷调查\"\r\n        type=\"warning\"\r\n        show-icon\r\n        :closable=\"false\">\r\n      </el-alert>\r\n      <div class=\"login-actions\">\r\n        <el-button type=\"primary\" @click=\"goToLogin\">去登录</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 已完成问卷提示 -->\r\n    <div v-else-if=\"hasCompleted\" class=\"completed-prompt\">\r\n      <el-alert\r\n        title=\"您已完成问卷调查\"\r\n        description=\"感谢您的参与！您可以查看统计结果或重新填写问卷\"\r\n        type=\"success\"\r\n        show-icon\r\n        :closable=\"false\">\r\n      </el-alert>\r\n      <div class=\"completed-actions\">\r\n        <el-button type=\"primary\" @click=\"viewStatistics\">查看统计结果</el-button>\r\n        <el-button type=\"warning\" @click=\"retakeQuestionnaire\">重新填写问卷</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 问卷表单 -->\r\n    <div v-else-if=\"showQuestionnaire\">\r\n      <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n\r\n      <div v-else>\r\n        <div v-for=\"(question, index) in questionsList\" :key=\"question.qid\" class=\"question-item\">\r\n          <div class=\"question-text\">{{ index + 1 }}. {{ question.question }}</div>\r\n          <div class=\"options\">\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"非常满意\" /> 非常满意\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"满意\" /> 满意\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"一般\" /> 一般\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"不满意\" /> 不满意\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"submit-container\">\r\n          <button @click=\"submitQuestionnaire\" class=\"submit-btn\">提交问卷</button>\r\n        </div>\r\n\r\n        <div v-if=\"submitMessage\" class=\"message\" :class=\"{ 'success': submitSuccess, 'error': !submitSuccess }\">\r\n          {{ submitMessage }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"QuestionnaireView\",\r\n  data() {\r\n    return {\r\n      questionsList: [],\r\n      answers: {},\r\n      loading: true,\r\n      submitMessage: '',\r\n      submitSuccess: false,\r\n      isLoggedIn: false,\r\n      hasCompleted: false,\r\n      showQuestionnaire: false,\r\n      userAccount: ''\r\n    };\r\n  },\r\n  created() {\r\n    this.checkLoginStatus();\r\n  },\r\n  methods: {\r\n    // 检查登录状态\r\n    checkLoginStatus() {\r\n      this.userAccount = sessionStorage.getItem(\"lname\");\r\n      this.isLoggedIn = !!this.userAccount;\r\n\r\n      if (this.isLoggedIn) {\r\n        this.checkCompletionStatus();\r\n      }\r\n    },\r\n\r\n    // 检查用户是否已完成问卷\r\n    checkCompletionStatus() {\r\n      let url = base + \"/results/checkCompleted?account=\" + this.userAccount;\r\n\r\n      request.post(url, {}).then((res) => {\r\n        if (res.code === 200) {\r\n          this.hasCompleted = res.resdata;\r\n          if (!this.hasCompleted) {\r\n            this.showQuestionnaire = true;\r\n            this.getQuestions();\r\n          }\r\n        } else {\r\n          // 检查失败，默认显示问卷\r\n          this.showQuestionnaire = true;\r\n          this.getQuestions();\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"检查完成状态失败:\", error);\r\n        // 检查失败，默认显示问卷\r\n        this.showQuestionnaire = true;\r\n        this.getQuestions();\r\n      });\r\n    },\r\n\r\n    // 去登录\r\n    goToLogin() {\r\n      this.$router.push('/ulogin');\r\n    },\r\n\r\n    // 查看统计结果\r\n    viewStatistics() {\r\n      // 创建一个新的路由来显示统计结果\r\n      this.$router.push('/questionnaireStatistics');\r\n    },\r\n\r\n    // 重新填写问卷\r\n    retakeQuestionnaire() {\r\n      this.$confirm('确定要重新填写问卷吗？这将清除您之前的答案。', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.hasCompleted = false;\r\n        this.showQuestionnaire = true;\r\n        this.getQuestions();\r\n      }).catch(() => {\r\n        // 用户取消\r\n      });\r\n    },\r\n\r\n    // 获取问卷问题列表\r\n    getQuestions() {\r\n      this.loading = true;\r\n      let url = base + \"/questions/list\";\r\n      let para = {};\r\n      \r\n      request.post(url, para, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\r\n        if (res.resdata) {\r\n          this.questionsList = res.resdata;\r\n          // 初始化答案对象\r\n          this.questionsList.forEach(question => {\r\n            this.$set(this.answers, question.qid, '');\r\n          });\r\n        }\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error(\"获取问卷问题失败:\", error);\r\n        this.loading = false;\r\n      });\r\n    },\r\n    \r\n    // 提交问卷\r\n    submitQuestionnaire() {\r\n      // 检查是否所有问题都已回答\r\n      const unansweredQuestions = this.questionsList.filter(q => !this.answers[q.qid]);\r\n      \r\n      if (unansweredQuestions.length > 0) {\r\n        this.submitMessage = `请回答所有问题后再提交`;\r\n        this.submitSuccess = false;\r\n        return;\r\n      }\r\n      \r\n      // 准备提交数据\r\n      const results = [];\r\n      \r\n      // 将每个问题的答案转换为提交格式\r\n      this.questionsList.forEach(question => {\r\n        results.push({\r\n          qid: question.qid,\r\n          question: question.question,\r\n          status: this.answers[question.qid],\r\n          account: sessionStorage.getItem(\"lname\"),\r\n        });\r\n      });\r\n      \r\n      // 显示提交中状态\r\n      this.submitMessage = \"正在提交...\";\r\n      this.submitSuccess = true;\r\n      \r\n      // 一次性提交所有答案\r\n      let successCount = 0;\r\n      let failCount = 0;\r\n      \r\n      // 逐个提交答案并检查每个请求的响应\r\n      results.forEach(result => {\r\n        request.post(base + \"/results/add\", result)\r\n          .then(res => {\r\n            successCount++;\r\n            // 如果所有请求都已完成\r\n            if (successCount + failCount === results.length) {\r\n              if (failCount === 0) {\r\n                this.submitMessage = \"问卷提交成功，感谢您的反馈！\";\r\n                this.submitSuccess = true;\r\n                // 清空答案\r\n                this.answers = {};\r\n                this.questionsList.forEach(question => {\r\n                  this.$set(this.answers, question.qid, '');\r\n                });\r\n                // 更新完成状态\r\n                setTimeout(() => {\r\n                  this.hasCompleted = true;\r\n                  this.showQuestionnaire = false;\r\n                }, 2000);\r\n              } else {\r\n                this.submitMessage = `部分提交成功，${failCount}个问题提交失败`;\r\n                this.submitSuccess = false;\r\n              }\r\n            }\r\n          })\r\n          .catch(error => {\r\n            failCount++;\r\n            console.error(\"提交问题失败:\", error);\r\n            if (successCount + failCount === results.length) {\r\n              this.submitMessage = `部分提交成功，${failCount}个问题提交失败`;\r\n              this.submitSuccess = false;\r\n            }\r\n          });\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.questionnaire-container {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.questionnaire-title {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  font-size: 24px;\r\n}\r\n\r\n.loading {\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 16px;\r\n}\r\n\r\n.question-item {\r\n  margin-bottom: 25px;\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.question-text {\r\n  font-size: 16px;\r\n  margin-bottom: 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-left: 20px;\r\n}\r\n\r\n.option {\r\n  margin-right: 30px;\r\n  margin-bottom: 10px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.option input {\r\n  margin-right: 5px;\r\n}\r\n\r\n.submit-container {\r\n  text-align: center;\r\n  margin-top: 30px;\r\n}\r\n\r\n.submit-btn {\r\n  padding: 10px 30px;\r\n  background-color: #409EFF;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n}\r\n\r\n.submit-btn:hover {\r\n  background-color: #66b1ff;\r\n}\r\n\r\n.message {\r\n  text-align: center;\r\n  margin-top: 20px;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.success {\r\n  background-color: #f0f9eb;\r\n  color: #67c23a;\r\n  border: 1px solid #c2e7b0;\r\n}\r\n\r\n.error {\r\n  background-color: #fef0f0;\r\n  color: #f56c6c;\r\n  border: 1px solid #fbc4c4;\r\n}\r\n\r\n.login-prompt, .completed-prompt {\r\n  text-align: center;\r\n  padding: 30px;\r\n}\r\n\r\n.login-actions, .completed-actions {\r\n  margin-top: 20px;\r\n}\r\n\r\n.login-actions .el-button, .completed-actions .el-button {\r\n  margin: 0 10px;\r\n}\r\n</style>\r\n\r\n\r\n\r\n"]}]}