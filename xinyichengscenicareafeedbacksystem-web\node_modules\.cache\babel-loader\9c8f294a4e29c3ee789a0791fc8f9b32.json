{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\utils\\http.js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\utils\\http.js", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJzsKLy9heGlvcy5kZWZhdWx0cy53aXRoQ3JlZGVudGlhbHMgPSB0cnVlCmV4cG9ydCBjb25zdCBiYXNlID0gJ2h0dHA6Ly8xMjcuMC4wLjE6ODA4OC94aW55aWNoZW5nc2NlbmljYXJlYWZlZWRiYWNrc3lzdGVtL2FwaSc7CmNvbnN0IHJlcXVlc3QgPSBheGlvcy5jcmVhdGUoewogIHRpbWVvdXQ6IDUwMDAKfSk7CnJlcXVlc3QuaW50ZXJjZXB0b3JzLnJlcXVlc3QudXNlKGNvbmZpZyA9PiB7CiAgY29uZmlnLmhlYWRlcnNbJ0NvbnRlbnQtVHlwZSddID0gJ2FwcGxpY2F0aW9uL2pzb247Y2hhcnNldD11dGYtOCc7CiAgLy8gY29uZmlnLmhlYWRlcnNbJ3Rva2VuJ10gPSB1c2VyLnRva2VuOyAgLy8g6K6+572u6K+35rGC5aS0CiAgcmV0dXJuIGNvbmZpZzsKfSwgZXJyb3IgPT4gewogIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7Cn0pOwoKLy8g5Y+v5Lul5Zyo5o6l5Y+j5ZON5bqU5ZCO57uf5LiA5aSE55CG57uT5p6cCnJlcXVlc3QuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShyZXNwb25zZSA9PiB7CiAgbGV0IHJlcyA9IHJlc3BvbnNlLmRhdGE7CiAgLy8g5aaC5p6c5piv6L+U5Zue55qE5paH5Lu2CiAgaWYgKHJlc3BvbnNlLmNvbmZpZy5yZXNwb25zZVR5cGUgPT09ICdibG9iJykgewogICAgcmV0dXJuIHJlczsKICB9CiAgLy8g5YW85a655pyN5Yqh56uv6L+U5Zue55qE5a2X56ym5Liy5pWw5o2uCiAgaWYgKHR5cGVvZiByZXMgPT09ICdzdHJpbmcnKSB7CiAgICByZXMgPSByZXMgPyBKU09OLnBhcnNlKHJlcykgOiByZXM7CiAgfQogIHJldHVybiByZXM7Cn0sIGVycm9yID0+IHsKICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpOwp9KTsKZXhwb3J0IGRlZmF1bHQgcmVxdWVzdDs="}, {"version": 3, "names": ["axios", "base", "request", "create", "timeout", "interceptors", "use", "config", "headers", "error", "Promise", "reject", "response", "res", "data", "responseType", "JSON", "parse"], "sources": ["I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/utils/http.js"], "sourcesContent": ["import axios from 'axios';\r\n//axios.defaults.withCredentials = true\r\nexport const base = 'http://127.0.0.1:8088/xinyichengscenicareafeedbacksystem/api';\r\n\r\nconst request = axios.create({\r\n    timeout: 5000\r\n})\r\n\r\nrequest.interceptors.request.use(config => {\r\n    config.headers['Content-Type'] = 'application/json;charset=utf-8';\r\n    // config.headers['token'] = user.token;  // 设置请求头\r\n    return config\r\n}, error => {\r\n    return Promise.reject(error)\r\n});\r\n\r\n// 可以在接口响应后统一处理结果\r\nrequest.interceptors.response.use(\r\n    response => {\r\n        let res = response.data;\r\n        // 如果是返回的文件\r\n        if (response.config.responseType === 'blob') {\r\n            return res\r\n        }\r\n        // 兼容服务端返回的字符串数据\r\n        if (typeof res === 'string') {\r\n            res = res ? JSON.parse(res) : res\r\n        }\r\n        return res;\r\n    },\r\n    error => {\r\n        return Promise.reject(error)\r\n    }\r\n)\r\n\r\nexport default request\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB;AACA,OAAO,MAAMC,IAAI,GAAG,8DAA8D;AAElF,MAAMC,OAAO,GAAGF,KAAK,CAACG,MAAM,CAAC;EACzBC,OAAO,EAAE;AACb,CAAC,CAAC;AAEFF,OAAO,CAACG,YAAY,CAACH,OAAO,CAACI,GAAG,CAACC,MAAM,IAAI;EACvCA,MAAM,CAACC,OAAO,CAAC,cAAc,CAAC,GAAG,gCAAgC;EACjE;EACA,OAAOD,MAAM;AACjB,CAAC,EAAEE,KAAK,IAAI;EACR,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CAAC,CAAC;;AAEF;AACAP,OAAO,CAACG,YAAY,CAACO,QAAQ,CAACN,GAAG,CAC7BM,QAAQ,IAAI;EACR,IAAIC,GAAG,GAAGD,QAAQ,CAACE,IAAI;EACvB;EACA,IAAIF,QAAQ,CAACL,MAAM,CAACQ,YAAY,KAAK,MAAM,EAAE;IACzC,OAAOF,GAAG;EACd;EACA;EACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzBA,GAAG,GAAGA,GAAG,GAAGG,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC,GAAGA,GAAG;EACrC;EACA,OAAOA,GAAG;AACd,CAAC,EACDJ,KAAK,IAAI;EACL,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;AAED,eAAeP,OAAO"}]}