{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Ulogin.vue?vue&type=template&id=d8391d2e", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Ulogin.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_createBlock", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_createVNode", "_component_el_form_item", "label", "prop", "_component_el_input", "account", "$event", "placeholder", "style", "type", "password", "_component_el_button", "size", "onClick", "$options", "login", "loading", "btnLoading", "icon"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Ulogin.vue"], "sourcesContent": ["<template>\r\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"账号\" prop=\"account\">\r\n<el-input v-model=\"formData.account\" placeholder=\"账号\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"密码\" prop=\"password\">\r\n<el-input type=\"password\" v-model=\"formData.password\" placeholder=\"密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\"  size=\"small\" @click=\"login\" :loading=\"btnLoading\"   icon=\"el-icon-upload\" >登 录</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"Ulogin\",\r\n  data() {\r\n    return {\r\n      formData:{},\r\n\r\n      addrules: {\r\n          account: [{ required: true, message: '请输入账号', trigger: 'blur' }],\r\n          password: [{ required: true, message: '请输入密码', trigger: 'blur' }],\n\r\n      },\r\n\r\n\r\n      btnLoading: false, //按钮是否在加载中\r\n\r\n    };\r\n  },\r\n  created() {\r\n    \r\n  },\r\n  methods: {  \r\n    //登录\nlogin() {\n    //表单验证\n    this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n            let url = base + \"/users/login\"; //请求地址\n            this.btnLoading = true; //按钮加载状态\n            request.post(url, this.formData).then((res) => { //请求接口             \n                if (res.code == 200) {\n                    this.$message({\n                        message: \"登录成功\",\n                        type: \"success\",\n                        offset: 320,\n                    });                   \n                    sessionStorage.setItem(\"lname\", this.formData.account); //保存用户信息\n                    this.$router.push(\"/uweclome\"); //跳转到个人中心首页\n                }\n                else if (res.code == 201) {\n                    this.$message({\n                        message: res.msg,\n                        type: \"error\",\n                        offset: 320,\n                    });\n                    this.btnLoading = false;\n                }\n                else {\n                    this.$message({\n                        message: \"服务器错误\",\n                        type: \"error\",\n                        offset: 320,\n                    });\n                    this.btnLoading = false;\n                }\n            });\n        }\n    });\n},\n\r\n\r\n\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n\r\n\r\n"], "mappings": ";iDASsG,KAAG;;;;;;uBARrGA,YAAA,CAUMC,kBAAA;IAVIC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAGC,KAAK,EAAC;;sBAC5F,MAEe,CAFfC,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAsF,CAAtFH,YAAA,CAAsFI,mBAAA;oBAAnEV,KAAA,CAAAC,QAAQ,CAACU,OAAO;mEAAhBX,KAAA,CAAAC,QAAQ,CAACU,OAAO,GAAAC,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAEC,KAAkB,EAAlB;UAAA;QAAA;;;QAEvDR,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAuG,CAAvGH,YAAA,CAAuGI,mBAAA;QAA7FK,IAAI,EAAC,UAAU;oBAAUf,KAAA,CAAAC,QAAQ,CAACe,QAAQ;mEAAjBhB,KAAA,CAAAC,QAAQ,CAACe,QAAQ,GAAAJ,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAEC,KAAkB,EAAlB;UAAA;QAAA;;;QAExER,YAAA,CAEeC,uBAAA;wBADf,MAAqH,CAArHD,YAAA,CAAqHW,oBAAA;QAA1GF,IAAI,EAAC,SAAS;QAAEG,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAC,KAAK;QAAGC,OAAO,EAAEtB,KAAA,CAAAuB,UAAU;QAAIC,IAAI,EAAC;;0BAAkB,MAAG,C"}]}