{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\TopMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\TopMenu.vue", "mtime": 1748954728813}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uL3V0aWxzL2h0dHAiOwppbXBvcnQgIi4uL2Fzc2V0cy9jc3MvcWJvb3RzdHJhcC5jc3MiOwppbXBvcnQgIi4uL2Fzc2V0cy9jc3MvcWJ4c2xpZGVyLmNzcyI7CmltcG9ydCAiLi4vYXNzZXRzL2Nzcy9xc3R5bGUuY3NzIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJUb3BNZW51IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaXNsb2dpbjogdHJ1ZSwKICAgICAgbG5hbWU6ICcnLAogICAgICBpc2hvdzogZmFsc2UsCiAgICAgIGtleTogJycKICAgIH07CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5sbmFtZSA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oImxuYW1lIik7CiAgICBpZiAodGhpcy5sbmFtZSkgewogICAgICB0aGlzLmlzbG9naW4gPSBmYWxzZTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGV4aXQ6IGZ1bmN0aW9uICgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy4kY29uZmlybSgi56Gu6K6k6YCA5Ye65ZCXPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICBzZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKCJsbmFtZSIpOwogICAgICAgIGxvY2F0aW9uLmhyZWYgPSAiL2luZGV4IjsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["request", "base", "name", "data", "islogin", "lname", "ishow", "key", "mounted", "sessionStorage", "getItem", "methods", "exit", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "removeItem", "location", "href", "catch"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\TopMenu.vue"], "sourcesContent": ["<template>\r\n  \r\n   <header>\r\n             <div id=\"top-bar\" class=\"top-bar\">\r\n                 <div class=\"container\">\r\n                     <div class=\"row\">\r\n                         <div class=\"col-md-6\">\r\n                             <div class=\"top-left\">\r\n                                 <ul>\r\n                                     <li><i class=\"glyphicon glyphicon-map-marker\"></i>沈河区沈阳路171号</li>\r\n                                     <li><a href=\"mailto:<EMAIL>\" title=\"email\"><i class=\"glyphicon glyphicon-envelope\"></i><EMAIL></a></li>\r\n                                     <li><a href=\"\" title=\"Call\"><i class=\"glyphicon glyphicon-phone-alt\"></i>024-24843001</a></li>\r\n                                 </ul>\r\n                             </div>\r\n                         </div>\r\n                         <div class=\"col-md-3\">\r\n                             <div class=\"top-right\" style=\"margin-top: -8px;\">\r\n                                 <form action=\"/newsList\" method=\"get\" class=\"search-form\">\r\n                                     <input type=\"text\" name=\"keys\" placeholder=\"搜索...\" aria-label=\"搜索\" style=\"height: 33px; color: #333;\" />\r\n                                     <button type=\"submit\" class=\"btn btn-primary\">搜索</button>\r\n                                 </form>\r\n                             </div>\r\n                         </div>\r\n                         <div class=\"col-md-3 text-right\">\r\n                             <div class=\"top-right\">                               \r\n                                 \r\n         \r\n\t\t\t\t    <li v-if=\"islogin\">\r\n\t\t\t\t\t<a href=\"/Ureg\" style=\"display: contents; color: #fff;\">用户注册</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                <a href=\"/Ulogin\" style=\"display: contents; color: #fff;\">用户登录</a>\r\n\t\t\t\t</li>\r\n\t\t\t\t  <block v-else>\r\n            欢迎您：<a href=\"javascript:void(0);\" style=\"color: #fff;\">{{ lname }}</a>\r\n            &nbsp;|&nbsp;\r\n            <a href=\"javascript:void(0);\" @click=\"exit\" style=\"color: #fff;\">退出登录</a>\r\n          </block>\r\n\t\t\t\t\r\n\r\n                             </div>\r\n                         </div>\r\n                     </div>\r\n                 </div>\r\n             </div>\r\n             <!-- Fixed navbar -->\r\n             <nav class=\"navbar navbar-default navbar-fixed-top\">\r\n                 <div class=\"container\">\r\n                     <div class=\"navbar-header\">\r\n                         <button type=\"button\" class=\"navbar-toggle collapsed\" data-toggle=\"collapse\" data-target=\"#navbar\" aria-expanded=\"false\" aria-controls=\"navbar\"><span class=\"sr-only\">导航菜单</span><span class=\"icon-bar\"></span><span class=\"icon-bar\"></span><span class=\"icon-bar\"></span></button>\r\n                         <a href=\"/index\"><img src=\"../assets/images/logo.png\" style=\"height: 80px;\" class=\"logo\"></a>\r\n                     </div>\r\n                     <div id=\"navbar\" class=\"navbar-collapse collapse\">\r\n                         <ul class=\"nav navbar-nav\">\r\n                             <li class=''><a href=\"/index\">首 页</a> </li>\r\n                             <li class=\" dropdown\"><a href=\"/newsList\">新闻资讯</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/ticketInfoList\">票务信息</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/board\">意见反馈</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/bbs\">互动交流</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/test\">问卷调查</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/uweclome\">个人中心</a>  </li>\r\n\r\n                             <!-- <li class=\" dropdown\">\r\n                                 <a href=\"cpzx.html\">产品中心</a>\r\n                                 <a id=\"app_menudown\" class=\"dropdown-toggle\" data-toggle=\"dropdown\" role=\"button\" aria-expanded=\"false\"><span class=\"glyphicon glyphicon-menu-down btn-xs\"></span></a><ul class=\"dropdown-menu nav_small\" role=\"menu\">\r\n     \r\n                                     <li><a href=\"cpzx.html\">产品分类1</a></li>\r\n     \r\n                                     <li><a href=\"cpzx.html\">产品分类2</a></li>\r\n     \r\n                                     <li><a href=\"cpzx.html\">产品分类3</a></li>\r\n     \r\n                                 </ul>\r\n                             </li> -->\r\n                          \r\n                         </ul>\r\n                     </div>\r\n                     <!--/.nav-collapse -->\r\n                 </div>\r\n             </nav>\r\n         </header>    \r\n\r\n \r\n</template>\r\n<script>\r\nimport request, { base } from \"../../utils/http\";\r\nimport \"../assets/css/qbootstrap.css\"\r\nimport \"../assets/css/qbxslider.css\"\r\nimport \"../assets/css/qstyle.css\"\r\nexport default {\r\n  name: \"TopMenu\",\r\n  data() {\r\n    return {\r\n      islogin: true,\r\n      lname: '',\r\n      ishow: false,\r\n      key: '',\r\n      \r\n    };\r\n  },\r\n  mounted() {\r\n    this.lname = sessionStorage.getItem(\"lname\");\r\n    if (this.lname) {\r\n      this.islogin = false;\r\n    }\r\n  },\r\n  methods: {    \r\n       \r\n    \r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n          .then(() => {\r\n            sessionStorage.removeItem(\"lname\");\r\n            location.href=\"/index\";\r\n          })\r\n          .catch(() => { });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n\r\n</style>\r\n\r\n\r\n"], "mappings": "AAmFA,OAAOA,OAAO,IAAIC,IAAG,QAAS,kBAAkB;AAChD,OAAO,8BAA6B;AACpC,OAAO,6BAA4B;AACnC,OAAO,0BAAyB;AAChC,eAAe;EACbC,IAAI,EAAE,SAAS;EACfC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE;IAEP,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACH,KAAI,GAAII,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;IAC5C,IAAI,IAAI,CAACL,KAAK,EAAE;MACd,IAAI,CAACD,OAAM,GAAI,KAAK;IACtB;EACF,CAAC;EACDO,OAAO,EAAE;IAGPC,IAAI,EAAE,SAAAA,CAAA,EAAY;MAChB,IAAIC,KAAI,GAAI,IAAI;MAChB,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC5BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACIC,IAAI,CAAC,MAAM;QACVT,cAAc,CAACU,UAAU,CAAC,OAAO,CAAC;QAClCC,QAAQ,CAACC,IAAI,GAAC,QAAQ;MACxB,CAAC,EACAC,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACvB;EACF;AACF,CAAC"}]}