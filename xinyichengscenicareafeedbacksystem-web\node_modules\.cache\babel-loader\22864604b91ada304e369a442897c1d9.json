{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Foot.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Foot.vue", "mtime": 1747056618959}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJGb290IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHt9OwogIH0sCiAgbW91bnRlZCgpIHt9LAogIG1ldGhvZHM6IHt9Cn07"}, {"version": 3, "names": ["name", "data", "mounted", "methods"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Foot.vue"], "sourcesContent": ["<template>\n  \n   \n\n\n        <footer>\n            <div class=\"container\">\n                <div class=\"row\">\n                    <div class=\"col-md-6\">\n                        <p>&copy; 辽宁心怡程景区意见反馈系统 版权所有</p>\n                    </div>\n                    <div class=\"col-md-6 text-right\">\n                        <a href=\"/login\" class=\"btn btn-link \" style=\"color: #fff;\">管理员登录</a>\n                    </div>\n                </div>\n            </div>\n        </footer>\n        <!--客服面板-->\n        <div id=\"cmsFloatPanel\">\n            <div class=\"ctrolPanel2\">  \n                <a href=\"#\" style=\"color: #fff; text-decoration: none;\">Top</a>\n            </div>          \n           \n        </div>\n      \n</template>\n<script>\nexport default {\n  name: \"Foot\",\n  data() {\n    return {\n\n    };\n  },\n  mounted() {\n\n  },\n  methods: {  \n\n  },\n};\n</script>\n\n\n\n"], "mappings": "AA2BA,eAAe;EACbA,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO,CAEP,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG,CAEV,CAAC;EACDC,OAAO,EAAE,CAET;AACF,CAAC"}]}