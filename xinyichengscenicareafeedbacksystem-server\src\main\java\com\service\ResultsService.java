package com.service;
import java.util.List;
import java.util.Map;

import com.model.Results;
import com.util.PageBean;

public interface ResultsService{
	
	//查询多条记录
	public List<Results> queryResultsList(Results results,PageBean page) throws Exception;
 
	//添加
	public int insertResults(Results results) throws Exception ;
	
	//根据ID删除
	public int deleteResults(int id) throws Exception ;
	
	//更新
	public int updateResults(Results results) throws Exception ;
	
	//根据ID查询单条数据
	public Results queryResultsById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(Results results);

	//获取问卷统计数据
	List<Map<String, Object>> getQuestionnaireStatistics() throws Exception;

	//检查用户是否已完成问卷
	boolean checkUserCompleted(String account) throws Exception;

}

