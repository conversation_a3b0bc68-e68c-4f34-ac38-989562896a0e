{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Uweclome.vue?vue&type=template&id=eca868a0", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Uweclome.vue", "mtime": 1747058409557}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIA0KIA0KDQo8dGFibGUgIHN0eWxlPSJ3aWR0aDoxMDAlOyB0ZXh0LWFsaWduOmNlbnRlcjtsaW5lLWhlaWdodDoyOHB4OyB0ZXh0LWluZGVudDogMTBweDttYXJnaW4tdG9wOiA1MHB4OyI+DQogICAgPHRyPjx0ZCAgYWxpZ249InJpZ2h0IiB3aWR0aD0iMzAlIj4NCiAgICAgICAgPGltZyA6c3JjPSInaHR0cDovL2xvY2FsaG9zdDo4MDg4L3hpbnlpY2hlbmdzY2VuaWNhcmVhZmVlZGJhY2tzeXN0ZW0vJyArIGZvcm1EYXRhLmF2YXRhciIgIHN0eWxlPSJ3aWR0aDogMTUwcHg7aGVpZ2h0OiAxNTBweDtib3JkZXItcmFkaXVzOiA3MHB4OyIgLz4NCiAgICA8L3RkPjx0ZCBjbGFzcz0ic3R5bGUxIiB2YWxpZ249InRvcCIgc3R5bGU9Ig0KICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7DQogICAgcGFkZGluZy1sZWZ0OiAyMHB4Ow0KIj4NCiAgICAgICAgPHRhYmxlPg0KICAgICAgICAgICAgPHRyPjx0ZCBjbGFzcz0ic3R5bGUxIj4NCiAgICAgICAgICAgICAgICDmgqjlpb3vvJo8YiBzdHlsZT0iY29sb3I6cmVkOyI+DQogICAgICAgICAgICAgICAge3tsbmFtZX19DQogICAgICAgICAgICA8L2I+DQogICAgICAgICAgICA8L3RkPjwvdHI+DQogICAgICAgICAgICA8dHI+PHRkIGNsYXNzPSJzdHlsZTEiPg0KICAgICAgICAgICAgICAgIDwvdGQ+PC90cj4NCiAgICAgICAgICAgIDx0cj48dGQgY2xhc3M9InN0eWxlMSI+DQogICAgICAgICAgICAgICAg55m75b2V5pe26Ze077yae3t0aW1lfX08L3RkPjwvdHI+DQogICAgICAgICAgICA8dHI+PHRkIGNsYXNzPSJzdHlsZTEiPg0KICAgICAgICAgICAgPC90ZD48L3RyPg0KICAgICAgICA8L3RhYmxlPg0KICAgIDwvdGQ+PC90cj4NCg0KPC90YWJsZT4NCg0KDQoNCg0K"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Uweclome.vue"], "names": [], "mappings": ";;;;AAIA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACxJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC;QACO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/Uweclome.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  \r\n \r\n\r\n<table  style=\"width:100%; text-align:center;line-height:28px; text-indent: 10px;margin-top: 50px;\">\r\n    <tr><td  align=\"right\" width=\"30%\">\r\n        <img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' + formData.avatar\"  style=\"width: 150px;height: 150px;border-radius: 70px;\" />\r\n    </td><td class=\"style1\" valign=\"top\" style=\"\r\n    vertical-align: middle;\r\n    padding-left: 20px;\r\n\">\r\n        <table>\r\n            <tr><td class=\"style1\">\r\n                您好：<b style=\"color:red;\">\r\n                {{lname}}\r\n            </b>\r\n            </td></tr>\r\n            <tr><td class=\"style1\">\r\n                </td></tr>\r\n            <tr><td class=\"style1\">\r\n                登录时间：{{time}}</td></tr>\r\n            <tr><td class=\"style1\">\r\n            </td></tr>\r\n        </table>\r\n    </td></tr>\r\n\r\n</table>\r\n\r\n\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"Uweclome\",\r\n  data() {\r\n    return {\r\n      lname: \"\",\r\n      formData: {}, //表单数据  \r\n      time: '', //当前时间\r\n\r\n    };\r\n  },\r\n  created() {\r\n    this.lname = sessionStorage.getItem(\"lname\");\r\n      this.getDatas();\r\n      this.time = new Date().toLocaleString();\r\n\r\n  },\r\n  methods: {  \r\n    //获取列表数据\r\ngetDatas() {\r\n    let para = {\r\n    };\r\n    this.listLoading = true;\r\n    let url = base + \"/users/get?id=\" + this.lname;\r\n    request.post(url, para).then((res) => {\r\n        this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n        this.listLoading = false;\r\n    });\r\n},\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n\r\n\r\n"]}]}