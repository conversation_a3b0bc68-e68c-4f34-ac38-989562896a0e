{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\LeftMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\LeftMenu.vue", "mtime": 1749041492881}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAkIGZyb20gJ2pxdWVyeSc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiTGVmdE1lbnUiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1c2VyTG5hbWU6ICIiLAogICAgICByb2xlOiAiIiwKICAgICAgYWN0aXZlTWVudTogbnVsbCAvLyDnlKjkuo7ot5/ouKrlvZPliY3mv4DmtLvnmoToj5zljZUKICAgIH07CiAgfSwKCiAgbW91bnRlZCgpIHsKICAgIHRoaXMudXNlckxuYW1lID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgidXNlckxuYW1lIik7CiAgICB0aGlzLnJvbGUgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCJyb2xlIik7CiAgfSwKICBtZXRob2RzOiB7CiAgICB0b2dnbGVNZW51KG1lbnUpIHsKICAgICAgdGhpcy5hY3RpdmVNZW51ID0gdGhpcy5hY3RpdmVNZW51ID09PSBtZW51ID8gbnVsbCA6IG1lbnU7CiAgICB9LAogICAgZXhpdDogZnVuY3Rpb24gKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCLnoa7orqTpgIDlh7rlkJc/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oInVzZXJMbmFtZSIpOwogICAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oInJvbGUiKTsKICAgICAgICBfdGhpcy4kcm91dGVyLnB1c2goIi9sb2dpbiIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["$", "name", "data", "userLname", "role", "activeMenu", "mounted", "sessionStorage", "getItem", "methods", "toggleMenu", "menu", "exit", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "removeItem", "$router", "push", "catch"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\LeftMenu.vue"], "sourcesContent": ["<template>\r\n    <div class=\"left-side-menu\">\r\n\r\n        <div class=\"slimscroll-menu\">\r\n\r\n            <!--- Sidemenu -->\r\n            <div id=\"sidebar-menu\" style=\"max-height: calc(100vh - 70px); overflow-y: auto;\">\r\n\r\n                <ul class=\"metismenu\" id=\"side-menu\">\r\n\r\n                    <li class=\"menu-title\">功能菜单</li>\r\n\r\n                    <li>\r\n                      <router-link to=\"/main\"><i class=\"mdi mdi-view-dashboard\"></i><span>首页</span></router-link>                       \r\n                    </li>\r\n\r\n                    <li :class=\"{ active: activeMenu === 'nav9' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav9')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">管理员管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav9' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/adminAdd\">添加管理员</router-link></li>\r\n  <li> <router-link to=\"/adminManage\">管理管理员</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n                    <li :class=\"{ active: activeMenu === 'nav5' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav5')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">用户管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav5' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/usersAdd\">添加用户</router-link></li>\r\n  <li> <router-link to=\"/usersManage\">管理用户</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n                    <li :class=\"{ active: activeMenu === 'nav7' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav7')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">景区管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav7' }\" aria-expanded=\"false\">\r\n                             <!-- <li> <router-link to=\"/scenicareaAdd\">添加景区</router-link></li> -->\r\n  <li> <router-link to=\"/scenicareaManage\">管理景区</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n                    <li :class=\"{ active: activeMenu === 'nav10' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav10')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">新闻资讯管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav10' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/newsAdd\">添加新闻资讯</router-link></li>\r\n  <li> <router-link to=\"/newsManage\">管理新闻资讯</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n                    <li :class=\"{ active: activeMenu === 'nav11' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav11')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">意见类别管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav11' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/categoryAdd\">添加意见类别</router-link></li>\r\n  <li> <router-link to=\"/categoryManage\">管理意见类别</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                    <li :class=\"{ active: activeMenu === 'nav6' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav6')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">意见反馈管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav6' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/boardManage\">管理意见反馈</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n     \r\n      <li :class=\"{ active: activeMenu === 'nav8' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav8')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">调查问卷管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav8' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/questionsAdd\">添加调查问卷</router-link></li>\r\n  <li> <router-link to=\"/questionsManage\">管理调查问卷</router-link></li>\r\n  <li> <router-link to=\"/questionsStatistics\">问卷调查统计</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n\r\n\r\n                        <li :class=\"{ active: activeMenu === 'nav1' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav1')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">票务信息管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav1' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/ticketInfoAdd\">添加票务信息</router-link></li>\r\n  <li> <router-link to=\"/ticketInfoManage\">管理票务信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n      <li :class=\"{ active: activeMenu === 'nav2' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav2')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">购票信息管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav2' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/ticketpurchasesManage\">管理购票信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                    <li :class=\"{ active: activeMenu === 'nav12' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav12')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">问卷结果管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav12' }\" aria-expanded=\"false\">\r\n                             <!-- <li> <router-link to=\"/resultsAdd\">添加问卷结果</router-link></li> -->\r\n  <li> <router-link to=\"/resultsManage\">管理问卷结果</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n      <li :class=\"{ active: activeMenu === 'nav4' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav4')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">互动交流管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav4' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/postsManage\">管理互动交流</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n     \r\n      \r\n     \r\n      \r\n     \r\n     \r\n      <li :class=\"{ active: activeMenu === 'nav25' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav25')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">报表统计</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav25' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/total1\">意见类别统计</router-link></li>\r\n  <li> <router-link to=\"/total2\">用户统计</router-link></li>\r\n  <li> <router-link to=\"/total3\">票务统计</router-link></li>\r\n  <!-- <li> <router-link to=\"/total4\">问卷统计</router-link></li> -->\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n                    <li :class=\"{ active: activeMenu === 'nav22' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav22')\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span> 系统管理 </span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav22' }\" aria-expanded=\"false\">\r\n                          <li><router-link to=\"password\">修改密码</router-link></li>\r\n                        </ul>\r\n                    </li>\r\n\r\n                \r\n\r\n                </ul>\r\n\r\n            </div>\r\n            <!-- Sidebar -->\r\n\r\n            <div class=\"clearfix\"></div>\r\n\r\n        </div>\r\n        <!-- Sidebar -left -->\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n  },\r\n  methods: {\r\n    toggleMenu(menu) {\r\n      this.activeMenu = this.activeMenu === menu ? null : menu;\r\n    },\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem(\"userLname\");\r\n          sessionStorage.removeItem(\"role\");\r\n          _this.$router.push(\"/login\");\r\n        })\r\n        .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.example-showcase .el-dropdown-link {\r\n  cursor: pointer;\r\n  color:green;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.pcoded-submenu a{\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n/*加点击效果*/\r\n.pcoded-submenu a:hover{\r\n  color: #fff;\r\n  color: #ff6600;\r\n}\r\n</style>\r\n\r\n\r\n"], "mappings": ";AAiLA,OAAOA,CAAA,MAAO,QAAQ;AAEtB,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,IAAI,CAAE;IACpB,CAAC;EACH,CAAC;;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACH,SAAQ,GAAII,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAI,CAACJ,IAAG,GAAIG,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;EAC5C,CAAC;EACDC,OAAO,EAAE;IACPC,UAAUA,CAACC,IAAI,EAAE;MACf,IAAI,CAACN,UAAS,GAAI,IAAI,CAACA,UAAS,KAAMM,IAAG,GAAI,IAAG,GAAIA,IAAI;IAC1D,CAAC;IACDC,IAAI,EAAE,SAAAA,CAAA,EAAY;MAChB,IAAIC,KAAI,GAAI,IAAI;MAChB,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC5BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACVX,cAAc,CAACY,UAAU,CAAC,WAAW,CAAC;QACtCZ,cAAc,CAACY,UAAU,CAAC,MAAM,CAAC;QACjCN,KAAK,CAACO,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;MAC9B,CAAC,EACAC,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB;EACF;AACF,CAAC"}]}