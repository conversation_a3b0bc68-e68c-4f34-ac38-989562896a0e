{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\NewsList.vue?vue&type=style&index=0&id=1eb20da6&lang=css", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\NewsList.vue", "mtime": 1747229072564}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749040033032}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749040034646}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749040033671}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoNCiAgICAuY29udGFpbmVyMjIyIHsNCiAgICAgICAgZGlzcGxheTogZ3JpZDsNCiAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgzMDBweCwgMWZyKSk7DQogICAgICAgIGdhcDogMjBweDsNCiAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgfQ0KICAgIC5jYXJkMjIyIHsgIA0KICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTsgIA0KICAgICAgICBib3JkZXItcmFkaXVzOiAxMHB4OyAgDQogICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEwcHggcmdiYSgwLCAwLCAwLCAwLjEpOyAgDQogICAgICAgIG92ZXJmbG93OiBoaWRkZW47ICANCiAgICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MsIGJveC1zaGFkb3cgMC4zczsgIA0KICAgIH0gDQogICAgLmNhcmQyMjIgYXsNCiAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lOw0KICAgIH0NCiAgICAuY2FyZDIyMjpob3ZlciB7ICANCiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpOyAgDQogICAgICAgIGJveC1zaGFkb3c6IDAgOHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjIpOyAgDQogICAgfSAgDQogICAgLmNhcmQyMjIgaW1nIHsgIA0KICAgICAgICB3aWR0aDogMTAwJTsgIA0KICAgICAgICBoZWlnaHQ6IDIwMHB4OyAgDQogICAgICAgIG9iamVjdC1maXQ6IGNvdmVyOyAgDQogICAgfSAgDQogICAgLmNhcmQtY29udGVudDIyMiB7ICANCiAgICAgICAgcGFkZGluZzogMTVweDsgIA0KICAgIH0gIA0KICAgIC5jYXJkLWNvbnRlbnQyMjIgaDIgeyAgDQogICAgICAgIGZvbnQtc2l6ZTogMThweDsgIA0KICAgICAgICBtYXJnaW46IDEwcHggMDsgIA0KICAgIH0gIA0KICAgIC5jYXJkLWNvbnRlbnQyMjIgcCB7ICANCiAgICAgICAgY29sb3I6ICM1NTU7ICANCiAgICAgICAgZm9udC1zaXplOiAxNHB4OyAgDQogICAgICAgIG1hcmdpbjogMTBweCAwOyAgDQogICAgfSAgDQogICAgLmNhcmQtZm9vdGVyMjIyIHsgIA0KICAgICAgICBkaXNwbGF5OiBmbGV4OyAgDQogICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsgIA0KICAgICAgICBmb250LXNpemU6IDEycHg7ICANCiAgICAgICAgY29sb3I6ICM4ODg7ICANCiAgICB9ICANCiAgICAucmVhZC1tb3JlMjIyIHsgIA0KICAgICAgICBjb2xvcjogIzAwN0JGRjsgIA0KICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7ICANCiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7ICANCiAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgIH0gIA0KICAgIC5yZWFkLW1vcmUyMjI6aG92ZXIgeyAgDQogICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lOyAgDQogICAgfSAgDQoNCg0KDQo="}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\NewsList.vue"], "names": [], "mappings": ";;IAgFI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/C;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7C;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/NewsList.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n   <div class=\"container222\">\r\n   \r\n    <div class=\"card222\" v-for=\"item in nelist\" :key=\"item.id\">  \r\n        <img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' + item.nimage\" style=\"height: 230px;\">  \r\n        <div class=\"card-content222\">  \r\n            <h2><a :href=\"'newsView?id='+item.id\">{{item.title}}</a></h2>  \r\n            <div class=\"card-footer222\">  \r\n                <span>发布时间：{{item.publishtime}}</span>           \r\n            </div>            \r\n            <p>{{item.content.length > 70 ? item.content.substring(0,70):item.content}}</p>  \r\n            <a :href=\"'newsView?id='+item.id\" class=\"read-more222\">查看更多</a>  \r\n        </div>  \r\n    </div>  \r\n\r\n</div> \r\n<el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\nbackground layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\r\nstyle=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"NewsList\",\r\n  data() {\r\n    return {\r\n          page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 12, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n    },\r\n    nelist: \"\",\r\n \r\n    };\r\n  },\r\n  created() {\r\n    \r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {  \r\n    \r\n    // 分页\r\n    handleCurrentChange(val) {\r\n        this.page.currentPage = val;\r\n        this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n        var keys = this.$route.query.keys;\r\n        var sql=\"\";\r\n        if(keys){\r\n            sql=\" and title like '%\"+keys+\"%'\";\r\n        }\r\n        let para = {\r\n            condition:sql\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/news/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\r\n        request.post(url, para).then((res) => {\r\n            if (res.resdata.length > 0) {\r\n                this.isPage = true;\r\n            } else {\r\n                this.isPage = false;\r\n            }\r\n            this.page.totalCount = res.count;\r\n            this.nelist = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n\r\n    .container222 {\r\n        display: grid;\r\n        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n        gap: 20px;\r\n        width: 100%;\r\n    }\r\n    .card222 {  \r\n        background-color: white;  \r\n        border-radius: 10px;  \r\n        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);  \r\n        overflow: hidden;  \r\n        transition: transform 0.3s, box-shadow 0.3s;  \r\n    } \r\n    .card222 a{\r\n        text-decoration: none;\r\n    }\r\n    .card222:hover {  \r\n        transform: translateY(-5px);  \r\n        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);  \r\n    }  \r\n    .card222 img {  \r\n        width: 100%;  \r\n        height: 200px;  \r\n        object-fit: cover;  \r\n    }  \r\n    .card-content222 {  \r\n        padding: 15px;  \r\n    }  \r\n    .card-content222 h2 {  \r\n        font-size: 18px;  \r\n        margin: 10px 0;  \r\n    }  \r\n    .card-content222 p {  \r\n        color: #555;  \r\n        font-size: 14px;  \r\n        margin: 10px 0;  \r\n    }  \r\n    .card-footer222 {  \r\n        display: flex;  \r\n        justify-content: space-between;  \r\n        font-size: 12px;  \r\n        color: #888;  \r\n    }  \r\n    .read-more222 {  \r\n        color: #007BFF;  \r\n        text-decoration: none;  \r\n        font-weight: bold;  \r\n        font-size: 12px;\r\n    }  \r\n    .read-more222:hover {  \r\n        text-decoration: underline;  \r\n    }  \r\n\r\n\r\n\r\n</style>\r\n\r\n\r\n\r\n"]}]}