package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Results;

public interface ResultsMapper {

	//返回所有记录
	public List<Results> findResultsList();
	
	//查询多条记录
	public List<Results> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertResults(Results results);

	//根据ID删除
	public int deleteResults(int id);
	
	//更新
	public int updateResults(Results results);
	
	//根据ID得到对应的记录
	public Results queryResultsById(int id);

	//获取问题统计数据
	public List<Map<String, Object>> getQuestionStatistics();

	//获取选项统计数据
	public List<Map<String, Object>> getOptionStatistics(int qid);

	//获取用户已完成问卷数量
	public int getUserCompletedCount(String account);

	//获取问卷总题目数
	public int getTotalQuestionCount();

}

