{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Left.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Left.vue", "mtime": 1747058115006}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uL3V0aWxzL2h0dHAiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkxlZnQiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsaXN0MTogIiIsCiAgICAgIGxpc3QyOiAiIgogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmdldGxpc3QxKCk7CiAgICB0aGlzLmdldGxpc3QyKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDojrflj5bmlrDpl7votYTorq8KICAgIGdldGxpc3QxKCkgewogICAgICBsZXQgcGFyYSA9IHt9OwogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL25ld3MvbGlzdD9jdXJyZW50UGFnZT0xJnBhZ2VTaXplPTYiOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5saXN0MSA9IHJlcy5yZXNkYXRhOwogICAgICAgIHRoaXMubGlzdExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6I635Y+W56Wo5Yqh5L+h5oGvCiAgICBnZXRsaXN0MigpIHsKICAgICAgbGV0IHBhcmEgPSB7fTsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7CiAgICAgIGxldCB1cmwgPSBiYXNlICsgIi90aWNrZXRJbmZvL2xpc3Q/Y3VycmVudFBhZ2U9MSZwYWdlU2l6ZT02IjsKICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMubGlzdDIgPSByZXMucmVzZGF0YTsKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["request", "base", "name", "data", "list1", "list2", "mounted", "getlist1", "getlist2", "methods", "para", "listLoading", "url", "post", "then", "res", "resdata"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Left.vue"], "sourcesContent": ["<template>\r\n  \r\n\r\n<div class=\"col-xs-12 col-sm-4 col-md-3\">\r\n       <div class=\"left_contact\">\r\n    <h3 class=\"left_h3\" style=\"margin-bottom:20px;\"><span>新闻资讯</span></h3>\r\n     <ul class=\"product-list\">\r\n\r\n        <li class=\"product-item\"  v-for=\"(item,index) in list1\" :key=\"index\" >\r\n            <a :href=\"'newsView?id='+item.id\">\r\n                <div class=\"product-image-wrapper\">\r\n                    <img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' +item.nimage\" class=\"product-image\" style=\"width: 230px;height: 140px;\" />\r\n                </div>\r\n                <div class=\"product-info\">\r\n                    <span class=\"product-name\">{{item.title}}</span>\r\n              \r\n                </div>\r\n            </a>\r\n        </li>       \r\n\r\n    </ul>\r\n</div>\r\n<div class=\"left_news\">\r\n    <h3 class=\"left_h3\"><span>票务信息</span></h3>\r\n    <ul class=\"left_news\">\r\n        <li  v-for=\"(item,index) in list2\" :key=\"index\" ><a :href=\"'ticketInfoView?id='+item.ticketid\">{{item.tname}}</a></li>       \r\n\r\n    </ul>\r\n</div>\r\n\r\n </div>\r\n \r\n</template>\r\n<script>\r\nimport request, { base } from \"../../utils/http\";\r\nexport default {\r\n  name: \"Left\",\r\n  data() {\r\n    return {\r\n    list1:\"\",\r\n    list2:\"\",\r\n\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getlist1();\r\n    this.getlist2();\r\n\r\n  },\r\n  methods: {\r\n\r\n    // 获取新闻资讯\r\n    getlist1() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/news/list?currentPage=1&pageSize=6\";\r\n        request.post(url, para).then((res) => {         \r\n            this.list1 = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n    // 获取票务信息\r\n    getlist2() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/ticketInfo/list?currentPage=1&pageSize=6\";\r\n        request.post(url, para).then((res) => {         \r\n            this.list2 = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n  },\r\n};\r\n</script>\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAkCA,OAAOA,OAAO,IAAIC,IAAG,QAAS,kBAAkB;AAChD,eAAe;EACbC,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO;MACPC,KAAK,EAAC,EAAE;MACRC,KAAK,EAAC;IAEN,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,QAAQ,CAAC,CAAC;EAEjB,CAAC;EACDC,OAAO,EAAE;IAEP;IACAF,QAAQA,CAAA,EAAG;MACP,IAAIG,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIX,IAAG,GAAI,qCAAqC;MACtDD,OAAO,CAACa,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAI,CAACX,KAAI,GAAIW,GAAG,CAACC,OAAO;QACxB,IAAI,CAACL,WAAU,GAAI,KAAK;MAC5B,CAAC,CAAC;IACN,CAAC;IAED;IACAH,QAAQA,CAAA,EAAG;MACP,IAAIE,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIX,IAAG,GAAI,2CAA2C;MAC5DD,OAAO,CAACa,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAI,CAACV,KAAI,GAAIU,GAAG,CAACC,OAAO;QACxB,IAAI,CAACL,WAAU,GAAI,KAAK;MAC5B,CAAC,CAAC;IACN;EAEF;AACF,CAAC"}]}