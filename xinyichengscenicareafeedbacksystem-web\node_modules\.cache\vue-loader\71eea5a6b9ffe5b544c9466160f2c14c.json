{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\ScenicareaView.vue?vue&type=template&id=1287a90c", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\ScenicareaView.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogICANCjx0YWJsZSBzdHlsZT0id2lkdGg6MTAwJTsgbGluZS1oZWlnaHQ6MjlweDtmb250LXNpemU6MTJweDsiPg0KICAgIDx0cj4NCiAgICAgICAgPHRkIHdpZHRoPSIzMzAiPg0KICAgICAgICAgICAgPGltZyA6c3JjPSInaHR0cDovL2xvY2FsaG9zdDo4MDg4L3hpbnlpY2hlbmdzY2VuaWNhcmVhZmVlZGJhY2tzeXN0ZW0vJyArc2NsaXN0LnBpY3VybCIgIHN0eWxlPSd3aWR0aDozNTBweCcgLz4NCiAgICAgICAgPC90ZD4NCiAgICAgICAgPHRkIGFsaWduPSJsZWZ0Ij4NCiAgICAgICAgICAgIDx0YWJsZSB3aWR0aD0iMTAwJSI+DQogICAgICAgICAgICAgICAgPHRyPg0KICAgICAgICAgICAgICAgICAgICA8dGQgc3R5bGU9ImZvbnQtc2l6ZToxNnB4O2ZvbnQ6Ym9sZDsiPnt7c2NsaXN0LmFuYW1lfX08L3RkPg0KICAgICAgICAgICAgICAgIDwvdHI+ICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgICA8dHI+PHRkPuS9jee9ru+8mnt7c2NsaXN0LmxvY2F0aW9uc319PC90ZD48L3RyPgogPHRyPjx0ZD7ogZTns7vmlrnlvI/vvJp7e3NjbGlzdC5jb250YWN0aW5mb319PC90ZD48L3RyPgogPHRyPjx0ZD7lvIDmlL7ml7bpl7TvvJp7e3NjbGlzdC5vcGVudGltZX19PC90ZD48L3RyPgogPHRyPjx0ZD7lhbPpl63ml7bpl7TvvJp7e3NjbGlzdC5jbG9zZXRpbWV9fTwvdGQ+PC90cj4KIDx0cj48dGQ+6Zeo56Wo5Lu35qC877yae3tzY2xpc3QudGlja2V0cHJpY2V9fTwvdGQ+PC90cj4KIDx0cj48dGQ+5pyA5L2z5a2j6IqC77yae3tzY2xpc3Quc2Vhc29uYmVzdH19PC90ZD48L3RyPgoNCiAgICAgICAgICAgICAgICA8dHI+PHRkPjwvdGQ+PC90cj4NCiAgICAgICAgICAgIDwvdGFibGU+DQogICAgICAgICAgICANCiAgICAgICAgPC90ZD4NCiAgICA8L3RyPg0KICAgIDx0cj4NCiAgICAgICAgPHRkIGNvbHNwYW49IjIiIHN0eWxlPSJib3JkZXItYm90dG9tOjJweCBzb2xpZCAjMDAyYjU3OyI+PGRpdiBzdHlsZT0iYmFja2dyb3VuZC1jb2xvcjojMDAyYjU3O2NvbG9yOndoaXRlO3dpZHRoOiAxMDBweDtoZWlnaHQ6IDMwcHg7dGV4dC1hbGlnbjogY2VudGVyOyI+6K+m5oOFPC9kaXY+PC90ZD4NCiAgICA8L3RyPg0KICAgIDx0cj4NCiAgICAgICAgPHRkIGNvbHNwYW49IjIiPjxkaXYgdi1odG1sPSJzY2xpc3QuZGVzY3JpcHRpb24iPjwvZGl2PjwvdGQ+DQogICAgPC90cj4NCg0KPC90YWJsZT4NCg0KDQoNCg0K"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\ScenicareaView.vue"], "names": [], "mappings": ";;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;QACC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACjH,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC;oBACC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAAC,CAAC;iBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEX,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;QACC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzK,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;QACC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC,CAAC;;AAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/ScenicareaView.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n   \r\n<table style=\"width:100%; line-height:29px;font-size:12px;\">\r\n    <tr>\r\n        <td width=\"330\">\r\n            <img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' +sclist.picurl\"  style='width:350px' />\r\n        </td>\r\n        <td align=\"left\">\r\n            <table width=\"100%\">\r\n                <tr>\r\n                    <td style=\"font-size:16px;font:bold;\">{{sclist.aname}}</td>\r\n                </tr>             \r\n                 <tr><td>位置：{{sclist.locations}}</td></tr>\n <tr><td>联系方式：{{sclist.contactinfo}}</td></tr>\n <tr><td>开放时间：{{sclist.opentime}}</td></tr>\n <tr><td>关闭时间：{{sclist.closetime}}</td></tr>\n <tr><td>门票价格：{{sclist.ticketprice}}</td></tr>\n <tr><td>最佳季节：{{sclist.seasonbest}}</td></tr>\n\r\n                <tr><td></td></tr>\r\n            </table>\r\n            \r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td colspan=\"2\" style=\"border-bottom:2px solid #002b57;\"><div style=\"background-color:#002b57;color:white;width: 100px;height: 30px;text-align: center;\">详情</div></td>\r\n    </tr>\r\n    <tr>\r\n        <td colspan=\"2\"><div v-html=\"sclist.description\"></div></td>\r\n    </tr>\r\n\r\n</table>\r\n\r\n\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"ScenicareaView\",\r\n  data() {\r\n    return {\r\n          sclist: \"\",\n \r\n\r\n    };\r\n  },\r\n  created() {\r\n    \n    this.getDatas();\n\r\n\r\n  },\r\n  methods: {  \r\n    \n    //获取列表数据\n    getDatas() {\n        let id = this.$route.query.id;\n        let para = {           \n        };\n        this.listLoading = true;\n        let url = base + \"/scenicarea/get?id=\" + id ;\n        request.post(url, para).then((res) => {\n            this.sclist = res.resdata;\n        });\n    },    \r\n\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n\r\n</style>\r\n\r\n\r\n\r\n"]}]}