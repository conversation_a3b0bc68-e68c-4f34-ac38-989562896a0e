﻿<template>
    <div class="left-side-menu">

        <div class="slimscroll-menu">

            <!--- Sidemenu -->
            <div id="sidebar-menu" style="max-height: calc(100vh - 70px); overflow-y: auto;">

                <ul class="metismenu" id="side-menu">

                    <li class="menu-title">功能菜单</li>

                    <li>
                      <router-link to="/main"><i class="mdi mdi-view-dashboard"></i><span>首页</span></router-link>                       
                    </li>

                    <li :class="{ active: activeMenu === 'nav9' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav9')" aria-expanded="true">
                            <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">管理员管理</span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav9' }" aria-expanded="false">
                             <li> <router-link to="/adminAdd">添加管理员</router-link></li>
  <li> <router-link to="/adminManage">管理管理员</router-link></li>

                        </ul>
                    </li>
                    <li :class="{ active: activeMenu === 'nav5' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav5')" aria-expanded="true">
                            <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">用户管理</span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav5' }" aria-expanded="false">
                             <li> <router-link to="/usersAdd">添加用户</router-link></li>
  <li> <router-link to="/usersManage">管理用户</router-link></li>

                        </ul>
                    </li>
                    <li :class="{ active: activeMenu === 'nav7' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav7')" aria-expanded="true">
                            <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">景区管理</span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav7' }" aria-expanded="false">
                             <!-- <li> <router-link to="/scenicareaAdd">添加景区</router-link></li> -->
  <li> <router-link to="/scenicareaManage">管理景区</router-link></li>

                        </ul>
                    </li>
                    <li :class="{ active: activeMenu === 'nav10' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav10')" aria-expanded="true">
                            <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">新闻资讯管理</span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav10' }" aria-expanded="false">
                             <li> <router-link to="/newsAdd">添加新闻资讯</router-link></li>
  <li> <router-link to="/newsManage">管理新闻资讯</router-link></li>

                        </ul>
                    </li>
                    <li :class="{ active: activeMenu === 'nav11' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav11')" aria-expanded="true">
                            <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">意见类别管理</span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav11' }" aria-expanded="false">
                             <li> <router-link to="/categoryAdd">添加意见类别</router-link></li>
  <li> <router-link to="/categoryManage">管理意见类别</router-link></li>

                        </ul>
                    </li>

                    <li :class="{ active: activeMenu === 'nav6' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav6')" aria-expanded="true">
                            <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">意见反馈管理</span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav6' }" aria-expanded="false">
                             <li> <router-link to="/boardManage">管理意见反馈</router-link></li>

                        </ul>
                    </li>
     
      <li :class="{ active: activeMenu === 'nav8' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav8')" aria-expanded="true">
                            <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">调查问卷管理</span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav8' }" aria-expanded="false">
                             <li> <router-link to="/questionsAdd">添加调查问卷</router-link></li>
  <li> <router-link to="/questionsManage">管理调查问卷</router-link></li>
  <li> <router-link to="/questionsStatistics">问卷调查统计</router-link></li>

                        </ul>
                    </li>




                        <li :class="{ active: activeMenu === 'nav1' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav1')" aria-expanded="true">
                            <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">票务信息管理</span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav1' }" aria-expanded="false">
                             <li> <router-link to="/ticketInfoAdd">添加票务信息</router-link></li>
  <li> <router-link to="/ticketInfoManage">管理票务信息</router-link></li>

                        </ul>
                    </li>
      <li :class="{ active: activeMenu === 'nav2' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav2')" aria-expanded="true">
                            <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">购票信息管理</span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav2' }" aria-expanded="false">
                             <li> <router-link to="/ticketpurchasesManage">管理购票信息</router-link></li>

                        </ul>
                    </li>

                    <li :class="{ active: activeMenu === 'nav12' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav12')" aria-expanded="true">
                            <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">问卷结果管理</span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav12' }" aria-expanded="false">
                             <!-- <li> <router-link to="/resultsAdd">添加问卷结果</router-link></li> -->
  <li> <router-link to="/resultsManage">管理问卷结果</router-link></li>

                        </ul>
                    </li>

      <li :class="{ active: activeMenu === 'nav4' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav4')" aria-expanded="true">
                            <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">互动交流管理</span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav4' }" aria-expanded="false">
                             <li> <router-link to="/postsManage">管理互动交流</router-link></li>

                        </ul>
                    </li>
     
      
     
      
     
     
      <li :class="{ active: activeMenu === 'nav25' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav25')" aria-expanded="true">
                            <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">报表统计</span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav25' }" aria-expanded="false">
                             <li> <router-link to="/total1">意见类别统计</router-link></li>
  <li> <router-link to="/total2">用户统计</router-link></li>
  <li> <router-link to="/total3">票务统计</router-link></li>
  <!-- <li> <router-link to="/total4">问卷统计</router-link></li> -->

                        </ul>
                    </li>


                    <li :class="{ active: activeMenu === 'nav22' }">
                        <a href="javascript: void(0);" @click="toggleMenu('nav22')">
                            <i class="mdi mdi-chart-donut-variant"></i><span> 系统管理 </span> <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level collapse" :class="{ in: activeMenu === 'nav22' }" aria-expanded="false">
                          <li><router-link to="password">修改密码</router-link></li>
                        </ul>
                    </li>

                

                </ul>

            </div>
            <!-- Sidebar -->

            <div class="clearfix"></div>

        </div>
        <!-- Sidebar -left -->

    </div>
</template>

<script>
import $ from 'jquery';

export default {
  name: "LeftMenu",
  data() {
    return {
      userLname: "",
      role: "",
      activeMenu: null, // 用于跟踪当前激活的菜单
    };
  },
  mounted() {
    this.userLname = sessionStorage.getItem("userLname");
    this.role = sessionStorage.getItem("role");
  },
  methods: {
    toggleMenu(menu) {
      this.activeMenu = this.activeMenu === menu ? null : menu;
    },
    exit: function () {
      var _this = this;
      this.$confirm("确认退出吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          sessionStorage.removeItem("userLname");
          sessionStorage.removeItem("role");
          _this.$router.push("/login");
        })
        .catch(() => { });
    },
  }
};
</script>

<style scoped>
.example-showcase .el-dropdown-link {
  cursor: pointer;
  color:green;
  display: flex;
  align-items: center;
}

.pcoded-submenu a{
  text-decoration: none;
  font-size: 14px;
}

/*加点击效果*/
.pcoded-submenu a:hover{
  color: #fff;
  color: #ff6600;
}
</style>


