{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\news\\NewsAdd.vue?vue&type=template&id=129f1ecc", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\news\\NewsAdd.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "href", "id", "style", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "$route", "meta", "title", "_hoisted_5", "_hoisted_6", "_createVNode", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_component_el_form_item", "label", "prop", "_component_el_input", "$event", "placeholder", "nimage", "readonly", "_component_el_button", "type", "size", "onClick", "$options", "showUpload", "_component_WangEditor", "content", "config", "_ctx", "editorConfig", "isClear", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "save", "loading", "btnLoading", "icon", "goBack", "_component_el_dialog", "uploadVisible", "onClose", "closeDialog", "_hoisted_10", "_component_el_upload", "action", "drag", "limit", "handlePreview", "handleRemove", "fileList", "handleExceed", "name", "fileListChange", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "hideUpload", "handleConfirm"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\news\\NewsAdd.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-title-box\">\r\n    <ol class=\"breadcrumb float-right\">\r\n      <li class=\"breadcrumb-item\"><a href=\"javascript:void(0);\" id=\"title1\">新闻资讯管理</a></li>\r\n      <li class=\"breadcrumb-item active\" id=\"title2\">{{ this.$route.meta.title }}</li>\r\n    </ol>\r\n    <h4 class=\"page-title\" id=\"title3\">{{ this.$route.meta.title }}</h4>\r\n  </div>\r\n\r\n\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"标题\" prop=\"title\">\r\n<el-input v-model=\"formData.title\" placeholder=\"标题\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item prop=\"nimage\" label=\"图片\"  min-width=\"20%\">\r\n<el-input  v-model=\"formData.nimage\" placeholder=\"图片\"  readonly=\"true\" style=\"width:50%;\" ></el-input>\r\n<el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\r\n</el-form-item>\r\n<el-form-item label=\"内容\" prop=\"content\">\r\n<WangEditor  ref=\"wangEditorRef\" v-model=\"formData.content\" :config=\"editorConfig\"   :isClear=\"isClear\" @change=\"editorChange\"></WangEditor>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n     <el-dialog\n        v-model=\"uploadVisible\"\n        title=\"附件上传\"\n        custom-class=\"el-dialog-widthSmall\"\n        @close=\"closeDialog\"\n      >\n        <div>\n          <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n        </div>\n        <el-upload\n          action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n          style=\"\n            margin: auto;\n            margin-top: 10px;\n            border: 1px solid #dcdfe6;\n            border-radius: 4px;\n          \"\n          drag\n          :limit=\"1\"\n          :on-preview=\"handlePreview\"\n          :on-remove=\"handleRemove\"\n          :file-list=\"fileList\"\n          :on-exceed=\"handleExceed\"\n          :auto-upload=\"false\"\n          name=\"file\"\n          :on-change=\"fileListChange\"\n        >\n          <i class=\"el-icon-upload\"></i>\n          <div class=\"el-upload__text\">\n            将数据文件拖到此处，或<em>点击上传</em>\n          </div>\n          <div class=\"el-upload__tip\">\n            <div\n              style=\"display: inline; color: #d70000; font-size: 14px\"\n              class=\"uploadFileWarning\"\n              id=\"uploadFileWarning\"\n            ></div>\n          </div>\n        </el-upload>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"hideUpload\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n        </span>\n      </el-dialog>\r\n\r\n\r\n\r\n    </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\nimport WangEditor from \"../../../components/WangEditor\";\nexport default {\n  name: 'NewsAdd',\n  components: {\n    WangEditor,\n  },  \n    data() {\n      return {   \n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        addrules: {\r\n          title: [{ required: true, message: '请输入标题', trigger: 'blur' },\r\n],          nimage: [{ required: true, message: '请输入图片', trigger: 'blur' },\r\n],        },\r\n\n      };\n    },\n    mounted() {\r\n    \r\n    },\r\n\r\n \n    methods: {    \n   // 添加\n    save() {       \n         this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n           if (valid) {\n             let url = base + \"/news/add\";\n             this.btnLoading = true;\n             request.post(url, this.formData).then((res) => { //发送请求         \n               if (res.code == 200) {\n                 this.$message({\n                   message: \"操作成功\",\n                   type: \"success\",\n                   offset: 320,\n                 });              \n                this.$router.push({\n                path: \"/NewsManage\",\n                });\n               } else {\n                 this.$message({\n                   message: res.msg,\n                   type: \"error\",\n                   offset: 320,\n                 });\n               }\n               this.btnLoading=false;\n             });\n           }        \n           \n         });\n    },\n    \n       // 返回\n        goBack() {\n          this.$router.push({\n            path: \"/NewsManage\",\n          });\n        },       \n              \n          \n            //显示上传框\n    showUpload() {\n      this.uploadVisible = true;\n    },\n\n    //隐藏上传框\n    hideUpload() {\n      this.uploadVisible = false;\n    },\n    //上传\n    handleRemove(file, fileList) {\n      this.fileList = fileList;\n    },\n    handlePreview(file) {\n      console.log(file);\n    },\n    handleExceed(files, fileList) {\n      this.$message({\n        duration: 1000,\n        message: \"只能上传一个文件\",\n        type: \"error\",\n        offset: 320,\n      });\n    },\n    // 判断上传文件后缀\n    fileListChange(file, fileList) {\n      let extendFileName = \"png,jpg\";\n      let extendFileNames = extendFileName.split(\",\");\n      let regExpRules = [];\n      for (let i = 0; i < extendFileNames.length; i++) {\n        regExpRules.push(\n          new RegExp(\"(.*).(\" + extendFileNames[i] + \")$\", \"gim\")\n        );\n      }\n      let fileNames = [];\n      let files = [];\n      let that = this;\n      fileList.forEach(function (key, val) {\n        let ret = false;\n        for (let i = 0; i < regExpRules.length; i++) {\n          ret = ret || regExpRules[i].test(key[\"name\"]);\n        }\n        if (!ret) {\n          console.log(key[\"name\"] + \":\" + ret);\n          that.$message({\n            duration: 1000,\n            message: \"上传的文件后缀必须为\" + extendFileName + \"格式！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        if (fileNames.indexOf(key[\"name\"]) != -1) {\n          that.$message({\n            duration: 1000,\n            message: \"上传的文件重复！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        //只能上传一个文件，用最后上传的覆盖\n        if (!that.multiFiles) {\n          files = [];\n          fileNames = [];\n        }\n        files.push(key);\n        fileNames.push(key[\"name\"]);\n        if (fileNames !== \"\") {\n          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n        }\n        //$(\".uploadFileWarning\").text(\"\");\n      });\n      this.files = fileNames;\n      this.fileList = files;\n    },\n    /**\n     * 确认按钮\n     */\n    handleConfirm() {\n      let filePath = this.fileList;\n      if (filePath.length === 0) {\n        this.$message({\n          duration: 1000,\n          message: \"请选择文件！\",\n          type: \"error\",\n          offset: 320,\n        });\n        return false;\n      }\n      let formData = new FormData();\n      this.fileList.forEach((file) => {\n        formData.append(\"file\", file.raw, file.raw.name);\n      });\n      let url = base + \"/common/uploadFile\";\n      console.log(\"url=\" + url);\n      request.post(url, formData).then((res) => {\n        console.log(res);\n        let furl = res.resdata.filePath;\n        this.formData.nimage = furl;  // 上传文件的路径\n        this.hideUpload();\n        console.log(res);\n      });\n    },\n\r\n   \n            // 富文本编辑器\n    editorChange(val) {\n      this.formData.content = val;\n    },\r\n   \n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EACrBA,KAAK,EAAC;AAAwB;gCAChCC,mBAAA,CAAqF;EAAjFD,KAAK,EAAC;AAAiB,I,aAACC,mBAAA,CAAoD;EAAjDC,IAAI,EAAC,qBAAqB;EAACC,EAAE,EAAC;GAAS,QAAM,E;;EACxEH,KAAK,EAAC,wBAAwB;EAACG,EAAE,EAAC;;;EAEpCH,KAAK,EAAC,YAAY;EAACG,EAAE,EAAC;;;EAIrBC,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;iDAOL,IAAE;iDAMoC,KAAG;iDAC5B,KAAG;iCASnEH,mBAAA,CAEM,c,aADJA,mBAAA,CAAkC,WAA/B,wBAA2B,E;iCAoB9BA,mBAAA,CAA8B;EAA3BD,KAAK,EAAC;AAAgB;iCACzBC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAiB,I,8BAAC,cAChB,G,aAAAC,mBAAA,CAAa,YAAT,MAAI,E;iCAErBA,mBAAA,CAMM;EANDD,KAAK,EAAC;AAAgB,I,aACzBC,mBAAA,CAIO;EAHLG,KAAwD,EAAxD;IAAA;IAAA;IAAA;EAAA,CAAwD;EACxDJ,KAAK,EAAC,mBAAmB;EACzBG,EAAE,EAAC;;;EAIHH,KAAK,EAAC;AAAe;kDACM,KAAG;kDACe,KAAG;;;;;;;;;6DAnE5DC,mBAAA,CAMM,OANNI,UAMM,GALJJ,mBAAA,CAGK,MAHLK,UAGK,GAFHC,UAAqF,EACrFN,mBAAA,CAAgF,MAAhFO,UAAgF,EAAAC,gBAAA,MAAzBC,MAAM,CAACC,IAAI,CAACC,KAAK,iB,GAE1EX,mBAAA,CAAoE,MAApEY,UAAoE,EAAAJ,gBAAA,MAAzBC,MAAM,CAACC,IAAI,CAACC,KAAK,iB,GAI5DX,mBAAA,CAgEM,OAhENa,UAgEM,GA/DHC,YAAA,CAeGC,kBAAA;IAfOC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAGC,KAAK,EAAC;;sBAC/F,MAEe,CAFfR,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAoF,CAApFX,YAAA,CAAoFY,mBAAA;oBAAjET,KAAA,CAAAC,QAAQ,CAACP,KAAK;mEAAdM,KAAA,CAAAC,QAAQ,CAACP,KAAK,GAAAgB,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAEzB,KAAkB,EAAlB;UAAA;QAAA;;;QAErDW,YAAA,CAGeS,uBAAA;MAHDE,IAAI,EAAC,QAAQ;MAACD,KAAK,EAAC,IAAI;MAAE,WAAS,EAAC;;wBAClD,MAAsG,CAAtGV,YAAA,CAAsGY,mBAAA;oBAAlFT,KAAA,CAAAC,QAAQ,CAACW,MAAM;mEAAfZ,KAAA,CAAAC,QAAQ,CAACW,MAAM,GAAAF,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAEE,QAAQ,EAAC,MAAM;QAAC3B,KAAkB,EAAlB;UAAA;QAAA;+CACvEW,YAAA,CAAyEiB,oBAAA;QAA9DC,IAAI,EAAC,SAAS;QAACC,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAC;;0BAAY,MAAE,C;;;;QAE7DtB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAA4I,CAA5IX,YAAA,CAA4IuB,qBAAA;QAA/HlB,GAAG,EAAC,eAAe;oBAAUF,KAAA,CAAAC,QAAQ,CAACoB,OAAO;mEAAhBrB,KAAA,CAAAC,QAAQ,CAACoB,OAAO,GAAAX,MAAA;QAAGY,MAAM,EAAEC,IAAA,CAAAC,YAAY;QAAKC,OAAO,EAAEF,IAAA,CAAAE,OAAO;QAAGC,QAAM,EAAER,QAAA,CAAAS;;;QAEjH9B,YAAA,CAGeS,uBAAA;wBAFf,MAAgH,CAAhHT,YAAA,CAAgHiB,oBAAA;QAArGC,IAAI,EAAC,SAAS;QAACC,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAU,IAAI;QAAGC,OAAO,EAAE7B,KAAA,CAAA8B,UAAU;QAAEC,IAAI,EAAC;;0BAAiB,MAAG,C;;iDACpGlC,YAAA,CAAuFiB,oBAAA;QAA5EC,IAAI,EAAC,MAAM;QAACC,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAc,MAAM;QAAED,IAAI,EAAC;;0BAAe,MAAG,C;;;;;;;yCAGtElC,YAAA,CA2CaoC,oBAAA;gBA1CDjC,KAAA,CAAAkC,aAAa;+DAAblC,KAAA,CAAAkC,aAAa,GAAAxB,MAAA;IACtBhB,KAAK,EAAC,MAAM;IACZ,cAAY,EAAC,sBAAsB;IAClCyC,OAAK,EAAEZ,IAAA,CAAAa;;sBAER,MAEM,CAFNC,WAEM,EACNxC,YAAA,CA6BYyC,oBAAA;MA5BVC,MAAM,EAAC,mDAAmD;MAC1DrD,KAKC,EALD;QAAA;QAAA;QAAA;QAAA;MAAA,CAKC;MACDsD,IAAI,EAAJ,EAAI;MACHC,KAAK,EAAE,CAAC;MACR,YAAU,EAAEvB,QAAA,CAAAwB,aAAa;MACzB,WAAS,EAAExB,QAAA,CAAAyB,YAAY;MACvB,WAAS,EAAEpB,IAAA,CAAAqB,QAAQ;MACnB,WAAS,EAAE1B,QAAA,CAAA2B,YAAY;MACvB,aAAW,EAAE,KAAK;MACnBC,IAAI,EAAC,MAAM;MACV,WAAS,EAAE5B,QAAA,CAAA6B;;wBAEZ,MAA8B,CAA9BC,WAA8B,EAC9BC,WAEM,EACNC,WAMM,C;;2FAERnE,mBAAA,CAGO,QAHPoE,WAGO,GAFLtD,YAAA,CAA8CiB,oBAAA;MAAlCG,OAAK,EAAEC,QAAA,CAAAkC;IAAU;wBAAE,MAAG,C;;oCAClCvD,YAAA,CAAgEiB,oBAAA;MAArDC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAEC,QAAA,CAAAmC;;wBAAe,MAAG,C"}]}