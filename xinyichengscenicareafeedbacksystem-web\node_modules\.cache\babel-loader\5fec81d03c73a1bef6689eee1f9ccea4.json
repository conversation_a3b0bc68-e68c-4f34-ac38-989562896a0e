{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue", "mtime": 1749041655057}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "data", "questionsList", "answers", "loading", "submitMessage", "submitSuccess", "isLoggedIn", "hasCompleted", "showQuestionnaire", "userAccount", "created", "checkLoginStatus", "methods", "sessionStorage", "getItem", "checkCompletionStatus", "url", "post", "then", "res", "code", "resdata", "getQuestions", "catch", "error", "console", "goToLogin", "$router", "push", "viewStatistics", "retakeQuestionnaire", "$confirm", "confirmButtonText", "cancelButtonText", "type", "para", "params", "currentPage", "pageSize", "for<PERSON>ach", "question", "$set", "qid", "submitQuestionnaire", "unansweredQuestions", "filter", "q", "length", "results", "status", "account", "successCount", "failCount", "result", "setTimeout"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue"], "sourcesContent": ["<template>\r\n  <div class=\"questionnaire-container\">\r\n    <h2 class=\"questionnaire-title\">调查问卷</h2>\r\n\r\n    <!-- 未登录提示 -->\r\n    <div v-if=\"!isLoggedIn\" class=\"login-prompt\">\r\n      <el-alert\r\n        title=\"请先登录\"\r\n        description=\"您需要登录后才能参与问卷调查\"\r\n        type=\"warning\"\r\n        show-icon\r\n        :closable=\"false\">\r\n      </el-alert>\r\n      <div class=\"login-actions\">\r\n        <el-button type=\"primary\" @click=\"goToLogin\">去登录</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 已完成问卷提示 -->\r\n    <div v-else-if=\"hasCompleted\" class=\"completed-prompt\">\r\n      <el-alert\r\n        title=\"您已完成问卷调查\"\r\n        description=\"感谢您的参与！您可以查看统计结果或重新填写问卷\"\r\n        type=\"success\"\r\n        show-icon\r\n        :closable=\"false\">\r\n      </el-alert>\r\n      <div class=\"completed-actions\">\r\n        <el-button type=\"primary\" @click=\"viewStatistics\">查看统计结果</el-button>\r\n        <el-button type=\"warning\" @click=\"retakeQuestionnaire\">重新填写问卷</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 问卷表单 -->\r\n    <div v-else-if=\"showQuestionnaire\">\r\n      <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n\r\n      <div v-else>\r\n        <div v-for=\"(question, index) in questionsList\" :key=\"question.qid\" class=\"question-item\">\r\n          <div class=\"question-text\">{{ index + 1 }}. {{ question.question }}</div>\r\n          <div class=\"options\">\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"非常满意\" /> 非常满意\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"满意\" /> 满意\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"一般\" /> 一般\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"不满意\" /> 不满意\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"submit-container\">\r\n          <button @click=\"submitQuestionnaire\" class=\"submit-btn\">提交问卷</button>\r\n        </div>\r\n\r\n        <div v-if=\"submitMessage\" class=\"message\" :class=\"{ 'success': submitSuccess, 'error': !submitSuccess }\">\r\n          {{ submitMessage }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"QuestionnaireView\",\r\n  data() {\r\n    return {\r\n      questionsList: [],\r\n      answers: {},\r\n      loading: true,\r\n      submitMessage: '',\r\n      submitSuccess: false,\r\n      isLoggedIn: false,\r\n      hasCompleted: false,\r\n      showQuestionnaire: false,\r\n      userAccount: ''\r\n    };\r\n  },\r\n  created() {\r\n    this.checkLoginStatus();\r\n  },\r\n  methods: {\r\n    // 检查登录状态\r\n    checkLoginStatus() {\r\n      this.userAccount = sessionStorage.getItem(\"lname\");\r\n      this.isLoggedIn = !!this.userAccount;\r\n\r\n      if (this.isLoggedIn) {\r\n        this.checkCompletionStatus();\r\n      }\r\n    },\r\n\r\n    // 检查用户是否已完成问卷\r\n    checkCompletionStatus() {\r\n      let url = base + \"/results/checkCompleted?account=\" + this.userAccount;\r\n\r\n      request.post(url, {}).then((res) => {\r\n        if (res.code === 200) {\r\n          this.hasCompleted = res.resdata;\r\n          if (!this.hasCompleted) {\r\n            this.showQuestionnaire = true;\r\n            this.getQuestions();\r\n          }\r\n        } else {\r\n          // 检查失败，默认显示问卷\r\n          this.showQuestionnaire = true;\r\n          this.getQuestions();\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"检查完成状态失败:\", error);\r\n        // 检查失败，默认显示问卷\r\n        this.showQuestionnaire = true;\r\n        this.getQuestions();\r\n      });\r\n    },\r\n\r\n    // 去登录\r\n    goToLogin() {\r\n      this.$router.push('/ulogin');\r\n    },\r\n\r\n    // 查看统计结果\r\n    viewStatistics() {\r\n      // 创建一个新的路由来显示统计结果\r\n      this.$router.push('/questionnaireStatistics');\r\n    },\r\n\r\n    // 重新填写问卷\r\n    retakeQuestionnaire() {\r\n      this.$confirm('确定要重新填写问卷吗？这将清除您之前的答案。', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.hasCompleted = false;\r\n        this.showQuestionnaire = true;\r\n        this.getQuestions();\r\n      }).catch(() => {\r\n        // 用户取消\r\n      });\r\n    },\r\n\r\n    // 获取问卷问题列表\r\n    getQuestions() {\r\n      this.loading = true;\r\n      let url = base + \"/questions/list\";\r\n      let para = {};\r\n      \r\n      request.post(url, para, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\r\n        if (res.resdata) {\r\n          this.questionsList = res.resdata;\r\n          // 初始化答案对象\r\n          this.questionsList.forEach(question => {\r\n            this.$set(this.answers, question.qid, '');\r\n          });\r\n        }\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error(\"获取问卷问题失败:\", error);\r\n        this.loading = false;\r\n      });\r\n    },\r\n    \r\n    // 提交问卷\r\n    submitQuestionnaire() {\r\n      // 检查是否所有问题都已回答\r\n      const unansweredQuestions = this.questionsList.filter(q => !this.answers[q.qid]);\r\n      \r\n      if (unansweredQuestions.length > 0) {\r\n        this.submitMessage = `请回答所有问题后再提交`;\r\n        this.submitSuccess = false;\r\n        return;\r\n      }\r\n      \r\n      // 准备提交数据\r\n      const results = [];\r\n      \r\n      // 将每个问题的答案转换为提交格式\r\n      this.questionsList.forEach(question => {\r\n        results.push({\r\n          qid: question.qid,\r\n          question: question.question,\r\n          status: this.answers[question.qid],\r\n          account: sessionStorage.getItem(\"lname\"),\r\n        });\r\n      });\r\n      \r\n      // 显示提交中状态\r\n      this.submitMessage = \"正在提交...\";\r\n      this.submitSuccess = true;\r\n      \r\n      // 一次性提交所有答案\r\n      let successCount = 0;\r\n      let failCount = 0;\r\n      \r\n      // 逐个提交答案并检查每个请求的响应\r\n      results.forEach(result => {\r\n        request.post(base + \"/results/add\", result)\r\n          .then(res => {\r\n            successCount++;\r\n            // 如果所有请求都已完成\r\n            if (successCount + failCount === results.length) {\r\n              if (failCount === 0) {\r\n                this.submitMessage = \"问卷提交成功，感谢您的反馈！\";\r\n                this.submitSuccess = true;\r\n                // 清空答案\r\n                this.answers = {};\r\n                this.questionsList.forEach(question => {\r\n                  this.$set(this.answers, question.qid, '');\r\n                });\r\n                // 更新完成状态\r\n                setTimeout(() => {\r\n                  this.hasCompleted = true;\r\n                  this.showQuestionnaire = false;\r\n                }, 2000);\r\n              } else {\r\n                this.submitMessage = `部分提交成功，${failCount}个问题提交失败`;\r\n                this.submitSuccess = false;\r\n              }\r\n            }\r\n          })\r\n          .catch(error => {\r\n            failCount++;\r\n            console.error(\"提交问题失败:\", error);\r\n            if (successCount + failCount === results.length) {\r\n              this.submitMessage = `部分提交成功，${failCount}个问题提交失败`;\r\n              this.submitSuccess = false;\r\n            }\r\n          });\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.questionnaire-container {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.questionnaire-title {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  font-size: 24px;\r\n}\r\n\r\n.loading {\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 16px;\r\n}\r\n\r\n.question-item {\r\n  margin-bottom: 25px;\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.question-text {\r\n  font-size: 16px;\r\n  margin-bottom: 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-left: 20px;\r\n}\r\n\r\n.option {\r\n  margin-right: 30px;\r\n  margin-bottom: 10px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.option input {\r\n  margin-right: 5px;\r\n}\r\n\r\n.submit-container {\r\n  text-align: center;\r\n  margin-top: 30px;\r\n}\r\n\r\n.submit-btn {\r\n  padding: 10px 30px;\r\n  background-color: #409EFF;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n}\r\n\r\n.submit-btn:hover {\r\n  background-color: #66b1ff;\r\n}\r\n\r\n.message {\r\n  text-align: center;\r\n  margin-top: 20px;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.success {\r\n  background-color: #f0f9eb;\r\n  color: #67c23a;\r\n  border: 1px solid #c2e7b0;\r\n}\r\n\r\n.error {\r\n  background-color: #fef0f0;\r\n  color: #f56c6c;\r\n  border: 1px solid #fbc4c4;\r\n}\r\n\r\n.login-prompt, .completed-prompt {\r\n  text-align: center;\r\n  padding: 30px;\r\n}\r\n\r\n.login-actions, .completed-actions {\r\n  margin-top: 20px;\r\n}\r\n\r\n.login-actions .el-button, .completed-actions .el-button {\r\n  margin: 0 10px;\r\n}\r\n</style>\r\n\r\n\r\n\r\n"], "mappings": ";AAqEA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,CAAC,CAAC;MACXC,OAAO,EAAE,IAAI;MACbC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,KAAK;MACnBC,iBAAiB,EAAE,KAAK;MACxBC,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,gBAAgB,CAAC,CAAC;EACzB,CAAC;EACDC,OAAO,EAAE;IACP;IACAD,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACF,WAAU,GAAII,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;MAClD,IAAI,CAACR,UAAS,GAAI,CAAC,CAAC,IAAI,CAACG,WAAW;MAEpC,IAAI,IAAI,CAACH,UAAU,EAAE;QACnB,IAAI,CAACS,qBAAqB,CAAC,CAAC;MAC9B;IACF,CAAC;IAED;IACAA,qBAAqBA,CAAA,EAAG;MACtB,IAAIC,GAAE,GAAIlB,IAAG,GAAI,kCAAiC,GAAI,IAAI,CAACW,WAAW;MAEtEZ,OAAO,CAACoB,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAIA,GAAG,CAACC,IAAG,KAAM,GAAG,EAAE;UACpB,IAAI,CAACb,YAAW,GAAIY,GAAG,CAACE,OAAO;UAC/B,IAAI,CAAC,IAAI,CAACd,YAAY,EAAE;YACtB,IAAI,CAACC,iBAAgB,GAAI,IAAI;YAC7B,IAAI,CAACc,YAAY,CAAC,CAAC;UACrB;QACF,OAAO;UACL;UACA,IAAI,CAACd,iBAAgB,GAAI,IAAI;UAC7B,IAAI,CAACc,YAAY,CAAC,CAAC;QACrB;MACF,CAAC,CAAC,CAACC,KAAK,CAACC,KAAI,IAAK;QAChBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;QACA,IAAI,CAAChB,iBAAgB,GAAI,IAAI;QAC7B,IAAI,CAACc,YAAY,CAAC,CAAC;MACrB,CAAC,CAAC;IACJ,CAAC;IAED;IACAI,SAASA,CAAA,EAAG;MACV,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED;IACAC,cAAcA,CAAA,EAAG;MACf;MACA,IAAI,CAACF,OAAO,CAACC,IAAI,CAAC,0BAA0B,CAAC;IAC/C,CAAC;IAED;IACAE,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACC,QAAQ,CAAC,wBAAwB,EAAE,IAAI,EAAE;QAC5CC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAChB,IAAI,CAAC,MAAM;QACZ,IAAI,CAACX,YAAW,GAAI,KAAK;QACzB,IAAI,CAACC,iBAAgB,GAAI,IAAI;QAC7B,IAAI,CAACc,YAAY,CAAC,CAAC;MACrB,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM;QACb;MAAA,CACD,CAAC;IACJ,CAAC;IAED;IACAD,YAAYA,CAAA,EAAG;MACb,IAAI,CAACnB,OAAM,GAAI,IAAI;MACnB,IAAIa,GAAE,GAAIlB,IAAG,GAAI,iBAAiB;MAClC,IAAIqC,IAAG,GAAI,CAAC,CAAC;MAEbtC,OAAO,CAACoB,IAAI,CAACD,GAAG,EAAEmB,IAAI,EAAE;QAAEC,MAAM,EAAE;UAAEC,WAAW,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAI;MAAE,CAAC,CAAC,CAACpB,IAAI,CAAEC,GAAG,IAAK;QACnF,IAAIA,GAAG,CAACE,OAAO,EAAE;UACf,IAAI,CAACpB,aAAY,GAAIkB,GAAG,CAACE,OAAO;UAChC;UACA,IAAI,CAACpB,aAAa,CAACsC,OAAO,CAACC,QAAO,IAAK;YACrC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACvC,OAAO,EAAEsC,QAAQ,CAACE,GAAG,EAAE,EAAE,CAAC;UAC3C,CAAC,CAAC;QACJ;QACA,IAAI,CAACvC,OAAM,GAAI,KAAK;MACtB,CAAC,CAAC,CAACoB,KAAK,CAACC,KAAI,IAAK;QAChBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACrB,OAAM,GAAI,KAAK;MACtB,CAAC,CAAC;IACJ,CAAC;IAED;IACAwC,mBAAmBA,CAAA,EAAG;MACpB;MACA,MAAMC,mBAAkB,GAAI,IAAI,CAAC3C,aAAa,CAAC4C,MAAM,CAACC,CAAA,IAAK,CAAC,IAAI,CAAC5C,OAAO,CAAC4C,CAAC,CAACJ,GAAG,CAAC,CAAC;MAEhF,IAAIE,mBAAmB,CAACG,MAAK,GAAI,CAAC,EAAE;QAClC,IAAI,CAAC3C,aAAY,GAAK,aAAY;QAClC,IAAI,CAACC,aAAY,GAAI,KAAK;QAC1B;MACF;;MAEA;MACA,MAAM2C,OAAM,GAAI,EAAE;;MAElB;MACA,IAAI,CAAC/C,aAAa,CAACsC,OAAO,CAACC,QAAO,IAAK;QACrCQ,OAAO,CAACpB,IAAI,CAAC;UACXc,GAAG,EAAEF,QAAQ,CAACE,GAAG;UACjBF,QAAQ,EAAEA,QAAQ,CAACA,QAAQ;UAC3BS,MAAM,EAAE,IAAI,CAAC/C,OAAO,CAACsC,QAAQ,CAACE,GAAG,CAAC;UAClCQ,OAAO,EAAErC,cAAc,CAACC,OAAO,CAAC,OAAO;QACzC,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACA,IAAI,CAACV,aAAY,GAAI,SAAS;MAC9B,IAAI,CAACC,aAAY,GAAI,IAAI;;MAEzB;MACA,IAAI8C,YAAW,GAAI,CAAC;MACpB,IAAIC,SAAQ,GAAI,CAAC;;MAEjB;MACAJ,OAAO,CAACT,OAAO,CAACc,MAAK,IAAK;QACxBxD,OAAO,CAACoB,IAAI,CAACnB,IAAG,GAAI,cAAc,EAAEuD,MAAM,EACvCnC,IAAI,CAACC,GAAE,IAAK;UACXgC,YAAY,EAAE;UACd;UACA,IAAIA,YAAW,GAAIC,SAAQ,KAAMJ,OAAO,CAACD,MAAM,EAAE;YAC/C,IAAIK,SAAQ,KAAM,CAAC,EAAE;cACnB,IAAI,CAAChD,aAAY,GAAI,gBAAgB;cACrC,IAAI,CAACC,aAAY,GAAI,IAAI;cACzB;cACA,IAAI,CAACH,OAAM,GAAI,CAAC,CAAC;cACjB,IAAI,CAACD,aAAa,CAACsC,OAAO,CAACC,QAAO,IAAK;gBACrC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACvC,OAAO,EAAEsC,QAAQ,CAACE,GAAG,EAAE,EAAE,CAAC;cAC3C,CAAC,CAAC;cACF;cACAY,UAAU,CAAC,MAAM;gBACf,IAAI,CAAC/C,YAAW,GAAI,IAAI;gBACxB,IAAI,CAACC,iBAAgB,GAAI,KAAK;cAChC,CAAC,EAAE,IAAI,CAAC;YACV,OAAO;cACL,IAAI,CAACJ,aAAY,GAAK,UAASgD,SAAU,SAAQ;cACjD,IAAI,CAAC/C,aAAY,GAAI,KAAK;YAC5B;UACF;QACF,CAAC,EACAkB,KAAK,CAACC,KAAI,IAAK;UACd4B,SAAS,EAAE;UACX3B,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B,IAAI2B,YAAW,GAAIC,SAAQ,KAAMJ,OAAO,CAACD,MAAM,EAAE;YAC/C,IAAI,CAAC3C,aAAY,GAAK,UAASgD,SAAU,SAAQ;YACjD,IAAI,CAAC/C,aAAY,GAAI,KAAK;UAC5B;QACF,CAAC,CAAC;MACN,CAAC,CAAC;IACJ;EACF;AACF,CAAC"}]}