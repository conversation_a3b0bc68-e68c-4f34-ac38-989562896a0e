<template>
  <div class="page-title-box">
    <ol class="breadcrumb float-right">
      <li class="breadcrumb-item"><a href="javascript:void(0);" id="title1">调查问卷管理</a></li>
      <li class="breadcrumb-item active" id="title2">{{ this.$route.meta.title }}</li>
    </ol>
    <h4 class="page-title" id="title3">{{ this.$route.meta.title }}</h4>
  </div>

  <div style="width: 100%;line-height: 30px;text-align: left;">
    <div v-if="loading" class="loading">
      <el-loading text="正在加载统计数据..."></el-loading>
    </div>
    
    <div v-else>
      <div v-if="statisticsData.length === 0" class="no-data">
        <el-empty description="暂无问卷统计数据"></el-empty>
      </div>
      
      <div v-else>
        <div v-for="(question, index) in statisticsData" :key="question.qid" class="chart-container">
          <h3 class="question-title">{{ index + 1 }}. {{ question.question }}</h3>
          <div :id="'chart-' + question.qid" class="chart" :style="chartStyle"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
import * as echarts from "echarts";

export default {
  name: 'QuestionsStatistics',
  data() {
    return {
      loading: true,
      statisticsData: [],
      chartStyle: { 
        width: "100%", 
        height: "400px",
        marginBottom: "30px"
      },
      charts: [] // 存储图表实例
    };
  },
  
  mounted() {
    this.getStatisticsData();
  },
  
  beforeDestroy() {
    // 销毁所有图表实例
    this.charts.forEach(chart => {
      if (chart) {
        chart.dispose();
      }
    });
  },
  
  methods: {
    // 获取统计数据
    getStatisticsData() {
      this.loading = true;
      let url = base + "/results/statistics";
      
      request.post(url, {}).then((res) => {
        if (res.code === 200 && res.resdata) {
          this.statisticsData = res.resdata;
          this.$nextTick(() => {
            this.initAllCharts();
          });
        } else {
          this.$message({
            message: "获取统计数据失败",
            type: "error",
            offset: 320,
          });
        }
        this.loading = false;
      }).catch(error => {
        console.error("获取统计数据失败:", error);
        this.$message({
          message: "获取统计数据失败",
          type: "error",
          offset: 320,
        });
        this.loading = false;
      });
    },
    
    // 初始化所有图表
    initAllCharts() {
      this.statisticsData.forEach((question, index) => {
        this.initChart(question, index);
      });
    },
    
    // 初始化单个图表
    initChart(questionData, index) {
      const chartId = 'chart-' + questionData.qid;
      const chartDom = document.getElementById(chartId);
      
      if (!chartDom) {
        console.error(`Chart container not found: ${chartId}`);
        return;
      }
      
      const chart = echarts.init(chartDom);
      this.charts.push(chart);
      
      // 准备饼图数据
      const pieData = questionData.options || [];
      const legendData = pieData.map(item => item.name);
      
      const option = {
        title: {
          text: `问题 ${index + 1} 统计`,
          left: 'center',
          top: 20,
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle',
          data: legendData
        },
        series: [
          {
            name: '选项统计',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}: {c} ({d}%)'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: pieData
          }
        ]
      };
      
      chart.setOption(option);
      
      // 响应式调整
      window.addEventListener('resize', () => {
        chart.resize();
      });
    }
  }
};
</script>

<style scoped>
.loading {
  text-align: center;
  padding: 50px;
}

.no-data {
  text-align: center;
  padding: 50px;
}

.chart-container {
  margin-bottom: 50px;
  padding: 20px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  background-color: #fff;
}

.question-title {
  margin-bottom: 20px;
  color: #333;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}

.chart {
  margin: 0 auto;
}
</style>
