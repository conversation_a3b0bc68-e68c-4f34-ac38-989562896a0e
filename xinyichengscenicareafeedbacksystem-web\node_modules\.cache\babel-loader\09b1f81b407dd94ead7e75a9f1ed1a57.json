{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionnaireStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionnaireStatistics.vue", "mtime": 1749040548672}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "echarts", "name", "data", "loading", "statisticsData", "totalCount", "created", "getStatistics", "methods", "url", "post", "then", "res", "resdata", "length", "reduce", "sum", "item", "value", "$nextTick", "<PERSON><PERSON><PERSON>", "catch", "error", "console", "$message", "message", "type", "offset", "chartDom", "document", "getElementById", "myChart", "init", "option", "title", "text", "left", "textStyle", "fontSize", "fontWeight", "tooltip", "trigger", "formatter", "legend", "orient", "top", "series", "radius", "center", "emphasis", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "label", "show", "color", "setOption", "window", "addEventListener", "resize", "getPercentage", "toFixed"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionnaireStatistics.vue"], "sourcesContent": ["<template>\n  <div class=\"questionnaire-statistics\">\n    <div class=\"page-header\">\n      <h2>问卷调查统计</h2>\n    </div>\n    \n    <div v-loading=\"loading\" class=\"statistics-content\">\n      <div v-if=\"statisticsData.length > 0\" class=\"chart-container\">\n        <div id=\"statisticsChart\" style=\"width: 100%; height: 500px;\"></div>\n      </div>\n      \n      <div v-else-if=\"!loading\" class=\"no-data\">\n        <p>暂无问卷统计数据</p>\n      </div>\n      \n      <!-- 统计表格 -->\n      <div v-if=\"statisticsData.length > 0\" class=\"statistics-table\" style=\"margin-top: 30px;\">\n        <h3>详细统计数据</h3>\n        <el-table :data=\"statisticsData\" border stripe style=\"width: 100%\">\n          <el-table-column prop=\"name\" label=\"满意度\" align=\"center\"></el-table-column>\n          <el-table-column prop=\"value\" label=\"数量\" align=\"center\"></el-table-column>\n          <el-table-column label=\"占比\" align=\"center\">\n            <template #default=\"scope\">\n              {{ getPercentage(scope.row.value) }}%\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nimport * as echarts from 'echarts';\n\nexport default {\n  name: \"QuestionnaireStatistics\",\n  data() {\n    return {\n      loading: false,\n      statisticsData: [],\n      totalCount: 0\n    };\n  },\n  created() {\n    this.getStatistics();\n  },\n  methods: {\n    // 获取统计数据\n    getStatistics() {\n      this.loading = true;\n      let url = base + \"/results/statistics\";\n      request.post(url).then((res) => {\n        this.loading = false;\n        if (res.resdata && res.resdata.length > 0) {\n          this.statisticsData = res.resdata;\n          this.totalCount = this.statisticsData.reduce((sum, item) => sum + item.value, 0);\n          this.$nextTick(() => {\n            this.renderChart();\n          });\n        }\n      }).catch(error => {\n        this.loading = false;\n        console.error(\"获取统计数据失败:\", error);\n        this.$message({\n          message: \"获取统计数据失败\",\n          type: \"error\",\n          offset: 320,\n        });\n      });\n    },\n\n    // 渲染饼图\n    renderChart() {\n      const chartDom = document.getElementById('statisticsChart');\n      if (!chartDom) return;\n      \n      const myChart = echarts.init(chartDom);\n      const option = {\n        title: {\n          text: '问卷调查满意度统计',\n          left: 'center',\n          textStyle: {\n            fontSize: 18,\n            fontWeight: 'bold'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 'left',\n          top: 'middle'\n        },\n        series: [\n          {\n            name: '满意度统计',\n            type: 'pie',\n            radius: ['40%', '70%'],\n            center: ['60%', '50%'],\n            data: this.statisticsData,\n            emphasis: {\n              itemStyle: {\n                shadowBlur: 10,\n                shadowOffsetX: 0,\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\n              }\n            },\n            label: {\n              show: true,\n              formatter: '{b}: {c}\\n({d}%)'\n            }\n          }\n        ],\n        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4']\n      };\n      myChart.setOption(option);\n      \n      // 响应式处理\n      window.addEventListener('resize', () => {\n        myChart.resize();\n      });\n    },\n\n    // 计算百分比\n    getPercentage(value) {\n      if (this.totalCount === 0) return 0;\n      return ((value / this.totalCount) * 100).toFixed(1);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.questionnaire-statistics {\n  padding: 20px;\n}\n\n.page-header {\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #eee;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.statistics-content {\n  background: #fff;\n  padding: 20px;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.chart-container {\n  text-align: center;\n}\n\n.no-data {\n  text-align: center;\n  padding: 50px;\n  color: #999;\n  font-size: 16px;\n}\n\n.statistics-table {\n  margin-top: 30px;\n}\n\n.statistics-table h3 {\n  margin-bottom: 15px;\n  color: #333;\n}\n</style>\n"], "mappings": ";AAiCA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,OAAO,KAAKC,OAAM,MAAO,SAAS;AAElC,eAAe;EACbC,IAAI,EAAE,yBAAyB;EAC/BC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,KAAK;MACdC,cAAc,EAAE,EAAE;MAClBC,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB,CAAC;EACDC,OAAO,EAAE;IACP;IACAD,aAAaA,CAAA,EAAG;MACd,IAAI,CAACJ,OAAM,GAAI,IAAI;MACnB,IAAIM,GAAE,GAAIV,IAAG,GAAI,qBAAqB;MACtCD,OAAO,CAACY,IAAI,CAACD,GAAG,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;QAC9B,IAAI,CAACT,OAAM,GAAI,KAAK;QACpB,IAAIS,GAAG,CAACC,OAAM,IAAKD,GAAG,CAACC,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UACzC,IAAI,CAACV,cAAa,GAAIQ,GAAG,CAACC,OAAO;UACjC,IAAI,CAACR,UAAS,GAAI,IAAI,CAACD,cAAc,CAACW,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAE,GAAIC,IAAI,CAACC,KAAK,EAAE,CAAC,CAAC;UAChF,IAAI,CAACC,SAAS,CAAC,MAAM;YACnB,IAAI,CAACC,WAAW,CAAC,CAAC;UACpB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAACC,KAAK,CAACC,KAAI,IAAK;QAChB,IAAI,CAACnB,OAAM,GAAI,KAAK;QACpBoB,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACE,QAAQ,CAAC;UACZC,OAAO,EAAE,UAAU;UACnBC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAP,WAAWA,CAAA,EAAG;MACZ,MAAMQ,QAAO,GAAIC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;MAC3D,IAAI,CAACF,QAAQ,EAAE;MAEf,MAAMG,OAAM,GAAI/B,OAAO,CAACgC,IAAI,CAACJ,QAAQ,CAAC;MACtC,MAAMK,MAAK,GAAI;QACbC,KAAK,EAAE;UACLC,IAAI,EAAE,WAAW;UACjBC,IAAI,EAAE,QAAQ;UACdC,SAAS,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE;UACd;QACF,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE;QACb,CAAC;QACDC,MAAM,EAAE;UACNC,MAAM,EAAE,UAAU;UAClBR,IAAI,EAAE,MAAM;UACZS,GAAG,EAAE;QACP,CAAC;QACDC,MAAM,EAAE,CACN;UACE7C,IAAI,EAAE,OAAO;UACbyB,IAAI,EAAE,KAAK;UACXqB,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtB9C,IAAI,EAAE,IAAI,CAACE,cAAc;UACzB6C,QAAQ,EAAE;YACRC,SAAS,EAAE;cACTC,UAAU,EAAE,EAAE;cACdC,aAAa,EAAE,CAAC;cAChBC,WAAW,EAAE;YACf;UACF,CAAC;UACDC,KAAK,EAAE;YACLC,IAAI,EAAE,IAAI;YACVb,SAAS,EAAE;UACb;QACF,EACD;QACDc,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;MAChG,CAAC;MACDzB,OAAO,CAAC0B,SAAS,CAACxB,MAAM,CAAC;;MAEzB;MACAyB,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;QACtC5B,OAAO,CAAC6B,MAAM,CAAC,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,aAAaA,CAAC3C,KAAK,EAAE;MACnB,IAAI,IAAI,CAACb,UAAS,KAAM,CAAC,EAAE,OAAO,CAAC;MACnC,OAAO,CAAEa,KAAI,GAAI,IAAI,CAACb,UAAU,GAAI,GAAG,EAAEyD,OAAO,CAAC,CAAC,CAAC;IACrD;EACF;AACF,CAAC"}]}