{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Foot.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Foot.vue", "mtime": 1747056618959}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiRm9vdCIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CgogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CgogIH0sCiAgbWV0aG9kczogeyAgCgogIH0sCn07Cg=="}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Foot.vue"], "names": [], "mappings": ";AA2BA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEP,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEV,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAET,CAAC;AACH,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/components/Foot.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  \n   \n\n\n        <footer>\n            <div class=\"container\">\n                <div class=\"row\">\n                    <div class=\"col-md-6\">\n                        <p>&copy; 辽宁心怡程景区意见反馈系统 版权所有</p>\n                    </div>\n                    <div class=\"col-md-6 text-right\">\n                        <a href=\"/login\" class=\"btn btn-link \" style=\"color: #fff;\">管理员登录</a>\n                    </div>\n                </div>\n            </div>\n        </footer>\n        <!--客服面板-->\n        <div id=\"cmsFloatPanel\">\n            <div class=\"ctrolPanel2\">  \n                <a href=\"#\" style=\"color: #fff; text-decoration: none;\">Top</a>\n            </div>          \n           \n        </div>\n      \n</template>\n<script>\nexport default {\n  name: \"Foot\",\n  data() {\n    return {\n\n    };\n  },\n  mounted() {\n\n  },\n  methods: {  \n\n  },\n};\n</script>\n\n\n\n"]}]}