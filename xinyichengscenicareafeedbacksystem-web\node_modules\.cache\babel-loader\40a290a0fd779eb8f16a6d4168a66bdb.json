{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\QuestionnaireStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\QuestionnaireStatistics.vue", "mtime": 1749041571658}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "echarts", "name", "data", "loading", "statisticsData", "chartStyle", "width", "height", "marginBottom", "charts", "mounted", "getStatisticsData", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "chart", "dispose", "methods", "url", "post", "then", "res", "code", "resdata", "$nextTick", "initAllCharts", "$message", "message", "type", "offset", "catch", "error", "console", "question", "index", "initChart", "questionData", "chartId", "qid", "chartDom", "document", "getElementById", "init", "push", "pieData", "options", "legendData", "map", "item", "colors", "option", "title", "text", "left", "top", "textStyle", "fontSize", "fontWeight", "tooltip", "trigger", "formatter", "legend", "orient", "bottom", "color", "series", "radius", "center", "avoidLabelOverlap", "label", "show", "position", "emphasis", "labelLine", "setOption", "window", "addEventListener", "resize", "goBack", "$router", "go", "retakeQuestionnaire", "$confirm", "confirmButtonText", "cancelButtonText"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\QuestionnaireStatistics.vue"], "sourcesContent": ["<template>\n  <div class=\"statistics-container\">\n    <h2 class=\"statistics-title\">您的问卷调查统计结果</h2>\n    \n    <div v-if=\"loading\" class=\"loading\">\n      <el-loading text=\"正在加载统计数据...\"></el-loading>\n    </div>\n    \n    <div v-else>\n      <div v-if=\"statisticsData.length === 0\" class=\"no-data\">\n        <el-empty description=\"暂无统计数据\"></el-empty>\n      </div>\n      \n      <div v-else>\n        <div class=\"intro-text\">\n          <p>感谢您参与我们的问卷调查！以下是所有用户的统计结果：</p>\n        </div>\n        \n        <div v-for=\"(question, index) in statisticsData\" :key=\"question.qid\" class=\"chart-container\">\n          <h3 class=\"question-title\">{{ index + 1 }}. {{ question.question }}</h3>\n          <div :id=\"'user-chart-' + question.qid\" class=\"chart\" :style=\"chartStyle\"></div>\n        </div>\n        \n        <div class=\"action-buttons\">\n          <el-button type=\"primary\" @click=\"goBack\">返回</el-button>\n          <el-button type=\"success\" @click=\"retakeQuestionnaire\">重新填写问卷</el-button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../utils/http\";\nimport * as echarts from \"echarts\";\n\nexport default {\n  name: 'QuestionnaireStatistics',\n  data() {\n    return {\n      loading: true,\n      statisticsData: [],\n      chartStyle: { \n        width: \"100%\", \n        height: \"350px\",\n        marginBottom: \"20px\"\n      },\n      charts: [] // 存储图表实例\n    };\n  },\n  \n  mounted() {\n    this.getStatisticsData();\n  },\n  \n  beforeDestroy() {\n    // 销毁所有图表实例\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.dispose();\n      }\n    });\n  },\n  \n  methods: {\n    // 获取统计数据\n    getStatisticsData() {\n      this.loading = true;\n      let url = base + \"/results/statistics\";\n      \n      request.post(url, {}).then((res) => {\n        if (res.code === 200 && res.resdata) {\n          this.statisticsData = res.resdata;\n          this.$nextTick(() => {\n            this.initAllCharts();\n          });\n        } else {\n          this.$message({\n            message: \"获取统计数据失败\",\n            type: \"error\",\n            offset: 320,\n          });\n        }\n        this.loading = false;\n      }).catch(error => {\n        console.error(\"获取统计数据失败:\", error);\n        this.$message({\n          message: \"获取统计数据失败\",\n          type: \"error\",\n          offset: 320,\n        });\n        this.loading = false;\n      });\n    },\n    \n    // 初始化所有图表\n    initAllCharts() {\n      this.statisticsData.forEach((question, index) => {\n        this.initChart(question, index);\n      });\n    },\n    \n    // 初始化单个图表\n    initChart(questionData, index) {\n      const chartId = 'user-chart-' + questionData.qid;\n      const chartDom = document.getElementById(chartId);\n      \n      if (!chartDom) {\n        console.error(`Chart container not found: ${chartId}`);\n        return;\n      }\n      \n      const chart = echarts.init(chartDom);\n      this.charts.push(chart);\n      \n      // 准备饼图数据\n      const pieData = questionData.options || [];\n      const legendData = pieData.map(item => item.name);\n      \n      // 定义颜色方案\n      const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];\n      \n      const option = {\n        title: {\n          text: `问题 ${index + 1} 统计结果`,\n          left: 'center',\n          top: 10,\n          textStyle: {\n            fontSize: 14,\n            fontWeight: 'bold'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c}人 ({d}%)'\n        },\n        legend: {\n          orient: 'horizontal',\n          left: 'center',\n          bottom: 10,\n          data: legendData\n        },\n        color: colors,\n        series: [\n          {\n            name: '选择人数',\n            type: 'pie',\n            radius: ['30%', '60%'],\n            center: ['50%', '45%'],\n            avoidLabelOverlap: false,\n            label: {\n              show: true,\n              position: 'outside',\n              formatter: '{b}\\n{c}人 ({d}%)',\n              fontSize: 12\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: '14',\n                fontWeight: 'bold'\n              }\n            },\n            labelLine: {\n              show: true\n            },\n            data: pieData\n          }\n        ]\n      };\n      \n      chart.setOption(option);\n      \n      // 响应式调整\n      window.addEventListener('resize', () => {\n        chart.resize();\n      });\n    },\n    \n    // 返回\n    goBack() {\n      this.$router.go(-1);\n    },\n    \n    // 重新填写问卷\n    retakeQuestionnaire() {\n      this.$confirm('确定要重新填写问卷吗？这将清除您之前的答案。', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      }).then(() => {\n        // 跳转到问卷页面\n        this.$router.push('/test');\n      }).catch(() => {\n        // 用户取消\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.statistics-container {\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 20px;\n  background-color: #f9f9f9;\n  min-height: 100vh;\n}\n\n.statistics-title {\n  text-align: center;\n  margin-bottom: 30px;\n  font-size: 24px;\n  color: #333;\n}\n\n.loading {\n  text-align: center;\n  padding: 50px;\n}\n\n.no-data {\n  text-align: center;\n  padding: 50px;\n}\n\n.intro-text {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 15px;\n  background-color: #e8f4fd;\n  border-radius: 8px;\n  color: #666;\n}\n\n.chart-container {\n  margin-bottom: 40px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  background-color: #fff;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.question-title {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n  font-weight: bold;\n  text-align: left;\n  padding-bottom: 10px;\n  border-bottom: 2px solid #409EFF;\n}\n\n.chart {\n  margin: 0 auto;\n}\n\n.action-buttons {\n  text-align: center;\n  margin-top: 30px;\n  padding: 20px;\n}\n\n.action-buttons .el-button {\n  margin: 0 10px;\n}\n</style>\n"], "mappings": ";AAiCA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,OAAO,KAAKC,OAAM,MAAO,SAAS;AAElC,eAAe;EACbC,IAAI,EAAE,yBAAyB;EAC/BC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,cAAc,EAAE,EAAE;MAClBC,UAAU,EAAE;QACVC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,OAAO;QACfC,YAAY,EAAE;MAChB,CAAC;MACDC,MAAM,EAAE,EAAC,CAAE;IACb,CAAC;EACH,CAAC;;EAEDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd;IACA,IAAI,CAACH,MAAM,CAACI,OAAO,CAACC,KAAI,IAAK;MAC3B,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACC,OAAO,CAAC,CAAC;MACjB;IACF,CAAC,CAAC;EACJ,CAAC;EAEDC,OAAO,EAAE;IACP;IACAL,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACR,OAAM,GAAI,IAAI;MACnB,IAAIc,GAAE,GAAIlB,IAAG,GAAI,qBAAqB;MAEtCD,OAAO,CAACoB,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAIA,GAAG,CAACC,IAAG,KAAM,GAAE,IAAKD,GAAG,CAACE,OAAO,EAAE;UACnC,IAAI,CAAClB,cAAa,GAAIgB,GAAG,CAACE,OAAO;UACjC,IAAI,CAACC,SAAS,CAAC,MAAM;YACnB,IAAI,CAACC,aAAa,CAAC,CAAC;UACtB,CAAC,CAAC;QACJ,OAAO;UACL,IAAI,CAACC,QAAQ,CAAC;YACZC,OAAO,EAAE,UAAU;YACnBC,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;QACA,IAAI,CAACzB,OAAM,GAAI,KAAK;MACtB,CAAC,CAAC,CAAC0B,KAAK,CAACC,KAAI,IAAK;QAChBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACL,QAAQ,CAAC;UACZC,OAAO,EAAE,UAAU;UACnBC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;QACF,IAAI,CAACzB,OAAM,GAAI,KAAK;MACtB,CAAC,CAAC;IACJ,CAAC;IAED;IACAqB,aAAaA,CAAA,EAAG;MACd,IAAI,CAACpB,cAAc,CAACS,OAAO,CAAC,CAACmB,QAAQ,EAAEC,KAAK,KAAK;QAC/C,IAAI,CAACC,SAAS,CAACF,QAAQ,EAAEC,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,SAASA,CAACC,YAAY,EAAEF,KAAK,EAAE;MAC7B,MAAMG,OAAM,GAAI,aAAY,GAAID,YAAY,CAACE,GAAG;MAChD,MAAMC,QAAO,GAAIC,QAAQ,CAACC,cAAc,CAACJ,OAAO,CAAC;MAEjD,IAAI,CAACE,QAAQ,EAAE;QACbP,OAAO,CAACD,KAAK,CAAE,8BAA6BM,OAAQ,EAAC,CAAC;QACtD;MACF;MAEA,MAAMtB,KAAI,GAAId,OAAO,CAACyC,IAAI,CAACH,QAAQ,CAAC;MACpC,IAAI,CAAC7B,MAAM,CAACiC,IAAI,CAAC5B,KAAK,CAAC;;MAEvB;MACA,MAAM6B,OAAM,GAAIR,YAAY,CAACS,OAAM,IAAK,EAAE;MAC1C,MAAMC,UAAS,GAAIF,OAAO,CAACG,GAAG,CAACC,IAAG,IAAKA,IAAI,CAAC9C,IAAI,CAAC;;MAEjD;MACA,MAAM+C,MAAK,GAAI,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAEvG,MAAMC,MAAK,GAAI;QACbC,KAAK,EAAE;UACLC,IAAI,EAAG,MAAKlB,KAAI,GAAI,CAAC,OAAO;UAC5BmB,IAAI,EAAE,QAAQ;UACdC,GAAG,EAAE,EAAE;UACPC,SAAS,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE;UACd;QACF,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE;QACb,CAAC;QACDC,MAAM,EAAE;UACNC,MAAM,EAAE,YAAY;UACpBT,IAAI,EAAE,QAAQ;UACdU,MAAM,EAAE,EAAE;UACV5D,IAAI,EAAE2C;QACR,CAAC;QACDkB,KAAK,EAAEf,MAAM;QACbgB,MAAM,EAAE,CACN;UACE/D,IAAI,EAAE,MAAM;UACZ0B,IAAI,EAAE,KAAK;UACXsC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,iBAAiB,EAAE,KAAK;UACxBC,KAAK,EAAE;YACLC,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,SAAS;YACnBX,SAAS,EAAE,kBAAkB;YAC7BJ,QAAQ,EAAE;UACZ,CAAC;UACDgB,QAAQ,EAAE;YACRH,KAAK,EAAE;cACLC,IAAI,EAAE,IAAI;cACVd,QAAQ,EAAE,IAAI;cACdC,UAAU,EAAE;YACd;UACF,CAAC;UACDgB,SAAS,EAAE;YACTH,IAAI,EAAE;UACR,CAAC;UACDnE,IAAI,EAAEyC;QACR;MAEJ,CAAC;MAED7B,KAAK,CAAC2D,SAAS,CAACxB,MAAM,CAAC;;MAEvB;MACAyB,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;QACtC7D,KAAK,CAAC8D,MAAM,CAAC,CAAC;MAChB,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,MAAMA,CAAA,EAAG;MACP,IAAI,CAACC,OAAO,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACC,QAAQ,CAAC,wBAAwB,EAAE,IAAI,EAAE;QAC5CC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBxD,IAAI,EAAE;MACR,CAAC,CAAC,CAACR,IAAI,CAAC,MAAM;QACZ;QACA,IAAI,CAAC2D,OAAO,CAACpC,IAAI,CAAC,OAAO,CAAC;MAC5B,CAAC,CAAC,CAACb,KAAK,CAAC,MAAM;QACb;MAAA,CACD,CAAC;IACJ;EACF;AACF,CAAC"}]}