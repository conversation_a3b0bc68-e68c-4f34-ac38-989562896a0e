{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue?vue&type=template&id=0fb93bb2&scoped=true", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue", "mtime": 1749041655057}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlRWxlbWVudFZOb2RlIGFzIF9jcmVhdGVFbGVtZW50Vk5vZGUsIGNyZWF0ZUNvbW1lbnRWTm9kZSBhcyBfY3JlYXRlQ29tbWVudFZOb2RlLCByZXNvbHZlQ29tcG9uZW50IGFzIF9yZXNvbHZlQ29tcG9uZW50LCBjcmVhdGVWTm9kZSBhcyBfY3JlYXRlVk5vZGUsIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlLCB3aXRoQ3R4IGFzIF93aXRoQ3R4LCBvcGVuQmxvY2sgYXMgX29wZW5CbG9jaywgY3JlYXRlRWxlbWVudEJsb2NrIGFzIF9jcmVhdGVFbGVtZW50QmxvY2ssIEZyYWdtZW50IGFzIF9GcmFnbWVudCwgcmVuZGVyTGlzdCBhcyBfcmVuZGVyTGlzdCwgdG9EaXNwbGF5U3RyaW5nIGFzIF90b0Rpc3BsYXlTdHJpbmcsIHZNb2RlbFJhZGlvIGFzIF92TW9kZWxSYWRpbywgd2l0aERpcmVjdGl2ZXMgYXMgX3dpdGhEaXJlY3RpdmVzLCBub3JtYWxpemVDbGFzcyBhcyBfbm9ybWFsaXplQ2xhc3MsIHB1c2hTY29wZUlkIGFzIF9wdXNoU2NvcGVJZCwgcG9wU2NvcGVJZCBhcyBfcG9wU2NvcGVJZCB9IGZyb20gInZ1ZSI7CmNvbnN0IF93aXRoU2NvcGVJZCA9IG4gPT4gKF9wdXNoU2NvcGVJZCgiZGF0YS12LTBmYjkzYmIyIiksIG4gPSBuKCksIF9wb3BTY29wZUlkKCksIG4pOwpjb25zdCBfaG9pc3RlZF8xID0gewogIGNsYXNzOiAicXVlc3Rpb25uYWlyZS1jb250YWluZXIiCn07CmNvbnN0IF9ob2lzdGVkXzIgPSAvKiNfX1BVUkVfXyovX3dpdGhTY29wZUlkKCgpID0+IC8qI19fUFVSRV9fKi9fY3JlYXRlRWxlbWVudFZOb2RlKCJoMiIsIHsKICBjbGFzczogInF1ZXN0aW9ubmFpcmUtdGl0bGUiCn0sICLosIPmn6Xpl67ljbciLCAtMSAvKiBIT0lTVEVEICovKSk7CmNvbnN0IF9ob2lzdGVkXzMgPSB7CiAga2V5OiAwLAogIGNsYXNzOiAibG9naW4tcHJvbXB0Igp9Owpjb25zdCBfaG9pc3RlZF80ID0gewogIGNsYXNzOiAibG9naW4tYWN0aW9ucyIKfTsKY29uc3QgX2hvaXN0ZWRfNSA9IC8qI19fUFVSRV9fKi9fY3JlYXRlVGV4dFZOb2RlKCLljrvnmbvlvZUiKTsKY29uc3QgX2hvaXN0ZWRfNiA9IHsKICBjbGFzczogImNvbXBsZXRlZC1wcm9tcHQiCn07CmNvbnN0IF9ob2lzdGVkXzcgPSB7CiAgY2xhc3M6ICJjb21wbGV0ZWQtYWN0aW9ucyIKfTsKY29uc3QgX2hvaXN0ZWRfOCA9IC8qI19fUFVSRV9fKi9fY3JlYXRlVGV4dFZOb2RlKCLmn6XnnIvnu5/orqHnu5PmnpwiKTsKY29uc3QgX2hvaXN0ZWRfOSA9IC8qI19fUFVSRV9fKi9fY3JlYXRlVGV4dFZOb2RlKCLph43mlrDloavlhpnpl67ljbciKTsKY29uc3QgX2hvaXN0ZWRfMTAgPSB7CiAga2V5OiAwLAogIGNsYXNzOiAibG9hZGluZyIKfTsKY29uc3QgX2hvaXN0ZWRfMTEgPSB7CiAga2V5OiAxCn07CmNvbnN0IF9ob2lzdGVkXzEyID0gewogIGNsYXNzOiAicXVlc3Rpb24tdGV4dCIKfTsKY29uc3QgX2hvaXN0ZWRfMTMgPSB7CiAgY2xhc3M6ICJvcHRpb25zIgp9Owpjb25zdCBfaG9pc3RlZF8xNCA9IHsKICBjbGFzczogIm9wdGlvbiIKfTsKY29uc3QgX2hvaXN0ZWRfMTUgPSBbIm9uVXBkYXRlOm1vZGVsVmFsdWUiXTsKY29uc3QgX2hvaXN0ZWRfMTYgPSAvKiNfX1BVUkVfXyovX2NyZWF0ZVRleHRWTm9kZSgiIOmdnuW4uOa7oeaEjyAiKTsKY29uc3QgX2hvaXN0ZWRfMTcgPSB7CiAgY2xhc3M6ICJvcHRpb24iCn07CmNvbnN0IF9ob2lzdGVkXzE4ID0gWyJvblVwZGF0ZTptb2RlbFZhbHVlIl07CmNvbnN0IF9ob2lzdGVkXzE5ID0gLyojX19QVVJFX18qL19jcmVhdGVUZXh0Vk5vZGUoIiDmu6HmhI8gIik7CmNvbnN0IF9ob2lzdGVkXzIwID0gewogIGNsYXNzOiAib3B0aW9uIgp9Owpjb25zdCBfaG9pc3RlZF8yMSA9IFsib25VcGRhdGU6bW9kZWxWYWx1ZSJdOwpjb25zdCBfaG9pc3RlZF8yMiA9IC8qI19fUFVSRV9fKi9fY3JlYXRlVGV4dFZOb2RlKCIg5LiA6IisICIpOwpjb25zdCBfaG9pc3RlZF8yMyA9IHsKICBjbGFzczogIm9wdGlvbiIKfTsKY29uc3QgX2hvaXN0ZWRfMjQgPSBbIm9uVXBkYXRlOm1vZGVsVmFsdWUiXTsKY29uc3QgX2hvaXN0ZWRfMjUgPSAvKiNfX1BVUkVfXyovX2NyZWF0ZVRleHRWTm9kZSgiIOS4jea7oeaEjyAiKTsKY29uc3QgX2hvaXN0ZWRfMjYgPSB7CiAgY2xhc3M6ICJzdWJtaXQtY29udGFpbmVyIgp9OwpleHBvcnQgZnVuY3Rpb24gcmVuZGVyKF9jdHgsIF9jYWNoZSwgJHByb3BzLCAkc2V0dXAsICRkYXRhLCAkb3B0aW9ucykgewogIGNvbnN0IF9jb21wb25lbnRfZWxfYWxlcnQgPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtYWxlcnQiKTsKICBjb25zdCBfY29tcG9uZW50X2VsX2J1dHRvbiA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1idXR0b24iKTsKICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8xLCBbX2hvaXN0ZWRfMiwgX2NyZWF0ZUNvbW1lbnRWTm9kZSgiIOacqueZu+W9leaPkOekuiAiKSwgISRkYXRhLmlzTG9nZ2VkSW4gPyAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8zLCBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfYWxlcnQsIHsKICAgIHRpdGxlOiAi6K+35YWI55m75b2VIiwKICAgIGRlc2NyaXB0aW9uOiAi5oKo6ZyA6KaB55m75b2V5ZCO5omN6IO95Y+C5LiO6Zeu5Y236LCD5p+lIiwKICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICJzaG93LWljb24iOiAiIiwKICAgIGNsb3NhYmxlOiBmYWxzZQogIH0pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF80LCBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfYnV0dG9uLCB7CiAgICB0eXBlOiAicHJpbWFyeSIsCiAgICBvbkNsaWNrOiAkb3B0aW9ucy5nb1RvTG9naW4KICB9LCB7CiAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2hvaXN0ZWRfNV0pLAogICAgXzogMSAvKiBTVEFCTEUgKi8KICB9LCA4IC8qIFBST1BTICovLCBbIm9uQ2xpY2siXSldKV0pKSA6ICRkYXRhLmhhc0NvbXBsZXRlZCA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soX0ZyYWdtZW50LCB7CiAgICBrZXk6IDEKICB9LCBbX2NyZWF0ZUNvbW1lbnRWTm9kZSgiIOW3suWujOaIkOmXruWNt+aPkOekuiAiKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfNiwgW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2FsZXJ0LCB7CiAgICB0aXRsZTogIuaCqOW3suWujOaIkOmXruWNt+iwg+afpSIsCiAgICBkZXNjcmlwdGlvbjogIuaEn+iwouaCqOeahOWPguS4ju+8geaCqOWPr+S7peafpeeci+e7n+iuoee7k+aenOaIlumHjeaWsOWhq+WGmemXruWNtyIsCiAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAic2hvdy1pY29uIjogIiIsCiAgICBjbG9zYWJsZTogZmFsc2UKICB9KSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfNywgW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgdHlwZTogInByaW1hcnkiLAogICAgb25DbGljazogJG9wdGlvbnMudmlld1N0YXRpc3RpY3MKICB9LCB7CiAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2hvaXN0ZWRfOF0pLAogICAgXzogMSAvKiBTVEFCTEUgKi8KICB9LCA4IC8qIFBST1BTICovLCBbIm9uQ2xpY2siXSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgdHlwZTogIndhcm5pbmciLAogICAgb25DbGljazogJG9wdGlvbnMucmV0YWtlUXVlc3Rpb25uYWlyZQogIH0sIHsKICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfaG9pc3RlZF85XSksCiAgICBfOiAxIC8qIFNUQUJMRSAqLwogIH0sIDggLyogUFJPUFMgKi8sIFsib25DbGljayJdKV0pXSldLCAyMTEyIC8qIFNUQUJMRV9GUkFHTUVOVCwgREVWX1JPT1RfRlJBR01FTlQgKi8pKSA6ICRkYXRhLnNob3dRdWVzdGlvbm5haXJlID8gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIHsKICAgIGtleTogMgogIH0sIFtfY3JlYXRlQ29tbWVudFZOb2RlKCIg6Zeu5Y236KGo5Y2VICIpLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBudWxsLCBbJGRhdGEubG9hZGluZyA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIF9ob2lzdGVkXzEwLCAi5Yqg6L295LitLi4uIikpIDogKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgX2hvaXN0ZWRfMTEsIFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRkYXRhLnF1ZXN0aW9uc0xpc3QsIChxdWVzdGlvbiwgaW5kZXgpID0+IHsKICAgIHJldHVybiBfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIHsKICAgICAga2V5OiBxdWVzdGlvbi5xaWQsCiAgICAgIGNsYXNzOiAicXVlc3Rpb24taXRlbSIKICAgIH0sIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF8xMiwgX3RvRGlzcGxheVN0cmluZyhpbmRleCArIDEpICsgIi4gIiArIF90b0Rpc3BsYXlTdHJpbmcocXVlc3Rpb24ucXVlc3Rpb24pLCAxIC8qIFRFWFQgKi8pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF8xMywgW19jcmVhdGVFbGVtZW50Vk5vZGUoImxhYmVsIiwgX2hvaXN0ZWRfMTQsIFtfd2l0aERpcmVjdGl2ZXMoX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaW5wdXQiLCB7CiAgICAgIHR5cGU6ICJyYWRpbyIsCiAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogJGV2ZW50ID0+ICRkYXRhLmFuc3dlcnNbcXVlc3Rpb24ucWlkXSA9ICRldmVudCwKICAgICAgdmFsdWU6ICLpnZ7luLjmu6HmhI8iCiAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBfaG9pc3RlZF8xNSksIFtbX3ZNb2RlbFJhZGlvLCAkZGF0YS5hbnN3ZXJzW3F1ZXN0aW9uLnFpZF1dXSksIF9ob2lzdGVkXzE2XSksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImxhYmVsIiwgX2hvaXN0ZWRfMTcsIFtfd2l0aERpcmVjdGl2ZXMoX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaW5wdXQiLCB7CiAgICAgIHR5cGU6ICJyYWRpbyIsCiAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogJGV2ZW50ID0+ICRkYXRhLmFuc3dlcnNbcXVlc3Rpb24ucWlkXSA9ICRldmVudCwKICAgICAgdmFsdWU6ICLmu6HmhI8iCiAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBfaG9pc3RlZF8xOCksIFtbX3ZNb2RlbFJhZGlvLCAkZGF0YS5hbnN3ZXJzW3F1ZXN0aW9uLnFpZF1dXSksIF9ob2lzdGVkXzE5XSksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImxhYmVsIiwgX2hvaXN0ZWRfMjAsIFtfd2l0aERpcmVjdGl2ZXMoX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaW5wdXQiLCB7CiAgICAgIHR5cGU6ICJyYWRpbyIsCiAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogJGV2ZW50ID0+ICRkYXRhLmFuc3dlcnNbcXVlc3Rpb24ucWlkXSA9ICRldmVudCwKICAgICAgdmFsdWU6ICLkuIDoiKwiCiAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBfaG9pc3RlZF8yMSksIFtbX3ZNb2RlbFJhZGlvLCAkZGF0YS5hbnN3ZXJzW3F1ZXN0aW9uLnFpZF1dXSksIF9ob2lzdGVkXzIyXSksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImxhYmVsIiwgX2hvaXN0ZWRfMjMsIFtfd2l0aERpcmVjdGl2ZXMoX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaW5wdXQiLCB7CiAgICAgIHR5cGU6ICJyYWRpbyIsCiAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogJGV2ZW50ID0+ICRkYXRhLmFuc3dlcnNbcXVlc3Rpb24ucWlkXSA9ICRldmVudCwKICAgICAgdmFsdWU6ICLkuI3mu6HmhI8iCiAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBfaG9pc3RlZF8yNCksIFtbX3ZNb2RlbFJhZGlvLCAkZGF0YS5hbnN3ZXJzW3F1ZXN0aW9uLnFpZF1dXSksIF9ob2lzdGVkXzI1XSldKV0pOwogIH0pLCAxMjggLyogS0VZRURfRlJBR01FTlQgKi8pKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMjYsIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJidXR0b24iLCB7CiAgICBvbkNsaWNrOiBfY2FjaGVbMF0gfHwgKF9jYWNoZVswXSA9ICguLi5hcmdzKSA9PiAkb3B0aW9ucy5zdWJtaXRRdWVzdGlvbm5haXJlICYmICRvcHRpb25zLnN1Ym1pdFF1ZXN0aW9ubmFpcmUoLi4uYXJncykpLAogICAgY2xhc3M6ICJzdWJtaXQtYnRuIgogIH0sICLmj5DkuqTpl67ljbciKV0pLCAkZGF0YS5zdWJtaXRNZXNzYWdlID8gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgewogICAga2V5OiAwLAogICAgY2xhc3M6IF9ub3JtYWxpemVDbGFzcyhbIm1lc3NhZ2UiLCB7CiAgICAgICdzdWNjZXNzJzogJGRhdGEuc3VibWl0U3VjY2VzcywKICAgICAgJ2Vycm9yJzogISRkYXRhLnN1Ym1pdFN1Y2Nlc3MKICAgIH1dKQogIH0sIF90b0Rpc3BsYXlTdHJpbmcoJGRhdGEuc3VibWl0TWVzc2FnZSksIDMgLyogVEVYVCwgQ0xBU1MgKi8pKSA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKV0pKV0pXSwgMjExMiAvKiBTVEFCTEVfRlJBR01FTlQsIERFVl9ST09UX0ZSQUdNRU5UICovKSkgOiBfY3JlYXRlQ29tbWVudFZOb2RlKCJ2LWlmIiwgdHJ1ZSldKTsKfQ=="}, {"version": 3, "names": ["class", "_createElementVNode", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_createCommentVNode", "$data", "isLoggedIn", "_hoisted_3", "_createVNode", "_component_el_alert", "title", "description", "type", "closable", "_hoisted_4", "_component_el_button", "onClick", "$options", "goToLogin", "hasCompleted", "_Fragment", "key", "_hoisted_6", "_hoisted_7", "viewStatistics", "retakeQuestionnaire", "showQuestionnaire", "loading", "_hoisted_10", "_hoisted_11", "_renderList", "questionsList", "question", "index", "qid", "_hoisted_12", "_toDisplayString", "_hoisted_13", "_hoisted_14", "answers", "$event", "value", "_hoisted_17", "_hoisted_20", "_hoisted_23", "_hoisted_26", "_cache", "args", "submitQuestionnaire", "submitMessage", "_normalizeClass", "submitSuccess"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue"], "sourcesContent": ["<template>\r\n  <div class=\"questionnaire-container\">\r\n    <h2 class=\"questionnaire-title\">调查问卷</h2>\r\n\r\n    <!-- 未登录提示 -->\r\n    <div v-if=\"!isLoggedIn\" class=\"login-prompt\">\r\n      <el-alert\r\n        title=\"请先登录\"\r\n        description=\"您需要登录后才能参与问卷调查\"\r\n        type=\"warning\"\r\n        show-icon\r\n        :closable=\"false\">\r\n      </el-alert>\r\n      <div class=\"login-actions\">\r\n        <el-button type=\"primary\" @click=\"goToLogin\">去登录</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 已完成问卷提示 -->\r\n    <div v-else-if=\"hasCompleted\" class=\"completed-prompt\">\r\n      <el-alert\r\n        title=\"您已完成问卷调查\"\r\n        description=\"感谢您的参与！您可以查看统计结果或重新填写问卷\"\r\n        type=\"success\"\r\n        show-icon\r\n        :closable=\"false\">\r\n      </el-alert>\r\n      <div class=\"completed-actions\">\r\n        <el-button type=\"primary\" @click=\"viewStatistics\">查看统计结果</el-button>\r\n        <el-button type=\"warning\" @click=\"retakeQuestionnaire\">重新填写问卷</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 问卷表单 -->\r\n    <div v-else-if=\"showQuestionnaire\">\r\n      <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n\r\n      <div v-else>\r\n        <div v-for=\"(question, index) in questionsList\" :key=\"question.qid\" class=\"question-item\">\r\n          <div class=\"question-text\">{{ index + 1 }}. {{ question.question }}</div>\r\n          <div class=\"options\">\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"非常满意\" /> 非常满意\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"满意\" /> 满意\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"一般\" /> 一般\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"不满意\" /> 不满意\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"submit-container\">\r\n          <button @click=\"submitQuestionnaire\" class=\"submit-btn\">提交问卷</button>\r\n        </div>\r\n\r\n        <div v-if=\"submitMessage\" class=\"message\" :class=\"{ 'success': submitSuccess, 'error': !submitSuccess }\">\r\n          {{ submitMessage }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"QuestionnaireView\",\r\n  data() {\r\n    return {\r\n      questionsList: [],\r\n      answers: {},\r\n      loading: true,\r\n      submitMessage: '',\r\n      submitSuccess: false,\r\n      isLoggedIn: false,\r\n      hasCompleted: false,\r\n      showQuestionnaire: false,\r\n      userAccount: ''\r\n    };\r\n  },\r\n  created() {\r\n    this.checkLoginStatus();\r\n  },\r\n  methods: {\r\n    // 检查登录状态\r\n    checkLoginStatus() {\r\n      this.userAccount = sessionStorage.getItem(\"lname\");\r\n      this.isLoggedIn = !!this.userAccount;\r\n\r\n      if (this.isLoggedIn) {\r\n        this.checkCompletionStatus();\r\n      }\r\n    },\r\n\r\n    // 检查用户是否已完成问卷\r\n    checkCompletionStatus() {\r\n      let url = base + \"/results/checkCompleted?account=\" + this.userAccount;\r\n\r\n      request.post(url, {}).then((res) => {\r\n        if (res.code === 200) {\r\n          this.hasCompleted = res.resdata;\r\n          if (!this.hasCompleted) {\r\n            this.showQuestionnaire = true;\r\n            this.getQuestions();\r\n          }\r\n        } else {\r\n          // 检查失败，默认显示问卷\r\n          this.showQuestionnaire = true;\r\n          this.getQuestions();\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"检查完成状态失败:\", error);\r\n        // 检查失败，默认显示问卷\r\n        this.showQuestionnaire = true;\r\n        this.getQuestions();\r\n      });\r\n    },\r\n\r\n    // 去登录\r\n    goToLogin() {\r\n      this.$router.push('/ulogin');\r\n    },\r\n\r\n    // 查看统计结果\r\n    viewStatistics() {\r\n      // 创建一个新的路由来显示统计结果\r\n      this.$router.push('/questionnaireStatistics');\r\n    },\r\n\r\n    // 重新填写问卷\r\n    retakeQuestionnaire() {\r\n      this.$confirm('确定要重新填写问卷吗？这将清除您之前的答案。', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.hasCompleted = false;\r\n        this.showQuestionnaire = true;\r\n        this.getQuestions();\r\n      }).catch(() => {\r\n        // 用户取消\r\n      });\r\n    },\r\n\r\n    // 获取问卷问题列表\r\n    getQuestions() {\r\n      this.loading = true;\r\n      let url = base + \"/questions/list\";\r\n      let para = {};\r\n      \r\n      request.post(url, para, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\r\n        if (res.resdata) {\r\n          this.questionsList = res.resdata;\r\n          // 初始化答案对象\r\n          this.questionsList.forEach(question => {\r\n            this.$set(this.answers, question.qid, '');\r\n          });\r\n        }\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error(\"获取问卷问题失败:\", error);\r\n        this.loading = false;\r\n      });\r\n    },\r\n    \r\n    // 提交问卷\r\n    submitQuestionnaire() {\r\n      // 检查是否所有问题都已回答\r\n      const unansweredQuestions = this.questionsList.filter(q => !this.answers[q.qid]);\r\n      \r\n      if (unansweredQuestions.length > 0) {\r\n        this.submitMessage = `请回答所有问题后再提交`;\r\n        this.submitSuccess = false;\r\n        return;\r\n      }\r\n      \r\n      // 准备提交数据\r\n      const results = [];\r\n      \r\n      // 将每个问题的答案转换为提交格式\r\n      this.questionsList.forEach(question => {\r\n        results.push({\r\n          qid: question.qid,\r\n          question: question.question,\r\n          status: this.answers[question.qid],\r\n          account: sessionStorage.getItem(\"lname\"),\r\n        });\r\n      });\r\n      \r\n      // 显示提交中状态\r\n      this.submitMessage = \"正在提交...\";\r\n      this.submitSuccess = true;\r\n      \r\n      // 一次性提交所有答案\r\n      let successCount = 0;\r\n      let failCount = 0;\r\n      \r\n      // 逐个提交答案并检查每个请求的响应\r\n      results.forEach(result => {\r\n        request.post(base + \"/results/add\", result)\r\n          .then(res => {\r\n            successCount++;\r\n            // 如果所有请求都已完成\r\n            if (successCount + failCount === results.length) {\r\n              if (failCount === 0) {\r\n                this.submitMessage = \"问卷提交成功，感谢您的反馈！\";\r\n                this.submitSuccess = true;\r\n                // 清空答案\r\n                this.answers = {};\r\n                this.questionsList.forEach(question => {\r\n                  this.$set(this.answers, question.qid, '');\r\n                });\r\n                // 更新完成状态\r\n                setTimeout(() => {\r\n                  this.hasCompleted = true;\r\n                  this.showQuestionnaire = false;\r\n                }, 2000);\r\n              } else {\r\n                this.submitMessage = `部分提交成功，${failCount}个问题提交失败`;\r\n                this.submitSuccess = false;\r\n              }\r\n            }\r\n          })\r\n          .catch(error => {\r\n            failCount++;\r\n            console.error(\"提交问题失败:\", error);\r\n            if (successCount + failCount === results.length) {\r\n              this.submitMessage = `部分提交成功，${failCount}个问题提交失败`;\r\n              this.submitSuccess = false;\r\n            }\r\n          });\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.questionnaire-container {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.questionnaire-title {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  font-size: 24px;\r\n}\r\n\r\n.loading {\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 16px;\r\n}\r\n\r\n.question-item {\r\n  margin-bottom: 25px;\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.question-text {\r\n  font-size: 16px;\r\n  margin-bottom: 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-left: 20px;\r\n}\r\n\r\n.option {\r\n  margin-right: 30px;\r\n  margin-bottom: 10px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.option input {\r\n  margin-right: 5px;\r\n}\r\n\r\n.submit-container {\r\n  text-align: center;\r\n  margin-top: 30px;\r\n}\r\n\r\n.submit-btn {\r\n  padding: 10px 30px;\r\n  background-color: #409EFF;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n}\r\n\r\n.submit-btn:hover {\r\n  background-color: #66b1ff;\r\n}\r\n\r\n.message {\r\n  text-align: center;\r\n  margin-top: 20px;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.success {\r\n  background-color: #f0f9eb;\r\n  color: #67c23a;\r\n  border: 1px solid #c2e7b0;\r\n}\r\n\r\n.error {\r\n  background-color: #fef0f0;\r\n  color: #f56c6c;\r\n  border: 1px solid #fbc4c4;\r\n}\r\n\r\n.login-prompt, .completed-prompt {\r\n  text-align: center;\r\n  padding: 30px;\r\n}\r\n\r\n.login-actions, .completed-actions {\r\n  margin-top: 20px;\r\n}\r\n\r\n.login-actions .el-button, .completed-actions .el-button {\r\n  margin: 0 10px;\r\n}\r\n</style>\r\n\r\n\r\n\r\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAyB;gEAClCC,mBAAA,CAAyC;EAArCD,KAAK,EAAC;AAAqB,GAAC,MAAI;;;EAGZA,KAAK,EAAC;;;EAQvBA,KAAK,EAAC;AAAe;iDACqB,KAAG;;EAKtBA,KAAK,EAAC;AAAkB;;EAQ/CA,KAAK,EAAC;AAAmB;iDACsB,QAAM;iDACD,QAAM;;;EAM3CA,KAAK,EAAC;;;;;;EAIjBA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAS;;EACXA,KAAK,EAAC;AAAQ;;kDACgD,QACrE;;EACOA,KAAK,EAAC;AAAQ;;kDAC8C,MACnE;;EACOA,KAAK,EAAC;AAAQ;;kDAC8C,MACnE;;EACOA,KAAK,EAAC;AAAQ;;kDAC+C,OACpE;;EAICA,KAAK,EAAC;AAAkB;;;;uBAvDnCE,mBAAA,CAgEM,OAhENC,UAgEM,GA/DJC,UAAyC,EAEzCC,mBAAA,WAAc,E,CACFC,KAAA,CAAAC,UAAU,I,cAAtBL,mBAAA,CAWM,OAXNM,UAWM,GAVJC,YAAA,CAMWC,mBAAA;IALTC,KAAK,EAAC,MAAM;IACZC,WAAW,EAAC,gBAAgB;IAC5BC,IAAI,EAAC,SAAS;IACd,WAAS,EAAT,EAAS;IACRC,QAAQ,EAAE;MAEbb,mBAAA,CAEM,OAFNc,UAEM,GADJN,YAAA,CAA4DO,oBAAA;IAAjDH,IAAI,EAAC,SAAS;IAAEI,OAAK,EAAEC,QAAA,CAAAC;;sBAAW,MAAG,C;;wCAKpCb,KAAA,CAAAc,YAAY,I,cAA5BlB,mBAAA,CAYMmB,SAAA;IAAAC,GAAA;EAAA,IAbNjB,mBAAA,aAAgB,EAChBJ,mBAAA,CAYM,OAZNsB,UAYM,GAXJd,YAAA,CAMWC,mBAAA;IALTC,KAAK,EAAC,UAAU;IAChBC,WAAW,EAAC,yBAAyB;IACrCC,IAAI,EAAC,SAAS;IACd,WAAS,EAAT,EAAS;IACRC,QAAQ,EAAE;MAEbb,mBAAA,CAGM,OAHNuB,UAGM,GAFJf,YAAA,CAAoEO,oBAAA;IAAzDH,IAAI,EAAC,SAAS;IAAEI,OAAK,EAAEC,QAAA,CAAAO;;sBAAgB,MAAM,C;;kCACxDhB,YAAA,CAAyEO,oBAAA;IAA9DH,IAAI,EAAC,SAAS;IAAEI,OAAK,EAAEC,QAAA,CAAAQ;;sBAAqB,MAAM,C;;yFAKjDpB,KAAA,CAAAqB,iBAAiB,I,cAAjCzB,mBAAA,CA8BMmB,SAAA;IAAAC,GAAA;EAAA,IA/BNjB,mBAAA,UAAa,EACbJ,mBAAA,CA8BM,cA7BOK,KAAA,CAAAsB,OAAO,I,cAAlB1B,mBAAA,CAAgD,OAAhD2B,WAAgD,EAAZ,QAAM,M,cAE1C3B,mBAAA,CA0BM,OAAA4B,WAAA,I,kBAzBJ5B,mBAAA,CAgBMmB,SAAA,QAAAU,WAAA,CAhB2BzB,KAAA,CAAA0B,aAAa,GAAjCC,QAAQ,EAAEC,KAAK;yBAA5BhC,mBAAA,CAgBM;MAhB2CoB,GAAG,EAAEW,QAAQ,CAACE,GAAG;MAAEnC,KAAK,EAAC;QACxEC,mBAAA,CAAyE,OAAzEmC,WAAyE,EAAAC,gBAAA,CAA3CH,KAAK,QAAO,IAAE,GAAAG,gBAAA,CAAGJ,QAAQ,CAACA,QAAQ,kBAChEhC,mBAAA,CAaM,OAbNqC,WAaM,GAZJrC,mBAAA,CAEQ,SAFRsC,WAEQ,G,gBADNtC,mBAAA,CAAmE;MAA5DY,IAAI,EAAC,OAAO;uCAAUP,KAAA,CAAAkC,OAAO,CAACP,QAAQ,CAACE,GAAG,IAAAM,MAAA;MAAGC,KAAK,EAAC;0DAA7BpC,KAAA,CAAAkC,OAAO,CAACP,QAAQ,CAACE,GAAG,G,kBAEnDlC,mBAAA,CAEQ,SAFR0C,WAEQ,G,gBADN1C,mBAAA,CAAiE;MAA1DY,IAAI,EAAC,OAAO;uCAAUP,KAAA,CAAAkC,OAAO,CAACP,QAAQ,CAACE,GAAG,IAAAM,MAAA;MAAGC,KAAK,EAAC;0DAA7BpC,KAAA,CAAAkC,OAAO,CAACP,QAAQ,CAACE,GAAG,G,kBAEnDlC,mBAAA,CAEQ,SAFR2C,WAEQ,G,gBADN3C,mBAAA,CAAiE;MAA1DY,IAAI,EAAC,OAAO;uCAAUP,KAAA,CAAAkC,OAAO,CAACP,QAAQ,CAACE,GAAG,IAAAM,MAAA;MAAGC,KAAK,EAAC;0DAA7BpC,KAAA,CAAAkC,OAAO,CAACP,QAAQ,CAACE,GAAG,G,kBAEnDlC,mBAAA,CAEQ,SAFR4C,WAEQ,G,gBADN5C,mBAAA,CAAkE;MAA3DY,IAAI,EAAC,OAAO;uCAAUP,KAAA,CAAAkC,OAAO,CAACP,QAAQ,CAACE,GAAG,IAAAM,MAAA;MAAGC,KAAK,EAAC;0DAA7BpC,KAAA,CAAAkC,OAAO,CAACP,QAAQ,CAACE,GAAG,G;kCAKvDlC,mBAAA,CAEM,OAFN6C,WAEM,GADJ7C,mBAAA,CAAqE;IAA5DgB,OAAK,EAAA8B,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAE9B,QAAA,CAAA+B,mBAAA,IAAA/B,QAAA,CAAA+B,mBAAA,IAAAD,IAAA,CAAmB;IAAEhD,KAAK,EAAC;KAAa,MAAI,E,GAGnDM,KAAA,CAAA4C,aAAa,I,cAAxBhD,mBAAA,CAEM;;IAFoBF,KAAK,EAAAmD,eAAA,EAAC,SAAS;MAAA,WAAsB7C,KAAA,CAAA8C,aAAa;MAAA,UAAY9C,KAAA,CAAA8C;IAAa;sBAChG9C,KAAA,CAAA4C,aAAa,2B"}]}