{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Ureg.vue?vue&type=template&id=11caf1d8", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Ureg.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_createElementVNode", "class", "style", "id", "_createVNode", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_component_el_form_item", "label", "prop", "_component_el_input", "account", "$event", "placeholder", "type", "password", "password2", "uname", "_component_el_radio_group", "gender", "_component_el_radio", "age", "phone", "email", "address", "avatar", "readonly", "_component_el_button", "size", "onClick", "$options", "showUpload", "save", "loading", "btnLoading", "icon", "_component_el_dialog", "uploadVisible", "title", "onClose", "_ctx", "closeDialog", "_hoisted_5", "_component_el_upload", "action", "drag", "limit", "handlePreview", "handleRemove", "fileList", "handleExceed", "name", "fileListChange", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "hideUpload", "handleConfirm"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Ureg.vue"], "sourcesContent": ["<template>\r\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"账号\" prop=\"account\">\r\n<el-input v-model=\"formData.account\" placeholder=\"账号\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"密码\" prop=\"password\">\r\n<el-input type=\"password\" v-model=\"formData.password\" placeholder=\"密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"确认密码\" prop=\"password2\">\r\n<el-input type=\"password\" v-model=\"formData.password2\" placeholder=\"确认密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"姓名\" prop=\"uname\">\r\n<el-input v-model=\"formData.uname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"性别\" prop=\"gender\">\r\n<el-radio-group v-model=\"formData.gender\">\r\n<el-radio label=\"男\">\r\n男\r\n</el-radio>\r\n<el-radio label=\"女\">\r\n女\r\n</el-radio>\r\n</el-radio-group>\r\n</el-form-item>\r\n<el-form-item label=\"年龄\" prop=\"age\">\r\n<el-input v-model=\"formData.age\" placeholder=\"年龄\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"手机号码\" prop=\"phone\">\r\n<el-input v-model=\"formData.phone\" placeholder=\"手机号码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"电子邮箱\" prop=\"email\">\r\n<el-input v-model=\"formData.email\" placeholder=\"电子邮箱\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系地址\" prop=\"address\">\r\n<el-input v-model=\"formData.address\" placeholder=\"联系地址\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item prop=\"avatar\" label=\"个人头像\"  min-width=\"20%\">\r\n<el-input v-model=\"formData.avatar\" placeholder=\"个人头像\"  readonly=\"true\" style=\"width:50%;\" ></el-input>\r\n<el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\"  size=\"small\" @click=\"save\" :loading=\"btnLoading\"   icon=\"el-icon-upload\" >注 册</el-button>\r\n</el-form-item>\r\n</el-form>\r\n     <el-dialog\n        v-model=\"uploadVisible\"\n        title=\"附件上传\"\n        custom-class=\"el-dialog-widthSmall\"\n        @close=\"closeDialog\"\n      >\n        <div>\n          <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n        </div>\n        <el-upload\n          action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n          style=\"\n            margin: auto;\n            margin-top: 10px;\n            border: 1px solid #dcdfe6;\n            border-radius: 4px;\n          \"\n          drag\n          :limit=\"1\"\n          :on-preview=\"handlePreview\"\n          :on-remove=\"handleRemove\"\n          :file-list=\"fileList\"\n          :on-exceed=\"handleExceed\"\n          :auto-upload=\"false\"\n          name=\"file\"\n          :on-change=\"fileListChange\"\n        >\n          <i class=\"el-icon-upload\"></i>\n          <div class=\"el-upload__text\">\n            将数据文件拖到此处，或<em>点击上传</em>\n          </div>\n          <div class=\"el-upload__tip\">\n            <div\n              style=\"display: inline; color: #d70000; font-size: 14px\"\n              class=\"uploadFileWarning\"\n              id=\"uploadFileWarning\"\n            ></div>\n          </div>\n        </el-upload>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"hideUpload\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n        </span>\n      </el-dialog>\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"Ureg\",\r\n  data() {\r\n    return {\r\n      formData:{},\r\n\r\n      addrules: {\r\n          account: [{ required: true, message: '请输入账号', trigger: 'blur' },],\r\n          password: [{ required: true, message: '请输入密码', trigger: 'blur' }],\n          password2: [{ required: true, message: '请输入密码', trigger: 'blur' },{ validator: (rule, value, callback) => { if (value !== this.formData.password) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\r\n          uname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\r\n          gender: [{ required: true, message: '请输入性别', trigger: 'blur' },],\r\n          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },        { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },],\r\n          email: [{ required: true, message: '请输入电子邮箱', trigger: 'blur' },        { pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/, message: '电子邮箱格式不正确', trigger: 'blur' },],\r\n          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },],\r\n          avatar: [{ required: true, message: '请上传个人头像', trigger: 'blur' }],\r\n      },\r\n\r\n\r\n      btnLoading: false, //按钮是否在加载中\r\n      uploadVisible: false, //上传弹出框\r\n\r\n    };\r\n  },\r\n  created() {\r\n    \r\n  },\r\n  methods: {  \r\n    //注册\nsave() {\n    //表单验证\n    this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n            let url = base + \"/users/add\"; //请求地址\n            this.btnLoading = true; //按钮加载状态\n            request.post(url, this.formData).then((res) => { //请求接口             \n                if (res.code == 200) {\n                    this.$message({\n                        message: \"恭喜您，注册成功！\",\n                        type: \"success\",\n                        offset: 320,\n                    });\n                    this.$router.push(\"/ulogin\");\n                }\n                else if (res.code == 201) {\n                    this.$message({\n                        message: res.msg,\n                        type: \"error\",\n                        offset: 320,\n                    });\n                    this.btnLoading = false;\n                }\n                else {\n                    this.$message({\n                        message: \"服务器错误\",\n                        type: \"error\",\n                        offset: 320,\n                    });\n                    this.btnLoading = false;\n                }\n            });\n        }\n    });\n},\r\n    //显示上传框\n    showUpload() {\n      this.uploadVisible = true;\n    },\n\n    //隐藏上传框\n    hideUpload() {\n      this.uploadVisible = false;\n    },\n    //上传\n    handleRemove(file, fileList) {\n      this.fileList = fileList;\n    },\n    handlePreview(file) {\n      console.log(file);\n    },\n    handleExceed(files, fileList) {\n      this.$message({\n        duration: 1000,\n        message: \"只能上传一个文件\",\n        type: \"error\",\n        offset: 320,\n      });\n    },\n    // 判断上传文件后缀\n    fileListChange(file, fileList) {\n      let extendFileName = \"png,jpg\";\n      let extendFileNames = extendFileName.split(\",\");\n      let regExpRules = [];\n      for (let i = 0; i < extendFileNames.length; i++) {\n        regExpRules.push(\n          new RegExp(\"(.*).(\" + extendFileNames[i] + \")$\", \"gim\")\n        );\n      }\n      let fileNames = [];\n      let files = [];\n      let that = this;\n      fileList.forEach(function (key, val) {\n        let ret = false;\n        for (let i = 0; i < regExpRules.length; i++) {\n          ret = ret || regExpRules[i].test(key[\"name\"]);\n        }\n        if (!ret) {\n          console.log(key[\"name\"] + \":\" + ret);\n          that.$message({\n            duration: 1000,\n            message: \"上传的文件后缀必须为\" + extendFileName + \"格式！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        if (fileNames.indexOf(key[\"name\"]) != -1) {\n          that.$message({\n            duration: 1000,\n            message: \"上传的文件重复！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        //只能上传一个文件，用最后上传的覆盖\n        if (!that.multiFiles) {\n          files = [];\n          fileNames = [];\n        }\n        files.push(key);\n        fileNames.push(key[\"name\"]);\n        if (fileNames !== \"\") {\n          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n        }\n        //$(\".uploadFileWarning\").text(\"\");\n      });\n      this.files = fileNames;\n      this.fileList = files;\n    },\n    /**\n     * 确认按钮\n     */\n    handleConfirm() {\n      let filePath = this.fileList;\n      if (filePath.length === 0) {\n        this.$message({\n          duration: 1000,\n          message: \"请选择文件！\",\n          type: \"error\",\n          offset: 320,\n        });\n        return false;\n      }\n      let formData = new FormData();\n      this.fileList.forEach((file) => {\n        formData.append(\"file\", file.raw, file.raw.name);\n      });\n      let url = base + \"/common/uploadFile\";\n      console.log(\"url=\" + url);\n      request.post(url, formData).then((res) => {\n        console.log(res);\n        let furl = res.resdata.filePath;\n        this.formData.avatar = furl;  // 上传文件的路径\n        this.hideUpload();\n        console.log(res);\n      });\n    },\n\r\n\r\n\r\n\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n\r\n\r\n"], "mappings": ";iDAgBoB,KAEpB;iDACoB,KAEpB;iDAiB2D,IAAE;iDAGwC,KAAG;gCAShGA,mBAAA,CAEM,c,aADJA,mBAAA,CAAkC,WAA/B,wBAA2B,E;gCAoB9BA,mBAAA,CAA8B;EAA3BC,KAAK,EAAC;AAAgB;gCACzBD,mBAAA,CAEM;EAFDC,KAAK,EAAC;AAAiB,I,8BAAC,cAChB,G,aAAAD,mBAAA,CAAa,YAAT,MAAI,E;gCAErBA,mBAAA,CAMM;EANDC,KAAK,EAAC;AAAgB,I,aACzBD,mBAAA,CAIO;EAHLE,KAAwD,EAAxD;IAAA;IAAA;IAAA;EAAA,CAAwD;EACxDD,KAAK,EAAC,mBAAmB;EACzBE,EAAE,EAAC;;;EAIHF,KAAK,EAAC;AAAe;kDACM,KAAG;kDACe,KAAG;;;;;;;;;;6DApF1DG,YAAA,CA0CMC,kBAAA;IA1CIC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAGC,KAAK,EAAC;;sBAC5F,MAEe,CAFfR,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAsF,CAAtFX,YAAA,CAAsFY,mBAAA;oBAAnET,KAAA,CAAAC,QAAQ,CAACS,OAAO;mEAAhBV,KAAA,CAAAC,QAAQ,CAACS,OAAO,GAAAC,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAEjB,KAAkB,EAAlB;UAAA;QAAA;;;QAEvDE,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAuG,CAAvGX,YAAA,CAAuGY,mBAAA;QAA7FI,IAAI,EAAC,UAAU;oBAAUb,KAAA,CAAAC,QAAQ,CAACa,QAAQ;mEAAjBd,KAAA,CAAAC,QAAQ,CAACa,QAAQ,GAAAH,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAEjB,KAAkB,EAAlB;UAAA;QAAA;;;QAExEE,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAA0G,CAA1GX,YAAA,CAA0GY,mBAAA;QAAhGI,IAAI,EAAC,UAAU;oBAAUb,KAAA,CAAAC,QAAQ,CAACc,SAAS;mEAAlBf,KAAA,CAAAC,QAAQ,CAACc,SAAS,GAAAJ,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAEjB,KAAkB,EAAlB;UAAA;QAAA;;;QAE3EE,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAoF,CAApFX,YAAA,CAAoFY,mBAAA;oBAAjET,KAAA,CAAAC,QAAQ,CAACe,KAAK;mEAAdhB,KAAA,CAAAC,QAAQ,CAACe,KAAK,GAAAL,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAEjB,KAAkB,EAAlB;UAAA;QAAA;;;QAErDE,YAAA,CASeS,uBAAA;MATDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAOiB,CAPjBX,YAAA,CAOiBoB,yBAAA;oBAPQjB,KAAA,CAAAC,QAAQ,CAACiB,MAAM;mEAAflB,KAAA,CAAAC,QAAQ,CAACiB,MAAM,GAAAP,MAAA;;0BACxC,MAEW,CAFXd,YAAA,CAEWsB,mBAAA;UAFDZ,KAAK,EAAC;QAAG;4BAAC,MAEpB,C;;YACAV,YAAA,CAEWsB,mBAAA;UAFDZ,KAAK,EAAC;QAAG;4BAAC,MAEpB,C;;;;;;;QAGAV,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAkF,CAAlFX,YAAA,CAAkFY,mBAAA;oBAA/DT,KAAA,CAAAC,QAAQ,CAACmB,GAAG;mEAAZpB,KAAA,CAAAC,QAAQ,CAACmB,GAAG,GAAAT,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAEjB,KAAkB,EAAlB;UAAA;QAAA;;;QAEnDE,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAsF,CAAtFX,YAAA,CAAsFY,mBAAA;oBAAnET,KAAA,CAAAC,QAAQ,CAACoB,KAAK;mEAAdrB,KAAA,CAAAC,QAAQ,CAACoB,KAAK,GAAAV,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAEjB,KAAkB,EAAlB;UAAA;QAAA;;;QAEvDE,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAsF,CAAtFX,YAAA,CAAsFY,mBAAA;oBAAnET,KAAA,CAAAC,QAAQ,CAACqB,KAAK;mEAAdtB,KAAA,CAAAC,QAAQ,CAACqB,KAAK,GAAAX,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAEjB,KAAkB,EAAlB;UAAA;QAAA;;;QAEvDE,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAwF,CAAxFX,YAAA,CAAwFY,mBAAA;oBAArET,KAAA,CAAAC,QAAQ,CAACsB,OAAO;mEAAhBvB,KAAA,CAAAC,QAAQ,CAACsB,OAAO,GAAAZ,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAEjB,KAAkB,EAAlB;UAAA;QAAA;;;QAEzDE,YAAA,CAGeS,uBAAA;MAHDE,IAAI,EAAC,QAAQ;MAACD,KAAK,EAAC,MAAM;MAAE,WAAS,EAAC;;wBACpD,MAAuG,CAAvGV,YAAA,CAAuGY,mBAAA;oBAApFT,KAAA,CAAAC,QAAQ,CAACuB,MAAM;mEAAfxB,KAAA,CAAAC,QAAQ,CAACuB,MAAM,GAAAb,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAEa,QAAQ,EAAC,MAAM;QAAC9B,KAAkB,EAAlB;UAAA;QAAA;+CACxEE,YAAA,CAAyE6B,oBAAA;QAA9Db,IAAI,EAAC,SAAS;QAACc,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAC;;0BAAY,MAAE,C;;;;QAE7DjC,YAAA,CAEeS,uBAAA;wBADf,MAAoH,CAApHT,YAAA,CAAoH6B,oBAAA;QAAzGb,IAAI,EAAC,SAAS;QAAEc,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAE,IAAI;QAAGC,OAAO,EAAEhC,KAAA,CAAAiC,UAAU;QAAIC,IAAI,EAAC;;0BAAkB,MAAG,C;;;;;;;yCAGnGrC,YAAA,CA2CasC,oBAAA;gBA1CDnC,KAAA,CAAAoC,aAAa;iEAAbpC,KAAA,CAAAoC,aAAa,GAAAzB,MAAA;IACtB0B,KAAK,EAAC,MAAM;IACZ,cAAY,EAAC,sBAAsB;IAClCC,OAAK,EAAEC,IAAA,CAAAC;;sBAER,MAEM,CAFNC,UAEM,EACN5C,YAAA,CA6BY6C,oBAAA;MA5BVC,MAAM,EAAC,mDAAmD;MAC1DhD,KAKC,EALD;QAAA;QAAA;QAAA;QAAA;MAAA,CAKC;MACDiD,IAAI,EAAJ,EAAI;MACHC,KAAK,EAAE,CAAC;MACR,YAAU,EAAEhB,QAAA,CAAAiB,aAAa;MACzB,WAAS,EAAEjB,QAAA,CAAAkB,YAAY;MACvB,WAAS,EAAER,IAAA,CAAAS,QAAQ;MACnB,WAAS,EAAEnB,QAAA,CAAAoB,YAAY;MACvB,aAAW,EAAE,KAAK;MACnBC,IAAI,EAAC,MAAM;MACV,WAAS,EAAErB,QAAA,CAAAsB;;wBAEZ,MAA8B,CAA9BC,UAA8B,EAC9BC,UAEM,EACNC,UAMM,C;;2FAER7D,mBAAA,CAGO,QAHP8D,UAGO,GAFL1D,YAAA,CAA8C6B,oBAAA;MAAlCE,OAAK,EAAEC,QAAA,CAAA2B;IAAU;wBAAE,MAAG,C;;oCAClC3D,YAAA,CAAgE6B,oBAAA;MAArDb,IAAI,EAAC,SAAS;MAAEe,OAAK,EAAEC,QAAA,CAAA4B;;wBAAe,MAAG,C"}]}