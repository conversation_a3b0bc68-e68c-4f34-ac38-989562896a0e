{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Bbs.vue?vue&type=style&index=0&id=310a34a4&lang=css", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Bbs.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749040033032}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749040034646}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749040033671}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoudGV4dCB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNjY2M7DQogIG1pbi1oZWlnaHQ6IDIwMHB4Ow0KfQ0K"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Bbs.vue"], "names": [], "mappings": ";AAsKA,CAAC,CAAC,CAAC,CAAC,EAAE;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/Bbs.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <table style=\"width: 100%;line-height:32px;font-size:13px\" class=\"bbs\" v-for=\"item in postslist\" :key=\"item.psid\">\r\n    <tr>\r\n      <td>\r\n        <a :href=\"'bbsView?id=' + item.psid\">{{ item.ptitle }}\r\n        </a>\r\n      </td>\r\n      <td width=\"400\">\r\n        <span style=\"float:right;\">\r\n          {{ item.ptime }}\r\n        </span>\r\n        <span class=\"cu-tag line-red\" style=\"height:28px;float:right;  margin-right: 10px;\">点击量：{{\r\n            item.vwcnt\r\n          }}</span>\r\n        <span class=\"cu-tag line-green light\" style=\"float:right; margin-right: 10px; height:28px; line-height:28px \">\r\n          发布人：{{ item.account }}\r\n        </span>\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n\r\n  <div style=\"width: 100%;display: inline-table;\">\r\n    <el-pagination @current-change=\"handleCurrentChange\"\r\n                   :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" background\r\n                   layout=\"total, prev, pager, next, jumper\"\r\n                   :total=\"page.totalCount\" style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n  </div>\r\n\r\n\r\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\r\n    <el-form-item label=\"帖子标题\" prop=\"ptitle\">\r\n      <el-input v-model=\"formData.ptitle\" placeholder=\"帖子标题\" style=\"width:50%;\"></el-input>\r\n    </el-form-item>\r\n    <el-form-item label=\"帖子内容\" prop=\"pcontent\">\r\n      <WangEditor ref=\"wangEditorRef\" v-model=\"formData.pcontent\" :config=\"editorConfig\" :isClear=\"isClear\"\r\n                  @change=\"editorChange\" height=\"200\"></WangEditor>\r\n    </el-form-item>\r\n    <el-form-item label=\"\">\r\n      <el-button type=\"primary\" @click=\"save\" :loading=\"btnLoading\">提 交</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n<script>\r\nimport request, {base} from \"../../../utils/http\";\r\nimport WangEditor from \"../../components/WangEditor\";\r\n\r\nexport default {\r\n  name: \"posts\",\r\n  components: {\r\n    WangEditor,\r\n  },\r\n  data() {\r\n    return {\r\n      page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 15, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n      },\r\n      postslist: \"\",\r\n\r\n      formData: {}, //表单数据\r\n      rules: {\r\n        ptitle: [\r\n          {required: true, message: \"请输入帖子标题\", trigger: \"blur\"},\r\n        ],\r\n        pcontent: [\r\n          {required: true, message: \"请输入帖子内容\", trigger: \"blur\"},\r\n        ],\r\n      },\r\n\r\n    };\r\n  },\r\n  created() {\r\n    this.keyword = this.$route.query.keyword;\r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {\r\n\r\n    // 分页\r\n    handleCurrentChange(val) {\r\n      this.page.currentPage = val;\r\n\r\n      this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let para = {      \r\n\r\n      };\r\n      this.listLoading = true;\r\n      let url = base + \"/posts/list?currentPage=\" + this.page.currentPage + \"&pageSize=\" + this.page.pageSize;\r\n      request.post(url, para).then((res) => {\r\n        if (res.resdata.length > 0) {\r\n          this.isPage = true;\r\n        } else {\r\n          this.isPage = false;\r\n        }\r\n        this.page.totalCount = res.count;\r\n        this.postslist = res.resdata;\r\n        this.listLoading = false;\r\n      });\r\n    },\r\n\r\n    //保存\r\n    save() {\r\n\r\n      var lname = sessionStorage.getItem(\"lname\");\r\n      if (lname == null) {\r\n        this.$message({\r\n          message: \"请先登录\",\r\n          type: \"error\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.$refs.formDataRef.validate((valid) => {\r\n        if (valid) {\r\n          let para = {\r\n            pcontent: this.formData.pcontent,\r\n            ptitle: this.formData.ptitle,\r\n            account: lname,\r\n            vwcnt: 0,\r\n          };\r\n          this.btnLoading = true;\r\n          let url = base + \"/posts/add\";\r\n          request.post(url, para).then((res) => {\r\n            this.btnLoading = false;\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                message: \"提交成功\",\r\n                type: \"success\",\r\n              });\r\n              this.formData = {};\r\n\r\n              //设置富文本编辑器内容\r\n              this.$nextTick(() => {\r\n                this.$refs[\"wangEditorRef\"].editor.txt.html(\"\");\r\n              });\r\n\r\n              this.getDatas();\r\n            } else {\r\n              this.$message({\r\n                message: res.msg,\r\n                type: \"error\",\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.pcontent = val;\r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.text {\r\n  border: 1px solid #ccc;\r\n  min-height: 200px;\r\n}\r\n</style>\r\n\r\n\r\n"]}]}