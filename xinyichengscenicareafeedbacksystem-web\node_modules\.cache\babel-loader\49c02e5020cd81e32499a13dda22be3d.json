{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionsStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionsStatistics.vue", "mtime": 1749041539479}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "echarts", "name", "data", "loading", "statisticsData", "chartStyle", "width", "height", "marginBottom", "charts", "mounted", "getStatisticsData", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "chart", "dispose", "methods", "url", "post", "then", "res", "code", "resdata", "$nextTick", "initAllCharts", "$message", "message", "type", "offset", "catch", "error", "console", "question", "index", "initChart", "questionData", "chartId", "qid", "chartDom", "document", "getElementById", "init", "push", "pieData", "options", "legendData", "map", "item", "option", "title", "text", "left", "top", "textStyle", "fontSize", "fontWeight", "tooltip", "trigger", "formatter", "legend", "orient", "series", "radius", "center", "avoidLabelOverlap", "label", "show", "position", "emphasis", "labelLine", "setOption", "window", "addEventListener", "resize"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionsStatistics.vue"], "sourcesContent": ["<template>\n  <div class=\"page-title-box\">\n    <ol class=\"breadcrumb float-right\">\n      <li class=\"breadcrumb-item\"><a href=\"javascript:void(0);\" id=\"title1\">调查问卷管理</a></li>\n      <li class=\"breadcrumb-item active\" id=\"title2\">{{ this.$route.meta.title }}</li>\n    </ol>\n    <h4 class=\"page-title\" id=\"title3\">{{ this.$route.meta.title }}</h4>\n  </div>\n\n  <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n    <div v-if=\"loading\" class=\"loading\">\n      <el-loading text=\"正在加载统计数据...\"></el-loading>\n    </div>\n    \n    <div v-else>\n      <div v-if=\"statisticsData.length === 0\" class=\"no-data\">\n        <el-empty description=\"暂无问卷统计数据\"></el-empty>\n      </div>\n      \n      <div v-else>\n        <div v-for=\"(question, index) in statisticsData\" :key=\"question.qid\" class=\"chart-container\">\n          <h3 class=\"question-title\">{{ index + 1 }}. {{ question.question }}</h3>\n          <div :id=\"'chart-' + question.qid\" class=\"chart\" :style=\"chartStyle\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nimport * as echarts from \"echarts\";\n\nexport default {\n  name: 'QuestionsStatistics',\n  data() {\n    return {\n      loading: true,\n      statisticsData: [],\n      chartStyle: { \n        width: \"100%\", \n        height: \"400px\",\n        marginBottom: \"30px\"\n      },\n      charts: [] // 存储图表实例\n    };\n  },\n  \n  mounted() {\n    this.getStatisticsData();\n  },\n  \n  beforeDestroy() {\n    // 销毁所有图表实例\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.dispose();\n      }\n    });\n  },\n  \n  methods: {\n    // 获取统计数据\n    getStatisticsData() {\n      this.loading = true;\n      let url = base + \"/results/statistics\";\n      \n      request.post(url, {}).then((res) => {\n        if (res.code === 200 && res.resdata) {\n          this.statisticsData = res.resdata;\n          this.$nextTick(() => {\n            this.initAllCharts();\n          });\n        } else {\n          this.$message({\n            message: \"获取统计数据失败\",\n            type: \"error\",\n            offset: 320,\n          });\n        }\n        this.loading = false;\n      }).catch(error => {\n        console.error(\"获取统计数据失败:\", error);\n        this.$message({\n          message: \"获取统计数据失败\",\n          type: \"error\",\n          offset: 320,\n        });\n        this.loading = false;\n      });\n    },\n    \n    // 初始化所有图表\n    initAllCharts() {\n      this.statisticsData.forEach((question, index) => {\n        this.initChart(question, index);\n      });\n    },\n    \n    // 初始化单个图表\n    initChart(questionData, index) {\n      const chartId = 'chart-' + questionData.qid;\n      const chartDom = document.getElementById(chartId);\n      \n      if (!chartDom) {\n        console.error(`Chart container not found: ${chartId}`);\n        return;\n      }\n      \n      const chart = echarts.init(chartDom);\n      this.charts.push(chart);\n      \n      // 准备饼图数据\n      const pieData = questionData.options || [];\n      const legendData = pieData.map(item => item.name);\n      \n      const option = {\n        title: {\n          text: `问题 ${index + 1} 统计`,\n          left: 'center',\n          top: 20,\n          textStyle: {\n            fontSize: 16,\n            fontWeight: 'bold'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 'left',\n          top: 'middle',\n          data: legendData\n        },\n        series: [\n          {\n            name: '选项统计',\n            type: 'pie',\n            radius: ['40%', '70%'],\n            center: ['60%', '50%'],\n            avoidLabelOverlap: false,\n            label: {\n              show: true,\n              position: 'outside',\n              formatter: '{b}: {c} ({d}%)'\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: '16',\n                fontWeight: 'bold'\n              }\n            },\n            labelLine: {\n              show: true\n            },\n            data: pieData\n          }\n        ]\n      };\n      \n      chart.setOption(option);\n      \n      // 响应式调整\n      window.addEventListener('resize', () => {\n        chart.resize();\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.loading {\n  text-align: center;\n  padding: 50px;\n}\n\n.no-data {\n  text-align: center;\n  padding: 50px;\n}\n\n.chart-container {\n  margin-bottom: 50px;\n  padding: 20px;\n  border: 1px solid #e6e6e6;\n  border-radius: 8px;\n  background-color: #fff;\n}\n\n.question-title {\n  margin-bottom: 20px;\n  color: #333;\n  font-size: 18px;\n  font-weight: bold;\n  text-align: center;\n}\n\n.chart {\n  margin: 0 auto;\n}\n</style>\n"], "mappings": ";AA8BA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,OAAO,KAAKC,OAAM,MAAO,SAAS;AAElC,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,cAAc,EAAE,EAAE;MAClBC,UAAU,EAAE;QACVC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,OAAO;QACfC,YAAY,EAAE;MAChB,CAAC;MACDC,MAAM,EAAE,EAAC,CAAE;IACb,CAAC;EACH,CAAC;;EAEDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd;IACA,IAAI,CAACH,MAAM,CAACI,OAAO,CAACC,KAAI,IAAK;MAC3B,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACC,OAAO,CAAC,CAAC;MACjB;IACF,CAAC,CAAC;EACJ,CAAC;EAEDC,OAAO,EAAE;IACP;IACAL,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACR,OAAM,GAAI,IAAI;MACnB,IAAIc,GAAE,GAAIlB,IAAG,GAAI,qBAAqB;MAEtCD,OAAO,CAACoB,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAIA,GAAG,CAACC,IAAG,KAAM,GAAE,IAAKD,GAAG,CAACE,OAAO,EAAE;UACnC,IAAI,CAAClB,cAAa,GAAIgB,GAAG,CAACE,OAAO;UACjC,IAAI,CAACC,SAAS,CAAC,MAAM;YACnB,IAAI,CAACC,aAAa,CAAC,CAAC;UACtB,CAAC,CAAC;QACJ,OAAO;UACL,IAAI,CAACC,QAAQ,CAAC;YACZC,OAAO,EAAE,UAAU;YACnBC,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;QACA,IAAI,CAACzB,OAAM,GAAI,KAAK;MACtB,CAAC,CAAC,CAAC0B,KAAK,CAACC,KAAI,IAAK;QAChBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACL,QAAQ,CAAC;UACZC,OAAO,EAAE,UAAU;UACnBC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;QACF,IAAI,CAACzB,OAAM,GAAI,KAAK;MACtB,CAAC,CAAC;IACJ,CAAC;IAED;IACAqB,aAAaA,CAAA,EAAG;MACd,IAAI,CAACpB,cAAc,CAACS,OAAO,CAAC,CAACmB,QAAQ,EAAEC,KAAK,KAAK;QAC/C,IAAI,CAACC,SAAS,CAACF,QAAQ,EAAEC,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,SAASA,CAACC,YAAY,EAAEF,KAAK,EAAE;MAC7B,MAAMG,OAAM,GAAI,QAAO,GAAID,YAAY,CAACE,GAAG;MAC3C,MAAMC,QAAO,GAAIC,QAAQ,CAACC,cAAc,CAACJ,OAAO,CAAC;MAEjD,IAAI,CAACE,QAAQ,EAAE;QACbP,OAAO,CAACD,KAAK,CAAE,8BAA6BM,OAAQ,EAAC,CAAC;QACtD;MACF;MAEA,MAAMtB,KAAI,GAAId,OAAO,CAACyC,IAAI,CAACH,QAAQ,CAAC;MACpC,IAAI,CAAC7B,MAAM,CAACiC,IAAI,CAAC5B,KAAK,CAAC;;MAEvB;MACA,MAAM6B,OAAM,GAAIR,YAAY,CAACS,OAAM,IAAK,EAAE;MAC1C,MAAMC,UAAS,GAAIF,OAAO,CAACG,GAAG,CAACC,IAAG,IAAKA,IAAI,CAAC9C,IAAI,CAAC;MAEjD,MAAM+C,MAAK,GAAI;QACbC,KAAK,EAAE;UACLC,IAAI,EAAG,MAAKjB,KAAI,GAAI,CAAC,KAAK;UAC1BkB,IAAI,EAAE,QAAQ;UACdC,GAAG,EAAE,EAAE;UACPC,SAAS,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE;UACd;QACF,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE;QACb,CAAC;QACDC,MAAM,EAAE;UACNC,MAAM,EAAE,UAAU;UAClBT,IAAI,EAAE,MAAM;UACZC,GAAG,EAAE,QAAQ;UACblD,IAAI,EAAE2C;QACR,CAAC;QACDgB,MAAM,EAAE,CACN;UACE5D,IAAI,EAAE,MAAM;UACZ0B,IAAI,EAAE,KAAK;UACXmC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,iBAAiB,EAAE,KAAK;UACxBC,KAAK,EAAE;YACLC,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,SAAS;YACnBT,SAAS,EAAE;UACb,CAAC;UACDU,QAAQ,EAAE;YACRH,KAAK,EAAE;cACLC,IAAI,EAAE,IAAI;cACVZ,QAAQ,EAAE,IAAI;cACdC,UAAU,EAAE;YACd;UACF,CAAC;UACDc,SAAS,EAAE;YACTH,IAAI,EAAE;UACR,CAAC;UACDhE,IAAI,EAAEyC;QACR;MAEJ,CAAC;MAED7B,KAAK,CAACwD,SAAS,CAACtB,MAAM,CAAC;;MAEvB;MACAuB,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;QACtC1D,KAAK,CAAC2D,MAAM,CAAC,CAAC;MAChB,CAAC,CAAC;IACJ;EACF;AACF,CAAC"}]}