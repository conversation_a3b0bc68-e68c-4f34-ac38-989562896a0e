<template>
  <div class="statistics-container">
    <h2 class="statistics-title">您的问卷调查统计结果</h2>
    
    <div v-if="loading" class="loading">
      <el-loading text="正在加载统计数据..."></el-loading>
    </div>
    
    <div v-else>
      <div v-if="statisticsData.length === 0" class="no-data">
        <el-empty description="暂无统计数据"></el-empty>
      </div>
      
      <div v-else>
        <div class="intro-text">
          <p>感谢您参与我们的问卷调查！以下是所有用户的统计结果：</p>
        </div>
        
        <div v-for="(question, index) in statisticsData" :key="question.qid" class="chart-container">
          <h3 class="question-title">{{ index + 1 }}. {{ question.question }}</h3>
          <div :id="'user-chart-' + question.qid" class="chart" :style="chartStyle"></div>
        </div>
        
        <div class="action-buttons">
          <el-button type="primary" @click="goBack">返回</el-button>
          <el-button type="success" @click="retakeQuestionnaire">重新填写问卷</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import request, { base } from "../../../utils/http";
import * as echarts from "echarts";

export default {
  name: 'QuestionnaireStatistics',
  data() {
    return {
      loading: true,
      statisticsData: [],
      chartStyle: { 
        width: "100%", 
        height: "350px",
        marginBottom: "20px"
      },
      charts: [] // 存储图表实例
    };
  },
  
  mounted() {
    this.getStatisticsData();
  },
  
  beforeDestroy() {
    // 销毁所有图表实例
    this.charts.forEach(chart => {
      if (chart) {
        chart.dispose();
      }
    });
  },
  
  methods: {
    // 获取统计数据
    getStatisticsData() {
      this.loading = true;
      let url = base + "/results/statistics";
      
      request.post(url, {}).then((res) => {
        if (res.code === 200 && res.resdata) {
          this.statisticsData = res.resdata;
          this.$nextTick(() => {
            this.initAllCharts();
          });
        } else {
          this.$message({
            message: "获取统计数据失败",
            type: "error",
            offset: 320,
          });
        }
        this.loading = false;
      }).catch(error => {
        console.error("获取统计数据失败:", error);
        this.$message({
          message: "获取统计数据失败",
          type: "error",
          offset: 320,
        });
        this.loading = false;
      });
    },
    
    // 初始化所有图表
    initAllCharts() {
      this.statisticsData.forEach((question, index) => {
        this.initChart(question, index);
      });
    },
    
    // 初始化单个图表
    initChart(questionData, index) {
      const chartId = 'user-chart-' + questionData.qid;
      const chartDom = document.getElementById(chartId);
      
      if (!chartDom) {
        console.error(`Chart container not found: ${chartId}`);
        return;
      }
      
      const chart = echarts.init(chartDom);
      this.charts.push(chart);
      
      // 准备饼图数据
      const pieData = questionData.options || [];
      const legendData = pieData.map(item => item.name);
      
      // 定义颜色方案
      const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];
      
      const option = {
        title: {
          text: `问题 ${index + 1} 统计结果`,
          left: 'center',
          top: 10,
          textStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}人 ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          left: 'center',
          bottom: 10,
          data: legendData
        },
        color: colors,
        series: [
          {
            name: '选择人数',
            type: 'pie',
            radius: ['30%', '60%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}\n{c}人 ({d}%)',
              fontSize: 12
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: pieData
          }
        ]
      };
      
      chart.setOption(option);
      
      // 响应式调整
      window.addEventListener('resize', () => {
        chart.resize();
      });
    },
    
    // 返回
    goBack() {
      this.$router.go(-1);
    },
    
    // 重新填写问卷
    retakeQuestionnaire() {
      this.$confirm('确定要重新填写问卷吗？这将清除您之前的答案。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 跳转到问卷页面
        this.$router.push('/test');
      }).catch(() => {
        // 用户取消
      });
    }
  }
};
</script>

<style scoped>
.statistics-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f9f9f9;
  min-height: 100vh;
}

.statistics-title {
  text-align: center;
  margin-bottom: 30px;
  font-size: 24px;
  color: #333;
}

.loading {
  text-align: center;
  padding: 50px;
}

.no-data {
  text-align: center;
  padding: 50px;
}

.intro-text {
  text-align: center;
  margin-bottom: 30px;
  padding: 15px;
  background-color: #e8f4fd;
  border-radius: 8px;
  color: #666;
}

.chart-container {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.question-title {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
  font-weight: bold;
  text-align: left;
  padding-bottom: 10px;
  border-bottom: 2px solid #409EFF;
}

.chart {
  margin: 0 auto;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
  padding: 20px;
}

.action-buttons .el-button {
  margin: 0 10px;
}
</style>
