{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Menunav.vue?vue&type=template&id=b8d5ba42", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Menunav.vue", "mtime": 1747058728840}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogICAgPFRvcE1lbnUgLz4NCiAgICANCg0KICAgIDxkaXYgY2xhc3M9InBhZ2VfYmciIDpzdHlsZT0ieyBiYWNrZ3JvdW5kOiBgdXJsKCR7cmVxdWlyZSgnQC9hc3NldHMvaW1hZ2VzL2luYmFuLmpwZycpfSkgY2VudGVyIHRvcCBuby1yZXBlYXRgIH0iPjwvZGl2Pg0KICAgICA8ZGl2IGNsYXNzPSJicmVhZF9iZyI+DQogICAgIAk8ZGl2IGNsYXNzPSJjb250YWluZXIiPiAgICANCiAgICAgCQk8ZGl2IGNsYXNzPSJyb3ciPg0KICAgICAJCQk8ZGl2IGNsYXNzPSJjb2wteHMtMTIgY29sLXNtLTEyIGNvbC1tZC0xMiI+DQogICAgIAkJCQk8ZGl2IGNsYXNzPSJicmVhZF9uYXYiPg0KICAgICAJCQkJCTxzcGFuPuaCqOeahOS9jee9ru+8mjwvc3Bhbj48YSBocmVmPSIvaW5kZXgiPummlumhtTwvYT4gJmd0OyA8YSBocmVmPSIiPnt7ICRyb3V0ZS5tZXRhLnRpdGxlIH19PC9hPg0KICAgICAJCQkJPC9kaXY+DQogICAgIAkJCTwvZGl2Pg0KICAgICAJCTwvZGl2Pg0KICAgICAJPC9kaXY+DQogICAgIDwvZGl2Pg0KICAgICA8ZGl2IGNsYXNzPSJjb250YWluZXIiPiAgICANCiAgICAgCTxkaXYgY2xhc3M9InJvdyI+DQogICAgIAkJIDxNZW51IC8+DQogICAgIAkJPCEtLSByaWdodCAtLT4NCiAgICAgCQk8ZGl2IGNsYXNzPSJjb2wteHMtMTIgY29sLXNtLTggY29sLW1kLTkiIHN0eWxlPSJmbG9hdDpyaWdodCI+DQogICAgIAkJCTxkaXYgY2xhc3M9InJpZ2h0X2hlYWQiPg0KICAgICAJCQkJPGgyPjxzcGFuPiB7eyAkcm91dGUubWV0YS50aXRsZSB9fTwvc3Bhbj48L2gyPg0KICAgICAJCQk8L2Rpdj4NCiAgICAgCQkJPGRpdiBjbGFzcz0icmlnaHRfY29udGVudHMgcmVpbWciPg0KIAkJCQkgIDxyb3V0ZXItdmlldyAvPg0KIAkJCQkgICAgPC9kaXY+IA0KICAgICAJCTwvZGl2Pg0KICAgICAJCTwhLS0gbGVmdCAtLT4NCiAgICAgCQkNCiAgICAgCTwvZGl2Pg0KICAgICA8L2Rpdj4NCg0KICAgICAgDQoNCg0KICAgIDxGb290IC8+DQo="}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Menunav.vue"], "names": [], "mappings": ";IACI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;;IAGV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACtH,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACrF,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC;OACN,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC;KACN,CAAC,CAAC,CAAC,CAAC,CAAC;KACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;OACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;OACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,CAAC;OACP,CAAC,CAAC,CAAC,CAAC,CAAC;OACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC;KACN,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;IAKN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/Menunav.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <TopMenu />\r\n    \r\n\r\n    <div class=\"page_bg\" :style=\"{ background: `url(${require('@/assets/images/inban.jpg')}) center top no-repeat` }\"></div>\r\n     <div class=\"bread_bg\">\r\n     \t<div class=\"container\">    \r\n     \t\t<div class=\"row\">\r\n     \t\t\t<div class=\"col-xs-12 col-sm-12 col-md-12\">\r\n     \t\t\t\t<div class=\"bread_nav\">\r\n     \t\t\t\t\t<span>您的位置：</span><a href=\"/index\">首页</a> &gt; <a href=\"\">{{ $route.meta.title }}</a>\r\n     \t\t\t\t</div>\r\n     \t\t\t</div>\r\n     \t\t</div>\r\n     \t</div>\r\n     </div>\r\n     <div class=\"container\">    \r\n     \t<div class=\"row\">\r\n     \t\t <Menu />\r\n     \t\t<!-- right -->\r\n     \t\t<div class=\"col-xs-12 col-sm-8 col-md-9\" style=\"float:right\">\r\n     \t\t\t<div class=\"right_head\">\r\n     \t\t\t\t<h2><span> {{ $route.meta.title }}</span></h2>\r\n     \t\t\t</div>\r\n     \t\t\t<div class=\"right_contents reimg\">\r\n \t\t\t\t  <router-view />\r\n \t\t\t\t    </div> \r\n     \t\t</div>\r\n     \t\t<!-- left -->\r\n     \t\t\r\n     \t</div>\r\n     </div>\r\n\r\n      \r\n\r\n\r\n    <Foot />\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport Menu from \"../../components/Menu\";\r\nimport TopMenu from \"../../components/TopMenu\";\r\nimport Foot from \"../../components/Foot\";\r\n\r\nexport default {\r\n    name: \"Menunav\",\r\n    components: {\r\n        TopMenu,\r\n        Foot,\r\n        Menu\r\n    },\r\n    data() {\r\n        return {\r\n\r\n        };\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    created() {\r\n\r\n    },\r\n    methods: {\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n\r\n<style></style>\r\n \r\n\r\n"]}]}