{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Menu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Menu.vue", "mtime": 1747059578986}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiTWVudSIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7fTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICAvL+WIpOaWreaYr+WQpueZu+W9lQogICAgdmFyIGxuYW1lID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgibG5hbWUiKTsKICAgIGlmIChsbmFtZSA9PSBudWxsKSB7CiAgICAgIC8v5by55Ye65o+Q56S6CiAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgIG1lc3NhZ2U6ICLor7flhYjnmbvlvZUiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgICBvZmZzZXQ6IDMyMAogICAgICB9KTsKICAgICAgLy/ot7PovazliLDnmbvlvZXpobXpnaIKICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi91bG9naW4iKTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIHF1aXQoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuehruiupOmAgOWHuuWQlz8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgc2Vzc2lvblN0b3JhZ2UucmVtb3ZlSXRlbSgibG5hbWUiKTsKICAgICAgICBfdGhpcy4kcm91dGVyLnB1c2goIi91bG9naW4iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "data", "mounted", "lname", "sessionStorage", "getItem", "$message", "message", "type", "offset", "$router", "push", "methods", "quit", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "then", "removeItem", "catch"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Menu.vue"], "sourcesContent": ["<template>\n  \n<div class=\"col-xs-12 col-sm-4 col-md-3\">\n<h3 class=\"left_h3\"><span>我的菜单</span></h3>\n<div class=\"left_column\">\n    <ul class=\"left_nav_ul\" id=\"firstpane\">\n   <li><a class=\"biglink\" href=\"/Uweclome\">欢迎页面</a></li>\n\n<li><a class=\"biglink\" href=\"/ticketpurchases_manage\">我的购票信息</a></li>\n<li><a class=\"biglink\" href=\"/results_manage\">我的问卷结果</a></li>\n<li><a class=\"biglink\" href=\"/Uinfo\">修改个人信息</a></li>\n<li><a class=\"biglink\" href=\"/Upassword\">修改密码</a></li>\n<li><a class=\"biglink\" @click=\"quit()\" style=\"cursor: pointer;\">退出登录</a></li>\n    </ul>\n</div>\n\n</div>\n\n</template>\n<script>\nexport default {\n  name: \"Menu\",\n  data() {\n    return {\n\n    };\n  },\n  mounted() {\n      //判断是否登录\n      var lname = sessionStorage.getItem(\"lname\");\n      if (lname == null) {\n        //弹出提示\n        this.$message({\n          message: \"请先登录\",\n          type: \"warning\",\n          offset: 320,\n        });\n        //跳转到登录页面\n        this.$router.push(\"/ulogin\");\n      }\n  },\n  methods: {\n    quit() {\n      var _this = this;\n      this.$confirm(\"确认退出吗?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n          .then(() => {\n            sessionStorage.removeItem(\"lname\");\n            _this.$router.push(\"/ulogin\");\n          })\n          .catch(() => { });\n    },\n\n  },\n};\n</script>\n\n<style></style>\n\n\n"], "mappings": ";AAoBA,eAAe;EACbA,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO,CAEP,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACN;IACA,IAAIC,KAAI,GAAIC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAI,IAAK,IAAI,EAAE;MACjB;MACA,IAAI,CAACG,QAAQ,CAAC;QACZC,OAAO,EAAE,MAAM;QACfC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;MACF;MACA,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;IAC9B;EACJ,CAAC;EACDC,OAAO,EAAE;IACPC,IAAIA,CAAA,EAAG;MACL,IAAIC,KAAI,GAAI,IAAI;MAChB,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC5BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBT,IAAI,EAAE;MACR,CAAC,EACIU,IAAI,CAAC,MAAM;QACVd,cAAc,CAACe,UAAU,CAAC,OAAO,CAAC;QAClCL,KAAK,CAACJ,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;MAC/B,CAAC,EACAS,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACvB;EAEF;AACF,CAAC"}]}