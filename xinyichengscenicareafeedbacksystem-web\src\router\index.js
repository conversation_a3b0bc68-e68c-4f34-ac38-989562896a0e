import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login'),
    meta: {
      requireAuth: false
    }
  },

  {
    path: '/main',
    name: 'Main',
    component: () => import('../views/Main'),
    redirect: "/home",
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('../views/admin/Home'),
        meta: {
          requireAuth: true,title:'首页'
        }

      },
     
      {
      path: '/ticketInfoAdd',
      name: 'TicketInfoAdd',
      component: () => import('../views/admin/ticketInfo/TicketInfoAdd'),
      meta: { requiresAuth: true,title: '票务信息添加' }
    },
 {
      path: '/ticketInfoEdit',
      name: 'TicketInfoEdit',
      component: () => import('../views/admin/ticketInfo/TicketInfoEdit'),
      meta: { requiresAuth: true,title: '票务信息修改' }
    },
 {
      path: '/ticketInfoManage',
      name: 'TicketInfoManage',
      component: () => import('../views/admin/ticketInfo/TicketInfoManage'),
      meta: { requiresAuth: true,title: '票务信息管理' }
    },
{
    path: '/ticketInfoDetail',
    name: 'TicketInfoDetail',
    component: () => import('../views/admin/ticketInfo/TicketInfoDetail'),
    meta: { requiresAuth: true,title: '票务信息详情' }
  },
 {
      path: '/ticketpurchasesEdit',
      name: 'TicketpurchasesEdit',
      component: () => import('../views/admin/ticketpurchases/TicketpurchasesEdit'),
      meta: { requiresAuth: true,title: '购票信息修改' }
    },
 {
      path: '/ticketpurchasesManage',
      name: 'TicketpurchasesManage',
      component: () => import('../views/admin/ticketpurchases/TicketpurchasesManage'),
      meta: { requiresAuth: true,title: '购票信息管理' }
    },
{
    path: '/ticketpurchasesDetail',
    name: 'TicketpurchasesDetail',
    component: () => import('../views/admin/ticketpurchases/TicketpurchasesDetail'),
    meta: { requiresAuth: true,title: '购票信息详情' }
  },
 {
      path: '/postsManage',
      name: 'PostsManage',
      component: () => import('../views/admin/posts/PostsManage'),
      meta: { requiresAuth: true,title: '帖子管理' }
    },
{
    path: '/postsDetail',
    name: 'PostsDetail',
    component: () => import('../views/admin/posts/PostsDetail'),
    meta: { requiresAuth: true,title: '帖子详情' }
  },
{
      path: '/usersAdd',
      name: 'UsersAdd',
      component: () => import('../views/admin/users/UsersAdd'),
      meta: { requiresAuth: true,title: '用户添加' }
    },
 {
      path: '/usersEdit',
      name: 'UsersEdit',
      component: () => import('../views/admin/users/UsersEdit'),
      meta: { requiresAuth: true,title: '用户修改' }
    },
 {
      path: '/usersManage',
      name: 'UsersManage',
      component: () => import('../views/admin/users/UsersManage'),
      meta: { requiresAuth: true,title: '用户管理' }
    },
{
    path: '/usersDetail',
    name: 'UsersDetail',
    component: () => import('../views/admin/users/UsersDetail'),
    meta: { requiresAuth: true,title: '用户详情' }
  },
 {
      path: '/boardEdit',
      name: 'BoardEdit',
      component: () => import('../views/admin/board/BoardEdit'),
      meta: { requiresAuth: true,title: '意见反馈修改' }
    },
 {
      path: '/boardManage',
      name: 'BoardManage',
      component: () => import('../views/admin/board/BoardManage'),
      meta: { requiresAuth: true,title: '意见反馈管理' }
    },
{
    path: '/boardDetail',
    name: 'BoardDetail',
    component: () => import('../views/admin/board/BoardDetail'),
    meta: { requiresAuth: true,title: '意见反馈详情' }
  },
{
      path: '/scenicareaAdd',
      name: 'ScenicareaAdd',
      component: () => import('../views/admin/scenicarea/ScenicareaAdd'),
      meta: { requiresAuth: true,title: '景区添加' }
    },
 {
      path: '/scenicareaEdit',
      name: 'ScenicareaEdit',
      component: () => import('../views/admin/scenicarea/ScenicareaEdit'),
      meta: { requiresAuth: true,title: '景区修改' }
    },
 {
      path: '/scenicareaManage',
      name: 'ScenicareaManage',
      component: () => import('../views/admin/scenicarea/ScenicareaManage'),
      meta: { requiresAuth: true,title: '景区管理' }
    },
{
    path: '/scenicareaDetail',
    name: 'ScenicareaDetail',
    component: () => import('../views/admin/scenicarea/ScenicareaDetail'),
    meta: { requiresAuth: true,title: '景区详情' }
  },
{
      path: '/questionsAdd',
      name: 'QuestionsAdd',
      component: () => import('../views/admin/questions/QuestionsAdd'),
      meta: { requiresAuth: true,title: '调查问卷添加' }
    },
 {
      path: '/questionsEdit',
      name: 'QuestionsEdit',
      component: () => import('../views/admin/questions/QuestionsEdit'),
      meta: { requiresAuth: true,title: '调查问卷修改' }
    },
 {
      path: '/questionsManage',
      name: 'QuestionsManage',
      component: () => import('../views/admin/questions/QuestionsManage'),
      meta: { requiresAuth: true,title: '调查问卷管理' }
    },
{
    path: '/questionsDetail',
    name: 'QuestionsDetail',
    component: () => import('../views/admin/questions/QuestionsDetail'),
    meta: { requiresAuth: true,title: '调查问卷详情' }
  },
{
    path: '/questionsStatistics',
    name: 'QuestionsStatistics',
    component: () => import('../views/admin/questions/QuestionsStatistics'),
    meta: { requiresAuth: true,title: '问卷调查统计' }
  },
{
      path: '/adminAdd',
      name: 'AdminAdd',
      component: () => import('../views/admin/admin/AdminAdd'),
      meta: { requiresAuth: true,title: '管理员添加' }
    },
 {
      path: '/adminEdit',
      name: 'AdminEdit',
      component: () => import('../views/admin/admin/AdminEdit'),
      meta: { requiresAuth: true,title: '管理员修改' }
    },
 {
      path: '/adminManage',
      name: 'AdminManage',
      component: () => import('../views/admin/admin/AdminManage'),
      meta: { requiresAuth: true,title: '管理员管理' }
    },
{
      path: '/newsAdd',
      name: 'NewsAdd',
      component: () => import('../views/admin/news/NewsAdd'),
      meta: { requiresAuth: true,title: '新闻资讯添加' }
    },
 {
      path: '/newsEdit',
      name: 'NewsEdit',
      component: () => import('../views/admin/news/NewsEdit'),
      meta: { requiresAuth: true,title: '新闻资讯修改' }
    },
 {
      path: '/newsManage',
      name: 'NewsManage',
      component: () => import('../views/admin/news/NewsManage'),
      meta: { requiresAuth: true,title: '新闻资讯管理' }
    },
{
    path: '/newsDetail',
    name: 'NewsDetail',
    component: () => import('../views/admin/news/NewsDetail'),
    meta: { requiresAuth: true,title: '新闻资讯详情' }
  },
{
      path: '/categoryAdd',
      name: 'CategoryAdd',
      component: () => import('../views/admin/category/CategoryAdd'),
      meta: { requiresAuth: true,title: '意见类别添加' }
    },
 {
      path: '/categoryEdit',
      name: 'CategoryEdit',
      component: () => import('../views/admin/category/CategoryEdit'),
      meta: { requiresAuth: true,title: '意见类别修改' }
    },
 {
      path: '/categoryManage',
      name: 'CategoryManage',
      component: () => import('../views/admin/category/CategoryManage'),
      meta: { requiresAuth: true,title: '意见类别管理' }
    },
{
    path: '/categoryDetail',
    name: 'CategoryDetail',
    component: () => import('../views/admin/category/CategoryDetail'),
    meta: { requiresAuth: true,title: '意见类别详情' }
  },
{
      path: '/resultsAdd',
      name: 'ResultsAdd',
      component: () => import('../views/admin/results/ResultsAdd'),
      meta: { requiresAuth: true,title: '问卷结果添加' }
    },
 {
      path: '/resultsEdit',
      name: 'ResultsEdit',
      component: () => import('../views/admin/results/ResultsEdit'),
      meta: { requiresAuth: true,title: '问卷结果修改' }
    },
 {
      path: '/resultsManage',
      name: 'ResultsManage',
      component: () => import('../views/admin/results/ResultsManage'),
      meta: { requiresAuth: true,title: '问卷结果管理' }
    },
{
    path: '/resultsDetail',
    name: 'ResultsDetail',
    component: () => import('../views/admin/results/ResultsDetail'),
    meta: { requiresAuth: true,title: '问卷结果详情' }
  },
{
    path: '/total1',
    name: 'Total1',
    component: () => import('../views/admin/total/Total1'),
    meta: { requiresAuth: true,title: '意见类别' }
  },
{
    path: '/total2',
    name: 'Total2',
    component: () => import('../views/admin/total/Total2'),
    meta: { requiresAuth: true,title: '用户统计' }
  },
{
    path: '/total3',
    name: 'Total3',
    component: () => import('../views/admin/total/Total3'),
    meta: { requiresAuth: true,title: '票务统计' }
  },
{
    path: '/total4',
    name: 'Total4',
    component: () => import('../views/admin/total/Total4'),
    meta: { requiresAuth: true,title: '图表4' }
  },

     {
          path: '/password',
          name: 'Password',
          component: () => import('../views/admin/system/Password'),
          meta: {
            requireAuth: true,title:'修改密码'
          }
     },
    ]
  },
    
  {
    path: '/',
    name: '/',
    component: () => import('../views/Index'),
    redirect: "/default",
    children: [
    {
        path: '/default',
        name: 'Default',
        component: () => import('../views/web/Default'),
    },
    ]
},
{
    path: '/index',
    name: 'Index',
    component: () => import('../views/Index'),
    redirect: "/default",
    children: [
    {
        path: '/default',
        name: 'Default',
        component: () => import('../views/web/Default'),
    },
    ]
},

{
    path: '/web', 
    name: 'web',
    component: () => import('../views/web/Leftnav'),
    children: [
    
      {
        path: '/ureg',
        name: 'Ureg',
        component: () => import('../views/web/Ureg'),
        meta: {
          title: '用户注册',
        }
      },

      {
        path: '/ulogin',
        name: 'Ulogin',
        component: () => import('../views/web/Ulogin'),
        meta: {
          title: '用户登录',
        }
      },

      {
        path: '/newslist',
        name: 'NewsList',
        component: () => import('../views/web/NewsList'),
        meta: {
          title: '新闻资讯',
        }
      },

      {
        path: '/ticketinfolist',
        name: 'TicketInfoList',
        component: () => import('../views/web/TicketInfoList'),
        meta: {
          title: '票务信息列表',
        }
      },

      {
        path: '/newsview',
        name: 'NewsView',
        component: () => import('../views/web/NewsView'),
        meta: {
          title: '新闻资讯详情',
        }
      },

      {
        path: '/ticketinfoview',
        name: 'TicketInfoView',
        component: () => import('../views/web/TicketInfoView'),
        meta: {
          title: '票务信息详情',
        }
      },

      {
        path: '/scenicareaview',
        name: 'ScenicareaView',
        component: () => import('../views/web/ScenicareaView'),
        meta: {
          title: '景区详情',
        }
      },

      {
        path: '/board',
        name: 'Board',
        component: () => import('../views/web/Board'),
        meta: {
          title: '意见反馈',
        }
      },

      {
        path: '/bbs',
        name: 'Bbs',
        component: () => import('../views/web/Bbs'),
        meta: {
          title: '互动交流列表',
        }
      },

      {
        path: '/bbsview',
        name: 'BbsView',
        component: () => import('../views/web/BbsView'),
        meta: {
          title: '互动交流详情',
        }
      },
  

    ]
},

{
    path: '/menunav',
    name: 'Menunav',
    component: () => import('../views/web/Menunav'),
    children: [
    
      {
        path: '/uweclome',
        name: 'Uweclome',
        component: () => import('../views/web/Uweclome'),
        meta: {
          title: '欢迎页面',
        }
      },

      {
        path: '/uinfo',
        name: 'Uinfo',
        component: () => import('../views/web/Uinfo'),
        meta: {
          title: '修改个人信息',
        }
      },

      {
        path: '/upassword',
        name: 'Upassword',
        component: () => import('../views/web/Upassword'),
        meta: {
          title: '修改密码',
        }
      },

      {
        path: '/posts_manage',
        name: 'Posts_Manage',
        component: () => import('../views/web/Posts_Manage'),
        meta: {
          title: '帖子管理',
        }
      },

      {
        path: '/posts_edit',
        name: 'Posts_Edit',
        component: () => import('../views/web/Posts_Edit'),
        meta: {
          title: '帖子编辑',
        }
      },

      {
        path: '/ticketpurchases_add',
        name: 'Ticketpurchases_Add',
        component: () => import('../views/web/Ticketpurchases_Add'),
        meta: {
          title: '购票信息添加',
        }
      },

      {
        path: '/ticketpurchases_manage',
        name: 'Ticketpurchases_Manage',
        component: () => import('../views/web/Ticketpurchases_Manage'),
        meta: {
          title: '购票信息管理',
        }
      },

      {
        path: '/ticketpurchases_edit',
        name: 'Ticketpurchases_Edit',
        component: () => import('../views/web/Ticketpurchases_Edit'),
        meta: {
          title: '购票信息编辑',
        }
      },

      {
        path: '/ticketpurchases_show',
        name: 'Ticketpurchases_Show',
        component: () => import('../views/web/Ticketpurchases_Show'),
        meta: {
          title: '购票信息详情',
        }
      },
      {
        path: '/test',
        name: 'Test',
        component: () => import('../views/web/Test'),
        meta: {
          title: '问卷调查',
        }
      },

      {
        path: '/questionnairestatistics',
        name: 'QuestionnaireStatistics',
        component: () => import('../views/web/QuestionnaireStatistics'),
        meta: {
          title: '问卷统计结果',
        }
      },
      {
        path: '/results_add',
        name: 'Results_Add',
        component: () => import('../views/web/Results_Add'),
        meta: {
          title: '问卷结果添加',
        }
      },

      {
        path: '/results_manage',
        name: 'Results_Manage',
        component: () => import('../views/web/Results_Manage'),
        meta: {
          title: '问卷结果管理',
        }
      },

      {
        path: '/results_edit',
        name: 'Results_Edit',
        component: () => import('../views/web/Results_Edit'),
        meta: {
          title: '问卷结果编辑',
        }
      },

      {
        path: '/results_show',
        name: 'Results_Show',
        component: () => import('../views/web/Results_Show'),
        meta: {
          title: '问卷结果详情',
        }
      },


    ]
},

]



const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})



router.beforeEach((to, from, next) => {
  if (to.path == '/') {
    sessionStorage.removeItem('userLname');
    sessionStorage.removeItem('role');
  }
  let currentUser = sessionStorage.getItem('userLname');
  console.log(to + "  to.meta.requireAuth");

  if (to.meta.requireAuth) {
    if (!currentUser && to.path != '/login') {
      next({ path: '/login' });
    } else {
      next();
    }
  } else {

    next();
  }
})

export default router


