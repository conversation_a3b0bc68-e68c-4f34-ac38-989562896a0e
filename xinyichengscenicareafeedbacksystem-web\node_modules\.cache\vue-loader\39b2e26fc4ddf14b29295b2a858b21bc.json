{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionnaireStatistics.vue?vue&type=style&index=0&id=438aee5a&scoped=true&lang=css", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionnaireStatistics.vue", "mtime": 1749040548672}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749040033032}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749040034646}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749040033671}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5xdWVzdGlvbm5haXJlLXN0YXRpc3RpY3MgewogIHBhZGRpbmc6IDIwcHg7Cn0KCi5wYWdlLWhlYWRlciB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBwYWRkaW5nLWJvdHRvbTogMTBweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VlZTsKfQoKLnBhZ2UtaGVhZGVyIGgyIHsKICBtYXJnaW46IDA7CiAgY29sb3I6ICMzMzM7Cn0KCi5zdGF0aXN0aWNzLWNvbnRlbnQgewogIGJhY2tncm91bmQ6ICNmZmY7CiAgcGFkZGluZzogMjBweDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwwLDAsMC4xKTsKfQoKLmNoYXJ0LWNvbnRhaW5lciB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwp9Cgoubm8tZGF0YSB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIHBhZGRpbmc6IDUwcHg7CiAgY29sb3I6ICM5OTk7CiAgZm9udC1zaXplOiAxNnB4Owp9Cgouc3RhdGlzdGljcy10YWJsZSB7CiAgbWFyZ2luLXRvcDogMzBweDsKfQoKLnN0YXRpc3RpY3MtdGFibGUgaDMgewogIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgY29sb3I6ICMzMzM7Cn0K"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionnaireStatistics.vue"], "names": [], "mappings": ";AAyIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/admin/questions/QuestionnaireStatistics.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"questionnaire-statistics\">\n    <div class=\"page-header\">\n      <h2>问卷调查统计</h2>\n    </div>\n    \n    <div v-loading=\"loading\" class=\"statistics-content\">\n      <div v-if=\"statisticsData.length > 0\" class=\"chart-container\">\n        <div id=\"statisticsChart\" style=\"width: 100%; height: 500px;\"></div>\n      </div>\n      \n      <div v-else-if=\"!loading\" class=\"no-data\">\n        <p>暂无问卷统计数据</p>\n      </div>\n      \n      <!-- 统计表格 -->\n      <div v-if=\"statisticsData.length > 0\" class=\"statistics-table\" style=\"margin-top: 30px;\">\n        <h3>详细统计数据</h3>\n        <el-table :data=\"statisticsData\" border stripe style=\"width: 100%\">\n          <el-table-column prop=\"name\" label=\"满意度\" align=\"center\"></el-table-column>\n          <el-table-column prop=\"value\" label=\"数量\" align=\"center\"></el-table-column>\n          <el-table-column label=\"占比\" align=\"center\">\n            <template #default=\"scope\">\n              {{ getPercentage(scope.row.value) }}%\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nimport * as echarts from 'echarts';\n\nexport default {\n  name: \"QuestionnaireStatistics\",\n  data() {\n    return {\n      loading: false,\n      statisticsData: [],\n      totalCount: 0\n    };\n  },\n  created() {\n    this.getStatistics();\n  },\n  methods: {\n    // 获取统计数据\n    getStatistics() {\n      this.loading = true;\n      let url = base + \"/results/statistics\";\n      request.post(url).then((res) => {\n        this.loading = false;\n        if (res.resdata && res.resdata.length > 0) {\n          this.statisticsData = res.resdata;\n          this.totalCount = this.statisticsData.reduce((sum, item) => sum + item.value, 0);\n          this.$nextTick(() => {\n            this.renderChart();\n          });\n        }\n      }).catch(error => {\n        this.loading = false;\n        console.error(\"获取统计数据失败:\", error);\n        this.$message({\n          message: \"获取统计数据失败\",\n          type: \"error\",\n          offset: 320,\n        });\n      });\n    },\n\n    // 渲染饼图\n    renderChart() {\n      const chartDom = document.getElementById('statisticsChart');\n      if (!chartDom) return;\n      \n      const myChart = echarts.init(chartDom);\n      const option = {\n        title: {\n          text: '问卷调查满意度统计',\n          left: 'center',\n          textStyle: {\n            fontSize: 18,\n            fontWeight: 'bold'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 'left',\n          top: 'middle'\n        },\n        series: [\n          {\n            name: '满意度统计',\n            type: 'pie',\n            radius: ['40%', '70%'],\n            center: ['60%', '50%'],\n            data: this.statisticsData,\n            emphasis: {\n              itemStyle: {\n                shadowBlur: 10,\n                shadowOffsetX: 0,\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\n              }\n            },\n            label: {\n              show: true,\n              formatter: '{b}: {c}\\n({d}%)'\n            }\n          }\n        ],\n        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4']\n      };\n      myChart.setOption(option);\n      \n      // 响应式处理\n      window.addEventListener('resize', () => {\n        myChart.resize();\n      });\n    },\n\n    // 计算百分比\n    getPercentage(value) {\n      if (this.totalCount === 0) return 0;\n      return ((value / this.totalCount) * 100).toFixed(1);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.questionnaire-statistics {\n  padding: 20px;\n}\n\n.page-header {\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #eee;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.statistics-content {\n  background: #fff;\n  padding: 20px;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.chart-container {\n  text-align: center;\n}\n\n.no-data {\n  text-align: center;\n  padding: 50px;\n  color: #999;\n  font-size: 16px;\n}\n\n.statistics-table {\n  margin-top: 30px;\n}\n\n.statistics-table h3 {\n  margin-bottom: 15px;\n  color: #333;\n}\n</style>\n"]}]}