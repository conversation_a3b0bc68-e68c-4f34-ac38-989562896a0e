package com.service;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.ResultsMapper;
import com.model.Results;
import com.util.PageBean;
@Service
public class ResultsServiceImpl implements ResultsService{
        
	@Autowired
	private ResultsMapper resultsMapper;

	//查询多条记录
	public List<Results> queryResultsList(Results results,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(results, page);
		
		List<Results> getResults = resultsMapper.query(map);
		
		return getResults;
	}
	
	//得到记录总数
	@Override
	public int getCount(Results results) {
		Map<String, Object> map = getQueryMap(results, null);
		int count = resultsMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Results results,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(results!=null){
			map.put("id", results.getId());
			map.put("qid", results.getQid());
			map.put("question", results.getQuestion());
			map.put("status", results.getStatus());
			map.put("account", results.getAccount());
			map.put("addtime", results.getAddtime());
			map.put("sort", results.getSort());
			map.put("condition", results.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertResults(Results results) throws Exception {
		return resultsMapper.insertResults(results);
	}

	//根据ID删除
	public int deleteResults(int id) throws Exception {
		return resultsMapper.deleteResults(id);
	}

	//更新
	public int updateResults(Results results) throws Exception {
		return resultsMapper.updateResults(results);
	}
	
	//根据ID得到对应的记录
	public Results queryResultsById(int id) throws Exception {
		Results po =  resultsMapper.queryResultsById(id);
		return po;
	}

	//获取问卷统计数据
	@Override
	public List<Map<String, Object>> getQuestionnaireStatistics() throws Exception {
		List<Map<String, Object>> statistics = new ArrayList<>();

		// 获取所有问题的统计数据
		List<Map<String, Object>> questionStats = resultsMapper.getQuestionStatistics();

		for (Map<String, Object> questionStat : questionStats) {
			Map<String, Object> questionData = new HashMap<>();
			questionData.put("qid", questionStat.get("qid"));
			questionData.put("question", questionStat.get("question"));

			// 获取该问题的选项统计
			List<Map<String, Object>> optionStats = resultsMapper.getOptionStatistics((Integer) questionStat.get("qid"));
			questionData.put("options", optionStats);

			statistics.add(questionData);
		}

		return statistics;
	}

	//检查用户是否已完成问卷
	@Override
	public boolean checkUserCompleted(String account) throws Exception {
		int count = resultsMapper.getUserCompletedCount(account);
		// 获取问卷总题目数
		int totalQuestions = resultsMapper.getTotalQuestionCount();
		return count >= totalQuestions;
	}
}

