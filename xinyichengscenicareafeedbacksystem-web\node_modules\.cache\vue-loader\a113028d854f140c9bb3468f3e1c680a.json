{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\ScenicareaView.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\ScenicareaView.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAiLi4vLi4vLi4vdXRpbHMvaHR0cCI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJTY2VuaWNhcmVhVmlldyIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgICAgICBzY2xpc3Q6ICIiLAogDQoNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIAogICAgdGhpcy5nZXREYXRhcygpOwoNCg0KICB9LA0KICBtZXRob2RzOiB7ICANCiAgICAKICAgIC8v6I635Y+W5YiX6KGo5pWw5o2uCiAgICBnZXREYXRhcygpIHsKICAgICAgICBsZXQgaWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsKICAgICAgICBsZXQgcGFyYSA9IHsgICAgICAgICAgIAogICAgICAgIH07CiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7CiAgICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3NjZW5pY2FyZWEvZ2V0P2lkPSIgKyBpZCA7CiAgICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgIHRoaXMuc2NsaXN0ID0gcmVzLnJlc2RhdGE7CiAgICAgICAgfSk7CiAgICB9LCAgICANCg0KDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\ScenicareaView.vue"], "names": [], "mappings": ";AAsCA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;;IAGhB,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;EAGjB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;QACX,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC;IACN,CAAC;;;EAGH,CAAC;AACH,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/ScenicareaView.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n   \r\n<table style=\"width:100%; line-height:29px;font-size:12px;\">\r\n    <tr>\r\n        <td width=\"330\">\r\n            <img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' +sclist.picurl\"  style='width:350px' />\r\n        </td>\r\n        <td align=\"left\">\r\n            <table width=\"100%\">\r\n                <tr>\r\n                    <td style=\"font-size:16px;font:bold;\">{{sclist.aname}}</td>\r\n                </tr>             \r\n                 <tr><td>位置：{{sclist.locations}}</td></tr>\n <tr><td>联系方式：{{sclist.contactinfo}}</td></tr>\n <tr><td>开放时间：{{sclist.opentime}}</td></tr>\n <tr><td>关闭时间：{{sclist.closetime}}</td></tr>\n <tr><td>门票价格：{{sclist.ticketprice}}</td></tr>\n <tr><td>最佳季节：{{sclist.seasonbest}}</td></tr>\n\r\n                <tr><td></td></tr>\r\n            </table>\r\n            \r\n        </td>\r\n    </tr>\r\n    <tr>\r\n        <td colspan=\"2\" style=\"border-bottom:2px solid #002b57;\"><div style=\"background-color:#002b57;color:white;width: 100px;height: 30px;text-align: center;\">详情</div></td>\r\n    </tr>\r\n    <tr>\r\n        <td colspan=\"2\"><div v-html=\"sclist.description\"></div></td>\r\n    </tr>\r\n\r\n</table>\r\n\r\n\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"ScenicareaView\",\r\n  data() {\r\n    return {\r\n          sclist: \"\",\n \r\n\r\n    };\r\n  },\r\n  created() {\r\n    \n    this.getDatas();\n\r\n\r\n  },\r\n  methods: {  \r\n    \n    //获取列表数据\n    getDatas() {\n        let id = this.$route.query.id;\n        let para = {           \n        };\n        this.listLoading = true;\n        let url = base + \"/scenicarea/get?id=\" + id ;\n        request.post(url, para).then((res) => {\n            this.sclist = res.resdata;\n        });\n    },    \r\n\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n\r\n</style>\r\n\r\n\r\n\r\n"]}]}