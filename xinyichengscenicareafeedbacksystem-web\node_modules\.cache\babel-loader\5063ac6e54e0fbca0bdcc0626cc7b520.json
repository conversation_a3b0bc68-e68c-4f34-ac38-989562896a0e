{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\NewsList.vue?vue&type=template&id=1eb20da6", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\NewsList.vue", "mtime": 1747229072564}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "_hoisted_1", "_createElementBlock", "_Fragment", "_renderList", "$data", "nelist", "item", "key", "id", "src", "nimage", "style", "_hoisted_3", "href", "title", "_hoisted_4", "_hoisted_5", "_toDisplayString", "publishtime", "content", "length", "substring", "_hoisted_6", "_createVNode", "_component_el_pagination", "onCurrentChange", "$options", "handleCurrentChange", "page", "currentPage", "pageSize", "background", "layout", "total", "totalCount"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\NewsList.vue"], "sourcesContent": ["<template>\r\n   <div class=\"container222\">\r\n   \r\n    <div class=\"card222\" v-for=\"item in nelist\" :key=\"item.id\">  \r\n        <img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' + item.nimage\" style=\"height: 230px;\">  \r\n        <div class=\"card-content222\">  \r\n            <h2><a :href=\"'newsView?id='+item.id\">{{item.title}}</a></h2>  \r\n            <div class=\"card-footer222\">  \r\n                <span>发布时间：{{item.publishtime}}</span>           \r\n            </div>            \r\n            <p>{{item.content.length > 70 ? item.content.substring(0,70):item.content}}</p>  \r\n            <a :href=\"'newsView?id='+item.id\" class=\"read-more222\">查看更多</a>  \r\n        </div>  \r\n    </div>  \r\n\r\n</div> \r\n<el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\nbackground layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\r\nstyle=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"NewsList\",\r\n  data() {\r\n    return {\r\n          page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 12, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n    },\r\n    nelist: \"\",\r\n \r\n    };\r\n  },\r\n  created() {\r\n    \r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {  \r\n    \r\n    // 分页\r\n    handleCurrentChange(val) {\r\n        this.page.currentPage = val;\r\n        this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n        var keys = this.$route.query.keys;\r\n        var sql=\"\";\r\n        if(keys){\r\n            sql=\" and title like '%\"+keys+\"%'\";\r\n        }\r\n        let para = {\r\n            condition:sql\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/news/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\r\n        request.post(url, para).then((res) => {\r\n            if (res.resdata.length > 0) {\r\n                this.isPage = true;\r\n            } else {\r\n                this.isPage = false;\r\n            }\r\n            this.page.totalCount = res.count;\r\n            this.nelist = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n\r\n    .container222 {\r\n        display: grid;\r\n        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n        gap: 20px;\r\n        width: 100%;\r\n    }\r\n    .card222 {  \r\n        background-color: white;  \r\n        border-radius: 10px;  \r\n        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);  \r\n        overflow: hidden;  \r\n        transition: transform 0.3s, box-shadow 0.3s;  \r\n    } \r\n    .card222 a{\r\n        text-decoration: none;\r\n    }\r\n    .card222:hover {  \r\n        transform: translateY(-5px);  \r\n        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);  \r\n    }  \r\n    .card222 img {  \r\n        width: 100%;  \r\n        height: 200px;  \r\n        object-fit: cover;  \r\n    }  \r\n    .card-content222 {  \r\n        padding: 15px;  \r\n    }  \r\n    .card-content222 h2 {  \r\n        font-size: 18px;  \r\n        margin: 10px 0;  \r\n    }  \r\n    .card-content222 p {  \r\n        color: #555;  \r\n        font-size: 14px;  \r\n        margin: 10px 0;  \r\n    }  \r\n    .card-footer222 {  \r\n        display: flex;  \r\n        justify-content: space-between;  \r\n        font-size: 12px;  \r\n        color: #888;  \r\n    }  \r\n    .read-more222 {  \r\n        color: #007BFF;  \r\n        text-decoration: none;  \r\n        font-weight: bold;  \r\n        font-size: 12px;\r\n    }  \r\n    .read-more222:hover {  \r\n        text-decoration: underline;  \r\n    }  \r\n\r\n\r\n\r\n</style>\r\n\r\n\r\n\r\n"], "mappings": ";;EACQA,KAAK,EAAC;AAAc;;;EAIfA,KAAK,EAAC;AAAiB;;;EAEnBA,KAAK,EAAC;AAAgB;;;;6DANpCC,mBAAA,CAcG,OAdHC,UAcG,I,kBAZFC,mBAAA,CAUMC,SAAA,QAAAC,WAAA,CAV8BC,KAAA,CAAAC,MAAM,EAAdC,IAAI;yBAAhCL,mBAAA,CAUM;MAVDH,KAAK,EAAC,SAAS;MAAyBS,GAAG,EAAED,IAAI,CAACE;QACnDT,mBAAA,CAA6G;MAAvGU,GAAG,gEAAgEH,IAAI,CAACI,MAAM;MAAEC,KAAsB,EAAtB;QAAA;MAAA;yCACtFZ,mBAAA,CAOM,OAPNa,UAOM,GANFb,mBAAA,CAA6D,aAAzDA,mBAAA,CAAoD;MAAhDc,IAAI,mBAAiBP,IAAI,CAACE;wBAAMF,IAAI,CAACQ,KAAK,wBAAAC,UAAA,E,GAClDhB,mBAAA,CAEM,OAFNiB,UAEM,GADFjB,mBAAA,CAAsC,cAAhC,OAAK,GAAAkB,gBAAA,CAAEX,IAAI,CAACY,WAAW,iB,GAEjCnB,mBAAA,CAA+E,WAAAkB,gBAAA,CAA1EX,IAAI,CAACa,OAAO,CAACC,MAAM,QAAQd,IAAI,CAACa,OAAO,CAACE,SAAS,UAAOf,IAAI,CAACa,OAAO,kBACzEpB,mBAAA,CAA+D;MAA3Dc,IAAI,mBAAiBP,IAAI,CAACE,EAAE;MAAEV,KAAK,EAAC;OAAe,MAAI,iBAAAwB,UAAA,E;oCAKvEC,YAAA,CAE4DC,wBAAA;IAF5CC,eAAc,EAAEC,QAAA,CAAAC,mBAAmB;IAAG,cAAY,EAAEvB,KAAA,CAAAwB,IAAI,CAACC,WAAW;IAAG,WAAS,EAAEzB,KAAA,CAAAwB,IAAI,CAACE,QAAQ;IAC/GC,UAAU,EAAV,EAAU;IAACC,MAAM,EAAC,kCAAkC;IAAEC,KAAK,EAAE7B,KAAA,CAAAwB,IAAI,CAACM,UAAU;IAC5EvB,KAA2C,EAA3C;MAAA;MAAA;IAAA"}]}