{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\TopMenu.vue?vue&type=template&id=91b6ff58", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\TopMenu.vue", "mtime": 1748954728813}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_imports_0", "id", "class", "_createElementVNode", "style", "action", "method", "type", "name", "placeholder", "href", "_hoisted_9", "_hoisted_11", "src", "_createCommentVNode", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "$data", "islogin", "_hoisted_8", "_hoisted_12", "_createBlock", "_component_block", "key", "_hoisted_14", "_toDisplayString", "lname", "onClick", "_cache", "args", "$options", "exit", "_hoisted_16"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\TopMenu.vue"], "sourcesContent": ["<template>\r\n  \r\n   <header>\r\n             <div id=\"top-bar\" class=\"top-bar\">\r\n                 <div class=\"container\">\r\n                     <div class=\"row\">\r\n                         <div class=\"col-md-6\">\r\n                             <div class=\"top-left\">\r\n                                 <ul>\r\n                                     <li><i class=\"glyphicon glyphicon-map-marker\"></i>沈河区沈阳路171号</li>\r\n                                     <li><a href=\"mailto:<EMAIL>\" title=\"email\"><i class=\"glyphicon glyphicon-envelope\"></i><EMAIL></a></li>\r\n                                     <li><a href=\"\" title=\"Call\"><i class=\"glyphicon glyphicon-phone-alt\"></i>024-24843001</a></li>\r\n                                 </ul>\r\n                             </div>\r\n                         </div>\r\n                         <div class=\"col-md-3\">\r\n                             <div class=\"top-right\" style=\"margin-top: -8px;\">\r\n                                 <form action=\"/newsList\" method=\"get\" class=\"search-form\">\r\n                                     <input type=\"text\" name=\"keys\" placeholder=\"搜索...\" aria-label=\"搜索\" style=\"height: 33px; color: #333;\" />\r\n                                     <button type=\"submit\" class=\"btn btn-primary\">搜索</button>\r\n                                 </form>\r\n                             </div>\r\n                         </div>\r\n                         <div class=\"col-md-3 text-right\">\r\n                             <div class=\"top-right\">                               \r\n                                 \r\n         \r\n\t\t\t\t    <li v-if=\"islogin\">\r\n\t\t\t\t\t<a href=\"/Ureg\" style=\"display: contents; color: #fff;\">用户注册</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                <a href=\"/Ulogin\" style=\"display: contents; color: #fff;\">用户登录</a>\r\n\t\t\t\t</li>\r\n\t\t\t\t  <block v-else>\r\n            欢迎您：<a href=\"javascript:void(0);\" style=\"color: #fff;\">{{ lname }}</a>\r\n            &nbsp;|&nbsp;\r\n            <a href=\"javascript:void(0);\" @click=\"exit\" style=\"color: #fff;\">退出登录</a>\r\n          </block>\r\n\t\t\t\t\r\n\r\n                             </div>\r\n                         </div>\r\n                     </div>\r\n                 </div>\r\n             </div>\r\n             <!-- Fixed navbar -->\r\n             <nav class=\"navbar navbar-default navbar-fixed-top\">\r\n                 <div class=\"container\">\r\n                     <div class=\"navbar-header\">\r\n                         <button type=\"button\" class=\"navbar-toggle collapsed\" data-toggle=\"collapse\" data-target=\"#navbar\" aria-expanded=\"false\" aria-controls=\"navbar\"><span class=\"sr-only\">导航菜单</span><span class=\"icon-bar\"></span><span class=\"icon-bar\"></span><span class=\"icon-bar\"></span></button>\r\n                         <a href=\"/index\"><img src=\"../assets/images/logo.png\" style=\"height: 80px;\" class=\"logo\"></a>\r\n                     </div>\r\n                     <div id=\"navbar\" class=\"navbar-collapse collapse\">\r\n                         <ul class=\"nav navbar-nav\">\r\n                             <li class=''><a href=\"/index\">首 页</a> </li>\r\n                             <li class=\" dropdown\"><a href=\"/newsList\">新闻资讯</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/ticketInfoList\">票务信息</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/board\">意见反馈</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/bbs\">互动交流</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/test\">问卷调查</a>  </li>\r\n<li class=\" dropdown\"><a href=\"/uweclome\">个人中心</a>  </li>\r\n\r\n                             <!-- <li class=\" dropdown\">\r\n                                 <a href=\"cpzx.html\">产品中心</a>\r\n                                 <a id=\"app_menudown\" class=\"dropdown-toggle\" data-toggle=\"dropdown\" role=\"button\" aria-expanded=\"false\"><span class=\"glyphicon glyphicon-menu-down btn-xs\"></span></a><ul class=\"dropdown-menu nav_small\" role=\"menu\">\r\n     \r\n                                     <li><a href=\"cpzx.html\">产品分类1</a></li>\r\n     \r\n                                     <li><a href=\"cpzx.html\">产品分类2</a></li>\r\n     \r\n                                     <li><a href=\"cpzx.html\">产品分类3</a></li>\r\n     \r\n                                 </ul>\r\n                             </li> -->\r\n                          \r\n                         </ul>\r\n                     </div>\r\n                     <!--/.nav-collapse -->\r\n                 </div>\r\n             </nav>\r\n         </header>    \r\n\r\n \r\n</template>\r\n<script>\r\nimport request, { base } from \"../../utils/http\";\r\nimport \"../assets/css/qbootstrap.css\"\r\nimport \"../assets/css/qbxslider.css\"\r\nimport \"../assets/css/qstyle.css\"\r\nexport default {\r\n  name: \"TopMenu\",\r\n  data() {\r\n    return {\r\n      islogin: true,\r\n      lname: '',\r\n      ishow: false,\r\n      key: '',\r\n      \r\n    };\r\n  },\r\n  mounted() {\r\n    this.lname = sessionStorage.getItem(\"lname\");\r\n    if (this.lname) {\r\n      this.islogin = false;\r\n    }\r\n  },\r\n  methods: {    \r\n       \r\n    \r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n          .then(() => {\r\n            sessionStorage.removeItem(\"lname\");\r\n            location.href=\"/index\";\r\n          })\r\n          .catch(() => { });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n\r\n</style>\r\n\r\n\r\n"], "mappings": ";OAgD+CA,UAA+B;;EA7C5DC,EAAE,EAAC,SAAS;EAACC,KAAK,EAAC;;;EACfA,KAAK,EAAC;AAAW;;EACbA,KAAK,EAAC;AAAK;;gCAUZC,mBAAA,CAOM;EAPDD,KAAK,EAAC;AAAU,I,aACjBC,mBAAA,CAKM;EALDD,KAAK,EAAC,WAAW;EAACE,KAAyB,EAAzB;IAAA;EAAA;iBACnBD,mBAAA,CAGO;EAHDE,MAAM,EAAC,WAAW;EAACC,MAAM,EAAC,KAAK;EAACJ,KAAK,EAAC;iBACxCC,mBAAA,CAAwG;EAAjGI,IAAI,EAAC,MAAM;EAACC,IAAI,EAAC,MAAM;EAACC,WAAW,EAAC,OAAO;EAAC,YAAU,EAAC,IAAI;EAACL,KAAkC,EAAlC;IAAA;IAAA;EAAA;iBACnED,mBAAA,CAAyD;EAAjDI,IAAI,EAAC,QAAQ;EAACL,KAAK,EAAC;GAAkB,IAAE,E;;EAIvDA,KAAK,EAAC;AAAqB;;EACvBA,KAAK,EAAC;AAAW;;;;gCAI9CC,mBAAA,CAAgE;EAA7DO,IAAI,EAAC,OAAO;EAACN,KAAuC,EAAvC;IAAA;IAAA;EAAA;GAAwC,MAAI;kDAAI,UACrD;iCAAAD,mBAAA,CAAkE;EAA/DO,IAAI,EAAC,SAAS;EAACN,KAAuC,EAAvC;IAAA;IAAA;EAAA;GAAwC,MAAI;qBADzEO,UAAgE,E,aACrDC,WAAkE,C;kDAE9D,OACJ;;EAAGF,IAAI,EAAC,qBAAqB;EAACN,KAAoB,EAApB;IAAA;EAAA;;kDAAoC,OAEtE;iCAUCD,mBAAA,CAiCM;EAjCDD,KAAK,EAAC;AAAwC,I,aAC/CC,mBAAA,CA+BM;EA/BDD,KAAK,EAAC;AAAW,I,aAClBC,mBAAA,CAGM;EAHDD,KAAK,EAAC;AAAe,I,aACtBC,mBAAA,CAAoR;EAA5QI,IAAI,EAAC,QAAQ;EAACL,KAAK,EAAC,yBAAyB;EAAC,aAAW,EAAC,UAAU;EAAC,aAAW,EAAC,SAAS;EAAC,eAAa,EAAC,OAAO;EAAC,eAAa,EAAC;iBAASC,mBAAA,CAAiC;EAA3BD,KAAK,EAAC;AAAS,GAAC,MAAI,G,aAAOC,mBAAA,CAA8B;EAAxBD,KAAK,EAAC;AAAU,I,aAAQC,mBAAA,CAA8B;EAAxBD,KAAK,EAAC;AAAU,I,aAAQC,mBAAA,CAA8B;EAAxBD,KAAK,EAAC;AAAU,G,gBACnQC,mBAAA,CAA6F;EAA1FO,IAAI,EAAC;AAAQ,I,aAACP,mBAAA,CAAwE;EAAnEU,GAA+B,EAA/Bb,UAA+B;EAACI,KAAqB,EAArB;IAAA;EAAA,CAAqB;EAACF,KAAK,EAAC;qBAEtFC,mBAAA,CAwBM;EAxBDF,EAAE,EAAC,QAAQ;EAACC,KAAK,EAAC;iBACnBC,mBAAA,CAsBK;EAtBDD,KAAK,EAAC;AAAgB,I,aACtBC,mBAAA,CAA2C;EAAvCD,KAAK,EAAC;AAAE,I,aAACC,mBAAA,CAAwB;EAArBO,IAAI,EAAC;AAAQ,GAAC,KAAG,E,gBACjCP,mBAAA,CAAyD;EAArDD,KAAK,EAAC;AAAW,I,aAACC,mBAAA,CAA4B;EAAzBO,IAAI,EAAC;AAAW,GAAC,MAAI,E,gBAC3EP,mBAAA,CAA+D;EAA3DD,KAAK,EAAC;AAAW,I,aAACC,mBAAA,CAAkC;EAA/BO,IAAI,EAAC;AAAiB,GAAC,MAAI,E,gBACpDP,mBAAA,CAAsD;EAAlDD,KAAK,EAAC;AAAW,I,aAACC,mBAAA,CAAyB;EAAtBO,IAAI,EAAC;AAAQ,GAAC,MAAI,E,gBAC3CP,mBAAA,CAAoD;EAAhDD,KAAK,EAAC;AAAW,I,aAACC,mBAAA,CAAuB;EAApBO,IAAI,EAAC;AAAM,GAAC,MAAI,E,gBACzCP,mBAAA,CAAqD;EAAjDD,KAAK,EAAC;AAAW,I,aAACC,mBAAA,CAAwB;EAArBO,IAAI,EAAC;AAAO,GAAC,MAAI,E,gBAC1CP,mBAAA,CAAyD;EAArDD,KAAK,EAAC;AAAW,I,aAACC,mBAAA,CAA4B;EAAzBO,IAAI,EAAC;AAAW,GAAC,MAAI,E,gBAEjBI,mBAAA,mtBAWS,C,kBAIjBA,mBAAA,mBAAsB,C;;;;uBAzExCC,mBAAA,CA4Ee,iBA3ELZ,mBAAA,CAuCM,OAvCNa,UAuCM,GAtCFb,mBAAA,CAqCM,OArCNc,UAqCM,GApCFd,mBAAA,CAmCM,OAnCNe,UAmCM,GAlCFC,UAQM,EACNC,UAOM,EACNjB,mBAAA,CAgBM,OAhBNkB,UAgBM,GAfFlB,mBAAA,CAcM,OAdNmB,UAcM,GAXjBC,KAAA,CAAAC,OAAO,I,cAAjBT,mBAAA,CAGC,MAAAU,UAAA,EAAAC,WAAA,M,cACHC,YAAA,CAIYC,gBAAA;IAAAC,GAAA;EAAA;sBAJE,MACJ,C,aAAA1B,mBAAA,CAAkE,KAAlE2B,WAAkE,EAAAC,gBAAA,CAAZR,KAAA,CAAAS,KAAK,kB,aAE/D7B,mBAAA,CAAyE;MAAtEO,IAAI,EAAC,qBAAqB;MAAEuB,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,IAAA,IAAAD,QAAA,CAAAC,IAAA,IAAAF,IAAA,CAAI;MAAE/B,KAAoB,EAApB;QAAA;MAAA;OAAqB,MAAI,E;;iBASpEU,mBAAA,kBAAqB,EACrBwB,WAiCM,C"}]}