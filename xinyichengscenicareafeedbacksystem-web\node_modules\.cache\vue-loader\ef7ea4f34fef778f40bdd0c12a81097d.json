{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Board.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Board.vue", "mtime": 1748665016744}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCByZXF1ZXN0LCB7IGJhc2UgfSBmcm9tICIuLi8uLi8uLi91dGlscy9odHRwIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJCb2FyZCIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgICAgICBwYWdlOiB7CiAgICAgICAgY3VycmVudFBhZ2U6IDEsIC8vIOW9k+W<PERSON><PERSON>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"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Board.vue"], "names": [], "mappings": ";AA8EA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACH,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3D,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACH,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7D,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzD,CAAC;IACL,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjB,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElC,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEP,CAAC,EAAE,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;gBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YACtB,EAAE,CAAC,CAAC,CAAC,EAAE;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB;YACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC;IACN,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC;IACN,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;;YAE1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;gBAEP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;oBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC;oBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV;;gBAEA,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAElD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;;wBAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBACf,CAAC,CAAC;;oBAEN;oBACA,CAAC,CAAC,CAAC,EAAE;wBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBACf,CAAC,CAAC;wBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3B;gBACJ,CAAC,CAAC;YACN;QACJ,CAAC,CAAC;IACN,CAAC;;EAEH,CAAC;AACH,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/Board.vue", "sourceRoot": "", "sourcesContent": ["<template>\n   \n<table style=\"width:100%;line-height:32px; font-size:13px;border: 1px solid #C2D5E3; \"  v-for=\"item in bolist\" :key=\"item.id\">\n    <tr>\n        <td width=\"100\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\" valign=\"top\">\n            <div v-if=\"item.isniming=='否'\">\n                <img style=\"width: 80px;height: 80px;border-radius: 35px\" \n :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' + item.by1\" />\n            </div>\n            <div v-else>\n                <img style=\"width: 80px;height: 80px;border-radius: 35px\" \n src=\"http://localhost:8088/xinyichengscenicareafeedbacksystem/upload/anonymous.png\" />\n            </div>\n\n            <span class=\"text-red\" v-if=\"item.isniming=='否'\"> {{item.account}}</span>\n            <span class=\"text-red\" v-else>匿名用户</span>\n        </td>\n        <td valign=\"top\">\n            <table width=\"100%\">\n                <tr>\n                    <td style=\"    border-bottom: 1px dashed #e3e3e3;\">\n                        <b>反馈标题：{{item.title}}</b>\n                        <span v-if=\"item.catname\" style=\"margin-left: 10px;\">[分类：{{item.catname}}]</span>\n                        <span style=\"float: right\"><i class=\"glyphicon glyphicon-time text-cyan\"></i>   反馈时间：{{item.addtime}}</span>\n                    </td>\n                </tr>\n                <tr>\n                    <td>\n                        {{item.content}}\n\n                      \n                            <br>\n                            <b style=\"color: red\"   v-if=\"item.adminreply != null\">管理员回复：{{item.adminreply}}</b>\n                       \n\n                    </td>\n                </tr>\n\n            </table>\n\n        </td>\n    </tr>\n</table>\n\n    <table style=\"width: 100%;\">\n                <tr>\n                    <td>\n                        <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\"\n                            :page-size=\"page.pageSize\" background layout=\"total, prev, pager, next, jumper\"\n                            :total=\"page.totalCount\" style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n                    </td>\n                </tr>\n            </table>\n\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\n        <el-form-item label=\"反馈标题\" prop=\"title\">\n        <el-input v-model=\"formData.title\" placeholder=\"反馈标题\" style=\"width:50%;\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"意见类别\" prop=\"catid\">\n        <el-select v-model=\"formData.catid\" placeholder=\"请选择意见类别\" style=\"width:50%;\">\n            <el-option v-for=\"item in categoryList\" :key=\"item.catid\" :label=\"item.catname\" :value=\"item.catid\"></el-option>\n        </el-select>\n    </el-form-item>\n    <el-form-item label=\"反馈内容\" prop=\"content\">\n        <el-input type=\"textarea\" :rows=\"5\" v-model=\"formData.content\" placeholder=\"反馈内容\"\n        size=\"small\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"是否匿名\">\n        <el-switch v-model=\"formData.isniming\" active-text=\"是\" inactive-text=\"否\"></el-switch>\n    </el-form-item>\n    <el-form-item>\n        <el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\"    icon=\"el-icon-upload\" >发 表</el-button>\n    </el-form-item>\n    </el-form>\n\n\n</template>\n<script>\nimport request, { base } from \"../../../utils/http\";\nexport default {\n  name: \"Board\",\n  data() {\n    return {\n          page: {\n        currentPage: 1, // 当前页\n        pageSize: 10, // 每页显示条目个数\n        totalCount: 0, // 总条目数\n    },\n    bolist: \"\",\n    categoryList: [], // 意见类别列表\n    formData: {\n      isniming: false\n    },\n    addrules: {\n        title: [\n            { required: true, message: \"请输入留言标题\", trigger: \"blur\" },\n        ],\n        catid: [\n            { required: true, message: \"请选择意见类别\", trigger: \"change\" },\n        ],\n        content: [\n          { required: true, message: \"请输入留言内容\", trigger: \"blur\" },\n        ],\n    },\n    btnLoading: false,\n \n    };\n  },\n  created() {\n    \n    this.getDatas();\n    this.getCategoryList(); // 获取分类列表\n\n  },\n  methods: {  \n    \n    // 分页\n    handleCurrentChange(val) {\n        this.page.currentPage = val;\n        this.getDatas();\n    },\n\n    //获取列表数据\n    getDatas() {\n        let para = {\n            status: \"审核通过\"\n        };\n        this.listLoading = true;\n        let url = base + \"/board/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\n        request.post(url, para).then((res) => {\n            if (res.resdata.length > 0) {\n                this.isPage = true;\n            } else {\n                this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.bolist = res.resdata;\n            this.listLoading = false;\n        });\n    },\n\n    // 获取分类列表\n    getCategoryList() {\n        let para = {};\n        let url = base + \"/category/list?currentPage=1&pageSize=1000\";\n        request.post(url, para).then((res) => {\n            this.categoryList = res.resdata;\n        });\n    },\n\n    //提交留言\n    save() {\n        //如果没有ID就新增数据\n        this.$refs[\"formDataRef\"].validate((valid) => {\n\n            if (valid) {\n\n                var lname = sessionStorage.getItem(\"lname\"); //获取登录名\n                if (lname == null) {\n                    this.$message({\n                        message: \"请先登录\",\n                        type: \"error\",\n                        offset: 320,\n                    });\n                    return;\n                }\n\n                let url = base + \"/board/add\"; //提交地址\n                this.btnLoading = true; //打开按钮加载状态\n                this.formData.account = lname; //留言人\n                this.formData.status = \"待审核\";\n                request.post(url, this.formData).then((res) => { //提交数据\n\n                    if (res.code == 200) {\n\n                        this.formData={}; //清空表单\n                        this.btnLoading = false; //关闭按钮加载状态\n                        this.getDatas(); //刷新列表\n\n                        this.$message({\n                            message: \"恭喜您，留言成功，请等待管理员的回复！\",\n                            type: \"success\",\n                            offset: 320,\n                        });\n\n                    }\n                    else {\n                        this.$message({\n                            message: \"服务器错误\",\n                            type: \"error\",\n                            offset: 320,\n                        });\n                        this.btnLoading = false;\n                    }\n                });\n            }\n        });\n    },\n    \n  },\n};\n</script>\n\n<style>\n\n</style>\n\n\n\n"]}]}