package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/results")
public class ResultsController{
	
	@Resource
	private ResultsService resultsService;
	
	//问卷结果列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Results>> list(@RequestBody Results results, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = resultsService.getCount(results);
		//获取当前页记录
		List<Results> resultsList = resultsService.queryResultsList(results, page);
        
		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(resultsList, counts, page_count);
	}
        
	//添加问卷结果
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Results results, HttpServletRequest req) throws Exception {
		try {
			resultsService.insertResults(results); //添加
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除问卷结果
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			resultsService.deleteResults(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改问卷结果
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Results results, HttpServletRequest req) throws Exception {
		try {
			resultsService.updateResults(results); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回问卷结果详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Results results=resultsService.queryResultsById(id); //根据ID查询
			return Response.success(results);
			} catch (Exception e) {
			return Response.error();
		}

	}

	//获取问卷统计数据
	@ResponseBody
	@PostMapping(value = "/statistics")
	@CrossOrigin
	public Response getStatistics(HttpServletRequest req) throws Exception {
		try {
			List<Map<String, Object>> statistics = resultsService.getQuestionnaireStatistics();
			return Response.success(statistics);
		} catch (Exception e) {
			return Response.error();
		}
	}

	//检查用户是否已完成问卷
	@ResponseBody
	@PostMapping(value = "/checkCompleted")
	@CrossOrigin
	public Response checkCompleted(HttpServletRequest req) throws Exception {
		try {
			String account = req.getParameter("account");
			boolean completed = resultsService.checkUserCompleted(account);
			return Response.success(completed);
		} catch (Exception e) {
			return Response.error();
		}
	}

}

