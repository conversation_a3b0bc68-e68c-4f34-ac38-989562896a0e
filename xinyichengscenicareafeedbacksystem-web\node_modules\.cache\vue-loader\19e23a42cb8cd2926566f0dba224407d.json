{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Default.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Default.vue", "mtime": 1747227353972}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Default.vue"], "names": [], "mappings": ";AAmGA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC;IACL,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC;IACN,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAET,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC;IACN,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC;IACN,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC;IACN,CAAC;IACD,CAAC;AACL,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/Default.vue", "sourceRoot": "", "sourcesContent": ["\r\n<template>\r\n    <!--幻灯片大图开始-->\r\n    <div id=\"banner_main\">\r\n        <div id=\"banner\" class=\"banner\" style=\"height: 400px;\">\r\n            <div class=\"swiper-container swiper-container1\">\r\n                <div class=\"swiper-wrapper\">\r\n                    <div class=\"swiper-slide\">\r\n                        <img style=\"width: 100%; height: 405px\" :src=\"banner1\" />\r\n                    </div>\r\n                    <div class=\"swiper-slide\">\r\n                        <img style=\"width: 100%; height: 405px\" :src=\"banner2\" />\r\n                    </div>\r\n   <div class=\"swiper-slide\">\r\n                        <img style=\"width: 100%; height: 405px\" :src=\"banner3\" />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <!--幻灯片大图结束-->\r\n    <div class=\"index\">\r\n        <div class=\"container\">\r\n    <div class=\"row\">\r\n        <div class=\"col-xs-12 col-sm-12 col-md-12 about_us\">\r\n            <div class=\"col-xs-12 col-sm-6 col-md-6 \" data-move-y=\"-40px\">\r\n                <h3 class=\"about_h3\">关于我们</h3>\r\n               <div  v-for=\"(item,index) in list1\" :key=\"index\" >\r\n                <div class=\"about_content\">{{item.description .length > 270 ? item.description .substring(0,270):item.description}}</div>\r\n                <a :href=\"'scenicareaView?id='+item.aid\" class=\"about_btn\">了解更多</a>\r\n            </div>\r\n            </div>\r\n            <div class=\"col-xs-12 col-sm-6 col-md-6 js_about_right\" data-move-y=\"40px\">\r\n                <img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' +list1[0].picurl\" style=\"width: 555px;height: 277px;\">\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>   \r\n<div class=\"tgt-section tgt-team-section pb-5\">\r\n    <div class=\"container\">\r\n        <div class=\"row justify-content-center\">\r\n            <div class=\"col-lg-6 col-lg-offset-3\">\r\n                <div class=\"tgt-section-title\">\r\n                    <h2>新闻资讯 </h2>                    \r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"row\">\r\n\r\n            <div class=\"col-lg-4\"  v-for=\"(item,index) in list2\" :key=\"index\" >\r\n                <div class=\"tgt-team-box\">\r\n                    <div class=\"tgt-team-box-img\">\r\n                        <a :href=\"'newsView?id='+item.id\"><img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' +item.nimage\" class=\"img-fluid\" style=\"width: 300px;height: 200px;\" ></a>\r\n                    </div>\r\n                    <div class=\"tgt-team-box-content\">\r\n                        <h5 class=\"mb5\">{{item.title}}</h5>\r\n                        <p>{{item.content .length > 10 ? item.content .substring(0,10):item.content}}</p>\r\n                    </div>\r\n                </div>\r\n            </div>     \r\n\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n\r\n<div class=\"tgt-section\">\r\n    <div class=\"container\">\r\n        <div class=\"row justify-content-center\">\r\n            <div class=\"col-lg-6 col-lg-offset-3\">\r\n                <div class=\"tgt-section-title\">\r\n                    <h2>意见反馈</h2>                    \r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"row\">\r\n\r\n            <div class=\"col-md-6\"  v-for=\"(item,index) in list3\" :key=\"index\" >\r\n                <div class=\"news-item\">\r\n                    <h4><a :href=\"'board?id='+item.id\">{{item.title}}</a></h4>\r\n                    <p class=\"news-meta\">\r\n                        <span class=\"date\">{{item.addtime}}</span>\r\n                    </p>\r\n                    <p class=\"news-excerpt\">\r\n                        {{item.content .length > 70 ? item.content .substring(0,70):item.content}}\r\n                        <a :href=\"'board?id='+item.id\" class=\"read-more\">阅读全文</a>\r\n                    </p>\r\n                    \r\n                </div>\r\n            </div>        \r\n\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n\r\n    </div>\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport Swiper from \"swiper\";\r\nimport \"swiper/dist/css/swiper.min.css\";\r\nimport \"swiper/dist/js/swiper.min\";\r\n\r\nexport default {\r\n    name: \"Default\",\r\n    data() {\r\n        return {\r\n            banner1: require(\"@/assets/images/banner1.jpg\"),\r\n            banner2: require(\"@/assets/images/banner2.jpg\"),\r\n            banner3: require(\"@/assets/images/banner3.jpg\"),\r\n                list1:\"\",\r\n    list2:\"\",\r\n    list3:\"\",\r\n        };\r\n    },\r\n    mounted() {\r\n        new Swiper(\".swiper-container\", {\r\n            slidesPerView: 1,\r\n            spaceBetween: 0,\r\n            loop: true,\r\n            autoplay: 3000,\r\n        });\r\n    },\r\n    created() {\r\n            this.getlist1();\r\n    this.getlist2();\r\n    this.getlist3();\r\n    },\r\n    methods: {\r\n        \r\n    // 获取景区\r\n    getlist1() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/scenicarea/list?currentPage=1&pageSize=1\";\r\n        request.post(url, para).then((res) => {         \r\n            this.list1 = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n    // 获取新闻资讯\r\n    getlist2() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/news/list?currentPage=1&pageSize=6\";\r\n        request.post(url, para).then((res) => {         \r\n            this.list2 = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n    // 获取意见反馈\r\n    getlist3() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/board/list?currentPage=1&pageSize=6\";\r\n        request.post(url, para).then((res) => {         \r\n            this.list3 = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n    },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n\r\n\r\n"]}]}