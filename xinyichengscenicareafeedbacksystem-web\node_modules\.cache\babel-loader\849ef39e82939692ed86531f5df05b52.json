{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\board\\BoardManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\board\\BoardManage.vue", "mtime": 1748665016815}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCByZXF1ZXN0LCB7IGJhc2UgfSBmcm9tICIuLi8uLi8uLi8uLi91dGlscy9odHRwIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdib2FyZCcsCiAgY29tcG9uZW50czoge30sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGZpbHRlcnM6IHsKICAgICAgICAvL+WIl+ihqOafpeivouWPguaVsAogICAgICAgIGFjY291bnQ6ICcnLAogICAgICAgIHRpdGxlOiAnJywKICAgICAgICBjYXRpZDogJycKICAgICAgfSwKICAgICAgcGFnZTogewogICAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICAgIC8vIOW9k+WJ<PERSON>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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "filters", "account", "title", "catid", "page", "currentPage", "pageSize", "totalCount", "isClear", "usersList", "categoryList", "listLoading", "btnLoading", "datalist", "created", "getDatas", "getusersList", "getcategoryList", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "id", "post", "res", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "para", "resdata", "length", "isPage", "count", "query", "handleShow", "$router", "push", "path", "handleEdit"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\board\\BoardManage.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-title-box\">\r\n    <ol class=\"breadcrumb float-right\">\r\n      <li class=\"breadcrumb-item\"><a href=\"javascript:void(0);\" id=\"title1\">意见反馈管理</a></li>\r\n      <li class=\"breadcrumb-item active\" id=\"title2\">{{ this.$route.meta.title }}</li>\r\n    </ol>\r\n    <h4 class=\"page-title\" id=\"title3\">{{ this.$route.meta.title }}</h4>\r\n  </div>\r\n\r\n\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n      <el-col  :span=\"24\"  style=\"padding-bottom: 0px; margin-left: 10px\">\r\n<el-form :inline=\"true\" :model=\"filters\" >\r\n<el-form-item label=\"用户\" prop=\"account\">\r\n<el-select v-model=\"filters.account\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option label=\"全部\" value=\"\"></el-option>\r\n<el-option v-for=\"item in usersList\" :key=\"item.account\" :label=\"item.uname\" :value=\"item.account\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-input v-model=\"filters.title\" placeholder=\"反馈标题\"  size=\"small\"></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"意见类别\" prop=\"catid\">\r\n<el-select v-model=\"filters.catid\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option label=\"全部\" value=\"\"></el-option>\r\n<el-option v-for=\"item in categoryList\" :key=\"item.catid\" :label=\"item.catname\" :value=\"item.catid\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\r\n</el-form-item>\r\n </el-form>\r\n</el-col>\r\n\r\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\r\n<el-table-column prop=\"uname\" label=\"用户\"  align=\"center\"></el-table-column>\r\n<el-table-column prop=\"title\" label=\"反馈标题\"  align=\"center\"></el-table-column>\r\n<el-table-column prop=\"catname\" label=\"意见类别\"  align=\"center\"></el-table-column>\r\n<el-table-column prop=\"content\" label=\"反馈内容\"  align=\"center\">\r\n<template #default=\"scope\">\r\n<span v-if=\"scope.row.content != null\">{{scope.row.content.substring(0,20)}}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"isniming\" label=\"是否匿名\"  align=\"center\"></el-table-column>\r\n<el-table-column prop=\"addtime\" label=\"反馈时间\"  align=\"center\"></el-table-column>\r\n<el-table-column prop=\"by2\" label=\"联系方式\"  align=\"center\"></el-table-column>\r\n<el-table-column prop=\"status\" label=\"审核状态\"  align=\"center\"></el-table-column>\r\n<el-table-column prop=\"adminreply\" label=\"管理员回复\"  align=\"center\"></el-table-column>\r\n<el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\r\n<template #default=\"scope\">\r\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\r\n<el-button type=\"success\" size=\"mini\" @click=\"handleEdit(scope.$index, scope.row)\" icon=\"el-icon-edit\" style=\" padding: 3px 6px 3px 6px;\" v-show=\"scope.row.adminreply == null\">回复</el-button>\r\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" \r\n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\" \r\n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n\r\n\r\n    </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\r\nexport default {\r\n  name: 'board',\r\n  components: {\r\n    \r\n  },  \r\n    data() {\r\n      return {\r\n               filters: {\r\n          //列表查询参数\r\n          account: '',\r\n          title: '',\r\n          catid: '',\r\n        },\r\n\r\n        page: {\r\n          currentPage: 1, // 当前页\r\n          pageSize: 10, // 每页显示条目个数\r\n          totalCount: 0, // 总条目数\r\n        },\r\n        isClear: false,      \r\n        usersList: [], //用户\r\ncategoryList: [], //意见类别\r\n\r\n        listLoading: false, //列表加载状态\r\n        btnLoading: false, //保存按钮加载状态\r\n        datalist: [], //表格数据  \r\n    \r\n      };\r\n    },\r\n    created() {\r\n      this.getDatas();\r\n      this.getusersList();\r\n      this.getcategoryList();\r\n    },\r\n\r\n \r\n    methods: {    \r\n\r\n              \r\n       // 删除意见反馈\r\n        handleDelete(index, row) {\r\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\r\n            confirmButtonText: \"确定\",\r\n            cancelButtonText: \"取消\",\r\n            type: \"warning\",\r\n          })\r\n            .then(() => {\r\n              this.listLoading = true;\r\n              let url = base + \"/board/del?id=\" + row.id;\r\n              request.post(url).then((res) => {\r\n                this.listLoading = false;\r\n             \r\n                this.$message({\r\n                  message: \"删除成功\",\r\n                  type: \"success\",\r\n                  offset: 320,\r\n                });\r\n                this.getDatas();\r\n              });\r\n            })\r\n            .catch(() => { });\r\n        },\r\n                \r\n        // 分页\r\n        handleCurrentChange(val) {\r\n          this.page.currentPage = val;\r\n          this.getDatas();\r\n        },     \r\n     \r\n        //获取列表数据\r\n        getDatas() {      \r\n          let para = {\r\n               account:this.filters.account,\r\n   title:this.filters.title,\r\n   catid:this.filters.catid,\r\n\r\n          };\r\n          this.listLoading = true;\r\n          let url = base + \"/board/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;        \r\n          request.post(url, para).then((res) => {   \r\n            if (res.resdata.length > 0) {\r\n              this.isPage = true;\r\n            } else {\r\n              this.isPage = false;\r\n            }\r\n            this.page.totalCount = res.count;\r\n            this.datalist = res.resdata;\r\n            this.listLoading = false;\r\n          });\r\n        },    \r\n                 //查询\r\n        query() {\r\n          this.getDatas();\r\n        },  \r\n            \r\n    getusersList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/users/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.usersList = res.resdata;\r\n      });\r\n    },\r\n    \r\n    getcategoryList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/category/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.categoryList = res.resdata;\r\n      });\r\n    },\r\n   \r\n        // 查看\r\n        handleShow(index, row) {\r\n          this.$router.push({\r\n            path: \"/BoardDetail\",\r\n             query: {\r\n                id: row.id,\r\n              },\r\n          });\r\n        },\r\n    \r\n        // 编辑\r\n        handleEdit(index, row) {\r\n          this.$router.push({\r\n            path: \"/BoardEdit\",\r\n             query: {\r\n                id: row.id,\r\n              },\r\n          });\r\n        },\r\n      },\r\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";AAkEA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACEC,OAAO,EAAE;QACd;QACAC,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACT,CAAC;MAEDC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;;MACDC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,EAAE;MAAE;MACvBC,YAAY,EAAE,EAAE;MAAE;;MAEVC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE,CAAE;IAEhB,CAAC;EACH,CAAC;;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,YAAY,CAAC,CAAC;IACnB,IAAI,CAACC,eAAe,CAAC,CAAC;EACxB,CAAC;EAGDC,OAAO,EAAE;IAGN;IACCC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAACf,WAAU,GAAI,IAAI;QACvB,IAAIgB,GAAE,GAAI/B,IAAG,GAAI,gBAAe,GAAIyB,GAAG,CAACO,EAAE;QAC1CjC,OAAO,CAACkC,IAAI,CAACF,GAAG,CAAC,CAACD,IAAI,CAAEI,GAAG,IAAK;UAC9B,IAAI,CAACnB,WAAU,GAAI,KAAK;UAExB,IAAI,CAACoB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfP,IAAI,EAAE,SAAS;YACfQ,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAAClB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAmB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAAChC,IAAI,CAACC,WAAU,GAAI+B,GAAG;MAC3B,IAAI,CAACrB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACT,IAAIsB,IAAG,GAAI;QACNpC,OAAO,EAAC,IAAI,CAACD,OAAO,CAACC,OAAO;QACxCC,KAAK,EAAC,IAAI,CAACF,OAAO,CAACE,KAAK;QACxBC,KAAK,EAAC,IAAI,CAACH,OAAO,CAACG;MAEZ,CAAC;MACD,IAAI,CAACQ,WAAU,GAAI,IAAI;MACvB,IAAIgB,GAAE,GAAI/B,IAAG,GAAI,0BAAyB,GAAI,IAAI,CAACQ,IAAI,CAACC,WAAW,GAAE,YAAW,GAAI,IAAI,CAACD,IAAI,CAACE,QAAQ;MACtGX,OAAO,CAACkC,IAAI,CAACF,GAAG,EAAEU,IAAI,CAAC,CAACX,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACQ,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAACpC,IAAI,CAACG,UAAS,GAAIuB,GAAG,CAACW,KAAK;QAChC,IAAI,CAAC5B,QAAO,GAAIiB,GAAG,CAACQ,OAAO;QAC3B,IAAI,CAAC3B,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IACQ;IACT+B,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC3B,QAAQ,CAAC,CAAC;IACjB,CAAC;IAELC,YAAYA,CAAA,EAAG;MACb,IAAIqB,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAAC1B,WAAU,GAAI,IAAI;MACvB,IAAIgB,GAAE,GAAI/B,IAAG,GAAI,yCAAyC;MAC1DD,OAAO,CAACkC,IAAI,CAACF,GAAG,EAAEU,IAAI,CAAC,CAACX,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAI,CAACrB,SAAQ,GAAIqB,GAAG,CAACQ,OAAO;MAC9B,CAAC,CAAC;IACJ,CAAC;IAEDrB,eAAeA,CAAA,EAAG;MAChB,IAAIoB,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAAC1B,WAAU,GAAI,IAAI;MACvB,IAAIgB,GAAE,GAAI/B,IAAG,GAAI,4CAA4C;MAC7DD,OAAO,CAACkC,IAAI,CAACF,GAAG,EAAEU,IAAI,CAAC,CAACX,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAI,CAACpB,YAAW,GAAIoB,GAAG,CAACQ,OAAO;MACjC,CAAC,CAAC;IACJ,CAAC;IAEG;IACAK,UAAUA,CAACvB,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACuB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,cAAc;QACnBJ,KAAK,EAAE;UACJd,EAAE,EAAEP,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAmB,UAAUA,CAAC3B,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACuB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,YAAY;QACjBJ,KAAK,EAAE;UACJd,EAAE,EAAEP,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ;EACF;AACN"}]}