{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\ticketpurchases\\TicketpurchasesEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\ticketpurchases\\TicketpurchasesEdit.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "id", "isClear", "uploadVisible", "btnLoading", "formData", "created", "$route", "query", "getDatas", "getusersList", "getticketInfoList", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "account", "uname", "ticketid", "tname", "save", "$refs", "validate", "valid", "code", "$message", "message", "type", "offset", "$router", "push", "path", "msg", "goBack", "usersList", "ticketInfoList"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\ticketpurchases\\TicketpurchasesEdit.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-title-box\">\r\n    <ol class=\"breadcrumb float-right\">\r\n      <li class=\"breadcrumb-item\"><a href=\"javascript:void(0);\" id=\"title1\">购票信息管理</a></li>\r\n      <li class=\"breadcrumb-item active\" id=\"title2\">{{ this.$route.meta.title }}</li>\r\n    </ol>\r\n    <h4 class=\"page-title\" id=\"title3\">{{ this.$route.meta.title }}</h4>\r\n  </div>\r\n\r\n\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"用户\" prop=\"account\">\r\n<el-select v-model=\"formData.account\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option v-for=\"item in usersList\" :key=\"item.account\" :label=\"item.uname\" :value=\"item.account\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"票种名称\" prop=\"tickid\">\r\n<el-select v-model=\"formData.tickid\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option v-for=\"item in ticketInfoList\" :key=\"item.ticketid\" :label=\"item.tname\" :value=\"item.ticketid\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"单价\" prop=\"price\">\r\n<el-input v-model=\"formData.price\" placeholder=\"单价\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"购票数量\" prop=\"pnum\">\r\n<el-input v-model=\"formData.pnum\" placeholder=\"购票数量\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"总金额\" prop=\"totalprice\">\r\n<el-input v-model=\"formData.totalprice\" placeholder=\"总金额\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"备注\" prop=\"remark\">\r\n<el-input type=\"textarea\" :rows=\"5\" v-model=\"formData.remark\" placeholder=\"备注\"  size=\"small\"></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n\r\n    </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'TicketpurchasesEdit',\n  components: {\n    \n  },  \n    data() {\n      return {   \n        id: '',\n        isClear: false,\n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        \n      };\n    },\n    created() {\r\n    this.id = this.$route.query.id;\r\n      this.getDatas();\r\n      this.getusersList();\r\n      this.getticketInfoList();\r\n    },\r\n\r\n \n    methods: {    \n\n//获取列表数据\n        getDatas() {\n          let para = {\n          };\n          this.listLoading = true;\n          let url = base + \"/ticketpurchases/get?id=\" + this.id;\n          request.post(url, para).then((res) => {\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\n            this.listLoading = false;\n            \n                    this.account = this.formData.account;\r\n        this.formData.account = this.formData.uname;\r\n        this.ticketid = this.formData.ticketid;\r\n        this.formData.ticketid = this.formData.tname;\r\n\n          });\n        },\n    \n        // 添加\n        save() {\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n            if (valid) {\n              let url = base + \"/ticketpurchases/update\";\n              this.btnLoading = true;\n                        this.formData.account = this.formData.account==this.formData.uname?this.account:this.formData.account;\r\n          this.formData.ticketid = this.formData.ticketid==this.formData.tname?this.ticketid:this.formData.ticketid;\r\n\n              request.post(url, this.formData).then((res) => { //发送请求         \n                if (res.code == 200) {\n                  this.$message({\n                    message: \"操作成功\",\n                    type: \"success\",\n                    offset: 320,\n                  });\n                  this.$router.push({\n                    path: \"/TicketpurchasesManage\",\n                  });\n                } else {\n                  this.$message({\n                    message:res.msg,\n                    type: \"error\",\n                    offset: 320,\n                  });\n                }\n                this.btnLoading = false;\n              });\n            }\n    \n          });\n        },\n        \n       // 返回\n        goBack() {\n          this.$router.push({\n            path: \"/TicketpurchasesManage\",\n          });\n        },       \n              \n            \r\n    getusersList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/users/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.usersList = res.resdata;\r\n      });\r\n    },\r\n    \r\n    getticketInfoList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/ticketInfo/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.ticketInfoList = res.resdata;\r\n      });\r\n    },\r\n  \n           \n           \n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";AA+CA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AAEtD,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC,CAAE;IAEhB,CAAC;EACH,CAAC;;EACDC,OAAOA,CAAA,EAAG;IACV,IAAI,CAACL,EAAC,GAAI,IAAI,CAACM,MAAM,CAACC,KAAK,CAACP,EAAE;IAC5B,IAAI,CAACQ,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,YAAY,CAAC,CAAC;IACnB,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B,CAAC;EAGDC,OAAO,EAAE;IAEb;IACQH,QAAQA,CAAA,EAAG;MACT,IAAII,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIlB,IAAG,GAAI,0BAAyB,GAAI,IAAI,CAACI,EAAE;MACrDL,OAAO,CAACoB,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACb,QAAO,GAAIc,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;QAEhB,IAAI,CAACS,OAAM,GAAI,IAAI,CAAClB,QAAQ,CAACkB,OAAO;QAChD,IAAI,CAAClB,QAAQ,CAACkB,OAAM,GAAI,IAAI,CAAClB,QAAQ,CAACmB,KAAK;QAC3C,IAAI,CAACC,QAAO,GAAI,IAAI,CAACpB,QAAQ,CAACoB,QAAQ;QACtC,IAAI,CAACpB,QAAQ,CAACoB,QAAO,GAAI,IAAI,CAACpB,QAAQ,CAACqB,KAAK;MAE1C,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,IAAIA,CAAA,EAAG;MACL,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAAE;QAC9C,IAAIA,KAAK,EAAE;UACT,IAAIf,GAAE,GAAIlB,IAAG,GAAI,yBAAyB;UAC1C,IAAI,CAACO,UAAS,GAAI,IAAI;UACZ,IAAI,CAACC,QAAQ,CAACkB,OAAM,GAAI,IAAI,CAAClB,QAAQ,CAACkB,OAAO,IAAE,IAAI,CAAClB,QAAQ,CAACmB,KAAK,GAAC,IAAI,CAACD,OAAO,GAAC,IAAI,CAAClB,QAAQ,CAACkB,OAAO;UACnH,IAAI,CAAClB,QAAQ,CAACoB,QAAO,GAAI,IAAI,CAACpB,QAAQ,CAACoB,QAAQ,IAAE,IAAI,CAACpB,QAAQ,CAACqB,KAAK,GAAC,IAAI,CAACD,QAAQ,GAAC,IAAI,CAACpB,QAAQ,CAACoB,QAAQ;UAErG7B,OAAO,CAACoB,IAAI,CAACD,GAAG,EAAE,IAAI,CAACV,QAAQ,CAAC,CAACY,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACa,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZC,OAAO,EAAE,MAAM;gBACfC,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;gBAChBC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACN,QAAQ,CAAC;gBACZC,OAAO,EAACf,GAAG,CAACqB,GAAG;gBACfL,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAAC/B,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MAEF,CAAC,CAAC;IACJ,CAAC;IAEF;IACCoC,MAAMA,CAAA,EAAG;MACP,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAGL5B,YAAYA,CAAA,EAAG;MACb,IAAIG,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIlB,IAAG,GAAI,yCAAyC;MAC1DD,OAAO,CAACoB,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACuB,SAAQ,GAAIvB,GAAG,CAACI,OAAO;MAC9B,CAAC,CAAC;IACJ,CAAC;IAEDX,iBAAiBA,CAAA,EAAG;MAClB,IAAIE,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIlB,IAAG,GAAI,8CAA8C;MAC/DD,OAAO,CAACoB,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACwB,cAAa,GAAIxB,GAAG,CAACI,OAAO;MACnC,CAAC,CAAC;IACJ;EAIE;AACN"}]}