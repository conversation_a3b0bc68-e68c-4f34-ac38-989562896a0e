{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\LeftMenu.vue?vue&type=template&id=edc10994&scoped=true", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\LeftMenu.vue", "mtime": 1748665098240}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "id", "style", "_createElementVNode", "_hoisted_8", "_hoisted_9", "_hoisted_11", "_hoisted_15", "_hoisted_16", "_hoisted_18", "_hoisted_22", "_hoisted_23", "_hoisted_25", "_hoisted_28", "_hoisted_29", "_hoisted_31", "_hoisted_35", "_hoisted_36", "_hoisted_38", "_hoisted_42", "_hoisted_43", "_hoisted_45", "_hoisted_48", "_hoisted_49", "_hoisted_51", "_hoisted_55", "_hoisted_56", "_hoisted_58", "_hoisted_62", "_hoisted_63", "_hoisted_65", "_hoisted_68", "_hoisted_69", "_hoisted_71", "_hoisted_74", "_hoisted_75", "_hoisted_77", "_hoisted_80", "_hoisted_81", "_hoisted_83", "_hoisted_88", "_hoisted_89", "_hoisted_91", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_createCommentVNode", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_createVNode", "_component_router_link", "to", "_hoisted_6", "_hoisted_7", "_normalizeClass", "active", "$data", "activeMenu", "href", "onClick", "_cache", "$event", "$options", "toggleMenu", "in", "_hoisted_94"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\LeftMenu.vue"], "sourcesContent": ["<template>\r\n    <div class=\"left-side-menu\">\r\n\r\n        <div class=\"slimscroll-menu\">\r\n\r\n            <!--- Sidemenu -->\r\n            <div id=\"sidebar-menu\" style=\"max-height: calc(100vh - 70px); overflow-y: auto;\">\r\n\r\n                <ul class=\"metismenu\" id=\"side-menu\">\r\n\r\n                    <li class=\"menu-title\">功能菜单</li>\r\n\r\n                    <li>\r\n                      <router-link to=\"/main\"><i class=\"mdi mdi-view-dashboard\"></i><span>首页</span></router-link>                       \r\n                    </li>\r\n\r\n                    <li :class=\"{ active: activeMenu === 'nav9' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav9')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">管理员管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav9' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/adminAdd\">添加管理员</router-link></li>\r\n  <li> <router-link to=\"/adminManage\">管理管理员</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n                    <li :class=\"{ active: activeMenu === 'nav5' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav5')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">用户管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav5' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/usersAdd\">添加用户</router-link></li>\r\n  <li> <router-link to=\"/usersManage\">管理用户</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n                    <li :class=\"{ active: activeMenu === 'nav7' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav7')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">景区管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav7' }\" aria-expanded=\"false\">\r\n                             <!-- <li> <router-link to=\"/scenicareaAdd\">添加景区</router-link></li> -->\r\n  <li> <router-link to=\"/scenicareaManage\">管理景区</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n                    <li :class=\"{ active: activeMenu === 'nav10' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav10')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">新闻资讯管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav10' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/newsAdd\">添加新闻资讯</router-link></li>\r\n  <li> <router-link to=\"/newsManage\">管理新闻资讯</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n                    <li :class=\"{ active: activeMenu === 'nav11' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav11')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">意见类别管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav11' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/categoryAdd\">添加意见类别</router-link></li>\r\n  <li> <router-link to=\"/categoryManage\">管理意见类别</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                    <li :class=\"{ active: activeMenu === 'nav6' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav6')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">意见反馈管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav6' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/boardManage\">管理意见反馈</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n     \r\n      <li :class=\"{ active: activeMenu === 'nav8' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav8')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">调查问卷管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav8' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/questionsAdd\">添加调查问卷</router-link></li>\r\n  <li> <router-link to=\"/questionsManage\">管理调查问卷</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n\r\n\r\n                        <li :class=\"{ active: activeMenu === 'nav1' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav1')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">票务信息管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav1' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/ticketInfoAdd\">添加票务信息</router-link></li>\r\n  <li> <router-link to=\"/ticketInfoManage\">管理票务信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n      <li :class=\"{ active: activeMenu === 'nav2' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav2')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">购票信息管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav2' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/ticketpurchasesManage\">管理购票信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                    <li :class=\"{ active: activeMenu === 'nav12' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav12')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">问卷结果管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav12' }\" aria-expanded=\"false\">\r\n                             <!-- <li> <router-link to=\"/resultsAdd\">添加问卷结果</router-link></li> -->\r\n  <li> <router-link to=\"/resultsManage\">管理问卷结果</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n      <li :class=\"{ active: activeMenu === 'nav4' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav4')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">互动交流管理</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav4' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/postsManage\">管理互动交流</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n     \r\n      \r\n     \r\n      \r\n     \r\n     \r\n      <li :class=\"{ active: activeMenu === 'nav25' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav25')\" aria-expanded=\"true\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span class=\"btitle\">报表统计</span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav25' }\" aria-expanded=\"false\">\r\n                             <li> <router-link to=\"/total1\">意见类别统计</router-link></li>\r\n  <li> <router-link to=\"/total2\">用户统计</router-link></li>\r\n  <li> <router-link to=\"/total3\">票务统计</router-link></li>\r\n  <!-- <li> <router-link to=\"/total4\">问卷统计</router-link></li> -->\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n                    <li :class=\"{ active: activeMenu === 'nav22' }\">\r\n                        <a href=\"javascript: void(0);\" @click=\"toggleMenu('nav22')\">\r\n                            <i class=\"mdi mdi-chart-donut-variant\"></i><span> 系统管理 </span> <span class=\"menu-arrow\"></span>\r\n                        </a>\r\n                        <ul class=\"nav-second-level collapse\" :class=\"{ in: activeMenu === 'nav22' }\" aria-expanded=\"false\">\r\n                          <li><router-link to=\"password\">修改密码</router-link></li>\r\n                        </ul>\r\n                    </li>\r\n\r\n                \r\n\r\n                </ul>\r\n\r\n            </div>\r\n            <!-- Sidebar -->\r\n\r\n            <div class=\"clearfix\"></div>\r\n\r\n        </div>\r\n        <!-- Sidebar -left -->\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n  },\r\n  methods: {\r\n    toggleMenu(menu) {\r\n      this.activeMenu = this.activeMenu === menu ? null : menu;\r\n    },\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem(\"userLname\");\r\n          sessionStorage.removeItem(\"role\");\r\n          _this.$router.push(\"/login\");\r\n        })\r\n        .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.example-showcase .el-dropdown-link {\r\n  cursor: pointer;\r\n  color:green;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.pcoded-submenu a{\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n/*加点击效果*/\r\n.pcoded-submenu a:hover{\r\n  color: #fff;\r\n  color: #ff6600;\r\n}\r\n</style>\r\n\r\n\r\n"], "mappings": ";;;EACSA,KAAK,EAAC;AAAgB;;EAElBA,KAAK,EAAC;AAAiB;;EAGnBC,EAAE,EAAC,cAAc;EAACC,KAAyD,EAAzD;IAAA;IAAA;EAAA;;;EAEfF,KAAK,EAAC,WAAW;EAACC,EAAE,EAAC;;gEAErBE,mBAAA,CAAgC;EAA5BH,KAAK,EAAC;AAAY,GAAC,MAAI;gEAGDG,mBAAA,CAAsC;EAAnCH,KAAK,EAAC;AAAwB;gEAAKG,mBAAA,CAAe,cAAT,IAAE;gEAKhEA,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;gEAAKG,mBAAA,CAAiC;EAA3BH,KAAK,EAAC;AAAQ,GAAC,OAAK;;iEAAQG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAArGI,UAA2C,EAAAC,UAAiC,E,aAACC,WAAgC,C;kDAG3E,OAAK;kDAC7B,OAAK;iEAMfH,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;iEAAKG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAQ,GAAC,MAAI;;iEAAQG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAApGO,WAA2C,EAAAC,WAAgC,E,aAACC,WAAgC,C;kDAG1E,MAAI;kDAC5B,MAAI;iEAMdN,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;iEAAKG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAQ,GAAC,MAAI;;iEAAQG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAApGU,WAA2C,EAAAC,WAAgC,E,aAACC,WAAgC,C;kDAI7F,MAAI;iEAMnBT,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;iEAAKG,mBAAA,CAAkC;EAA5BH,KAAK,EAAC;AAAQ,GAAC,QAAM;;iEAAQG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAAtGa,WAA2C,EAAAC,WAAkC,E,aAACC,WAAgC,C;kDAG7E,QAAM;kDAC9B,QAAM;iEAMfZ,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;iEAAKG,mBAAA,CAAkC;EAA5BH,KAAK,EAAC;AAAQ,GAAC,QAAM;;iEAAQG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAAtGgB,WAA2C,EAAAC,WAAkC,E,aAACC,WAAgC,C;kDAGzE,QAAM;kDAC9B,QAAM;iEAOnBf,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;iEAAKG,mBAAA,CAAkC;EAA5BH,KAAK,EAAC;AAAQ,GAAC,QAAM;;iEAAQG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAAtGmB,WAA2C,EAAAC,WAAkC,E,aAACC,WAAgC,C;kDAGzE,QAAM;iEAO3ClB,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;iEAAKG,mBAAA,CAAkC;EAA5BH,KAAK,EAAC;AAAQ,GAAC,QAAM;;iEAAQG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAAtGsB,WAA2C,EAAAC,WAAkC,E,aAACC,WAAgC,C;kDAGxE,QAAM;kDAC9B,QAAM;iEAUpBrB,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;iEAAKG,mBAAA,CAAkC;EAA5BH,KAAK,EAAC;AAAQ,GAAC,QAAM;;iEAAQG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAAtGyB,WAA2C,EAAAC,WAAkC,E,aAACC,WAAgC,C;kDAGvE,QAAM;kDAC9B,QAAM;iEAMrBxB,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;iEAAKG,mBAAA,CAAkC;EAA5BH,KAAK,EAAC;AAAQ,GAAC,QAAM;;iEAAQG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAAtG4B,WAA2C,EAAAC,WAAkC,E,aAACC,WAAgC,C;kDAG/D,QAAM;iEAOrD3B,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;iEAAKG,mBAAA,CAAkC;EAA5BH,KAAK,EAAC;AAAQ,GAAC,QAAM;;iEAAQG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAAtG+B,WAA2C,EAAAC,WAAkC,E,aAACC,WAAgC,C;kDAIlG,QAAM;iEAOlB9B,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;iEAAKG,mBAAA,CAAkC;EAA5BH,KAAK,EAAC;AAAQ,GAAC,QAAM;;iEAAQG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAAtGkC,WAA2C,EAAAC,WAAkC,E,aAACC,WAAgC,C;kDAGzE,QAAM;iEAY3CjC,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;iEAAKG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAQ,GAAC,MAAI;;iEAAQG,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAApGqC,WAA2C,EAAAC,WAAgC,E,aAACC,WAAgC,C;kDAG5E,QAAM;kDACjC,MAAI;kDACJ,MAAI;iEASTpC,mBAAA,CAA2C;EAAxCH,KAAK,EAAC;AAA6B;iEAAKG,mBAAA,CAAmB,cAAb,QAAM;;iEAAQA,mBAAA,CAAgC;EAA1BH,KAAK,EAAC;AAAY;qBAAvFwC,WAA2C,EAAAC,WAAmB,E,aAACC,WAAgC,C;kDAGlE,MAAI;iEAWjDvC,mBAAA,CAA4B;EAAvBH,KAAK,EAAC;AAAU;;;;uBAtK7B2C,mBAAA,CA2KM,OA3KNC,UA2KM,GAzKFzC,mBAAA,CAsKM,OAtKN0C,UAsKM,GApKFC,mBAAA,eAAkB,EAClB3C,mBAAA,CA8JM,OA9JN4C,UA8JM,GA5JF5C,mBAAA,CA0JK,MA1JL6C,UA0JK,GAxJDC,UAAgC,EAEhC9C,mBAAA,CAEK,aADH+C,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAO;sBAAC,MAAsC,CAAtCC,UAAsC,EAAAC,UAAe,C;;QAG/EnD,mBAAA,CASK;IATAH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MAC5BvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;IAAU,eAAa,EAAC;mBAGzE7D,mBAAA,CAIK;IAJDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAe,eAAa,EAAC;MACtFvD,mBAAA,CAAyD,aAApD+C,YAAA,CAA+CC,sBAAA;IAAlCC,EAAE,EAAC;EAAW;sBAAC,MAAK,C;;QACjEjD,mBAAA,CAA4D,aAAvD+C,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAc;sBAAC,MAAK,C;;0CAIvBjD,mBAAA,CASK;IATAH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MAC5BvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;IAAU,eAAa,EAAC;mBAGzE7D,mBAAA,CAIK;IAJDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAe,eAAa,EAAC;MACtFvD,mBAAA,CAAwD,aAAnD+C,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAW;sBAAC,MAAI,C;;QAChEjD,mBAAA,CAA2D,aAAtD+C,YAAA,CAAiDC,sBAAA;IAApCC,EAAE,EAAC;EAAc;sBAAC,MAAI,C;;0CAItBjD,mBAAA,CASK;IATAH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MAC5BvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;IAAU,eAAa,EAAC;mBAGzE7D,mBAAA,CAIK;IAJDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAe,eAAa,EAAC;MACtFZ,mBAAA,qEAAsE,EACjG3C,mBAAA,CAAgE,aAA3D+C,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;0CAI3BjD,mBAAA,CASK;IATAH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MAC5BvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;IAAW,eAAa,EAAC;mBAG1E7D,mBAAA,CAIK;IAJDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAgB,eAAa,EAAC;MACvFvD,mBAAA,CAAyD,aAApD+C,YAAA,CAA+CC,sBAAA;IAAlCC,EAAE,EAAC;EAAU;sBAAC,MAAM,C;;QACjEjD,mBAAA,CAA4D,aAAvD+C,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;0CAIvBjD,mBAAA,CASK;IATAH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MAC5BvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;IAAW,eAAa,EAAC;mBAG1E7D,mBAAA,CAIK;IAJDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAgB,eAAa,EAAC;MACvFvD,mBAAA,CAA6D,aAAxD+C,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;QACrEjD,mBAAA,CAAgE,aAA3D+C,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;0CAK3BjD,mBAAA,CAQK;IARAH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MAC5BvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;IAAU,eAAa,EAAC;mBAGzE7D,mBAAA,CAGK;IAHDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAe,eAAa,EAAC;MACtFvD,mBAAA,CAA6D,aAAxD+C,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;0CAKjEjD,mBAAA,CASmB;IATdH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MACdvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;IAAU,eAAa,EAAC;mBAGzE7D,mBAAA,CAIK;IAJDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAe,eAAa,EAAC;MACtFvD,mBAAA,CAA8D,aAAzD+C,YAAA,CAAoDC,sBAAA;IAAvCC,EAAE,EAAC;EAAe;sBAAC,MAAM,C;;QACtEjD,mBAAA,CAAiE,aAA5D+C,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;0CAQxBjD,mBAAA,CASC;IATIH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MAChCvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;IAAU,eAAa,EAAC;mBAGzE7D,mBAAA,CAIK;IAJDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAe,eAAa,EAAC;MACtFvD,mBAAA,CAA+D,aAA1D+C,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACvEjD,mBAAA,CAAkE,aAA7D+C,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;0CAI3CjD,mBAAA,CAQmB;IARdH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MACdvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;IAAU,eAAa,EAAC;mBAGzE7D,mBAAA,CAGK;IAHDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAe,eAAa,EAAC;MACtFvD,mBAAA,CAAuE,aAAlE+C,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;0CAK7DjD,mBAAA,CASK;IATAH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MAC5BvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;IAAW,eAAa,EAAC;mBAG1E7D,mBAAA,CAIK;IAJDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAgB,eAAa,EAAC;MACvFZ,mBAAA,oEAAqE,EAChG3C,mBAAA,CAA+D,aAA1D+C,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;0CAKxCjD,mBAAA,CAQmB;IARdH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MACdvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;IAAU,eAAa,EAAC;mBAGzE7D,mBAAA,CAGK;IAHDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAe,eAAa,EAAC;MACtFvD,mBAAA,CAA6D,aAAxD+C,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;0CAUjEjD,mBAAA,CAWmB;IAXdH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MACdvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;IAAW,eAAa,EAAC;mBAG1E7D,mBAAA,CAMK;IANDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAgB,eAAa,EAAC;MACvFvD,mBAAA,CAAwD,aAAnD+C,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAS;sBAAC,MAAM,C;;QAChEjD,mBAAA,CAAsD,aAAjD+C,YAAA,CAA4CC,sBAAA;IAA/BC,EAAE,EAAC;EAAS;sBAAC,MAAI,C;;QACnCjD,mBAAA,CAAsD,aAAjD+C,YAAA,CAA4CC,sBAAA;IAA/BC,EAAE,EAAC;EAAS;sBAAC,MAAI,C;;QACnCN,mBAAA,8DAA+D,C,mCAM7C3C,mBAAA,CAOK;IAPAH,KAAK,EAAAuD,eAAA;MAAAC,MAAA,EAAYC,KAAA,CAAAC,UAAU;IAAA;MAC5BvD,mBAAA,CAEI;IAFDwD,IAAI,EAAC,sBAAsB;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAAC,UAAU;mBAGjD7D,mBAAA,CAEK;IAFDH,KAAK,EAAAuD,eAAA,EAAC,2BAA2B;MAAAU,EAAA,EAAeR,KAAA,CAAAC,UAAU;IAAA;IAAgB,eAAa,EAAC;MAC1FvD,mBAAA,CAAsD,aAAlD+C,YAAA,CAA6CC,sBAAA;IAAhCC,EAAE,EAAC;EAAU;sBAAC,MAAI,C;;8CASjDN,mBAAA,aAAgB,EAEhBoB,WAA4B,C,GAGhCpB,mBAAA,mBAAsB,C"}]}