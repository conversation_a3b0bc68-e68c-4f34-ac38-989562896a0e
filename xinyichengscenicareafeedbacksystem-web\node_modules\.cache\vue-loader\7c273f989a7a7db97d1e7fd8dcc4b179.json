{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue?vue&type=style&index=0&id=0fb93bb2&scoped=true&lang=css", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue", "mtime": 1749041655057}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749040033032}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749040034646}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749040033671}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Test.vue"], "names": [], "mappings": ";AAmPA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACtB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAChB", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/Test.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"questionnaire-container\">\r\n    <h2 class=\"questionnaire-title\">调查问卷</h2>\r\n\r\n    <!-- 未登录提示 -->\r\n    <div v-if=\"!isLoggedIn\" class=\"login-prompt\">\r\n      <el-alert\r\n        title=\"请先登录\"\r\n        description=\"您需要登录后才能参与问卷调查\"\r\n        type=\"warning\"\r\n        show-icon\r\n        :closable=\"false\">\r\n      </el-alert>\r\n      <div class=\"login-actions\">\r\n        <el-button type=\"primary\" @click=\"goToLogin\">去登录</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 已完成问卷提示 -->\r\n    <div v-else-if=\"hasCompleted\" class=\"completed-prompt\">\r\n      <el-alert\r\n        title=\"您已完成问卷调查\"\r\n        description=\"感谢您的参与！您可以查看统计结果或重新填写问卷\"\r\n        type=\"success\"\r\n        show-icon\r\n        :closable=\"false\">\r\n      </el-alert>\r\n      <div class=\"completed-actions\">\r\n        <el-button type=\"primary\" @click=\"viewStatistics\">查看统计结果</el-button>\r\n        <el-button type=\"warning\" @click=\"retakeQuestionnaire\">重新填写问卷</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 问卷表单 -->\r\n    <div v-else-if=\"showQuestionnaire\">\r\n      <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n\r\n      <div v-else>\r\n        <div v-for=\"(question, index) in questionsList\" :key=\"question.qid\" class=\"question-item\">\r\n          <div class=\"question-text\">{{ index + 1 }}. {{ question.question }}</div>\r\n          <div class=\"options\">\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"非常满意\" /> 非常满意\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"满意\" /> 满意\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"一般\" /> 一般\r\n            </label>\r\n            <label class=\"option\">\r\n              <input type=\"radio\" v-model=\"answers[question.qid]\" value=\"不满意\" /> 不满意\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"submit-container\">\r\n          <button @click=\"submitQuestionnaire\" class=\"submit-btn\">提交问卷</button>\r\n        </div>\r\n\r\n        <div v-if=\"submitMessage\" class=\"message\" :class=\"{ 'success': submitSuccess, 'error': !submitSuccess }\">\r\n          {{ submitMessage }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"QuestionnaireView\",\r\n  data() {\r\n    return {\r\n      questionsList: [],\r\n      answers: {},\r\n      loading: true,\r\n      submitMessage: '',\r\n      submitSuccess: false,\r\n      isLoggedIn: false,\r\n      hasCompleted: false,\r\n      showQuestionnaire: false,\r\n      userAccount: ''\r\n    };\r\n  },\r\n  created() {\r\n    this.checkLoginStatus();\r\n  },\r\n  methods: {\r\n    // 检查登录状态\r\n    checkLoginStatus() {\r\n      this.userAccount = sessionStorage.getItem(\"lname\");\r\n      this.isLoggedIn = !!this.userAccount;\r\n\r\n      if (this.isLoggedIn) {\r\n        this.checkCompletionStatus();\r\n      }\r\n    },\r\n\r\n    // 检查用户是否已完成问卷\r\n    checkCompletionStatus() {\r\n      let url = base + \"/results/checkCompleted?account=\" + this.userAccount;\r\n\r\n      request.post(url, {}).then((res) => {\r\n        if (res.code === 200) {\r\n          this.hasCompleted = res.resdata;\r\n          if (!this.hasCompleted) {\r\n            this.showQuestionnaire = true;\r\n            this.getQuestions();\r\n          }\r\n        } else {\r\n          // 检查失败，默认显示问卷\r\n          this.showQuestionnaire = true;\r\n          this.getQuestions();\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"检查完成状态失败:\", error);\r\n        // 检查失败，默认显示问卷\r\n        this.showQuestionnaire = true;\r\n        this.getQuestions();\r\n      });\r\n    },\r\n\r\n    // 去登录\r\n    goToLogin() {\r\n      this.$router.push('/ulogin');\r\n    },\r\n\r\n    // 查看统计结果\r\n    viewStatistics() {\r\n      // 创建一个新的路由来显示统计结果\r\n      this.$router.push('/questionnaireStatistics');\r\n    },\r\n\r\n    // 重新填写问卷\r\n    retakeQuestionnaire() {\r\n      this.$confirm('确定要重新填写问卷吗？这将清除您之前的答案。', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.hasCompleted = false;\r\n        this.showQuestionnaire = true;\r\n        this.getQuestions();\r\n      }).catch(() => {\r\n        // 用户取消\r\n      });\r\n    },\r\n\r\n    // 获取问卷问题列表\r\n    getQuestions() {\r\n      this.loading = true;\r\n      let url = base + \"/questions/list\";\r\n      let para = {};\r\n      \r\n      request.post(url, para, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {\r\n        if (res.resdata) {\r\n          this.questionsList = res.resdata;\r\n          // 初始化答案对象\r\n          this.questionsList.forEach(question => {\r\n            this.$set(this.answers, question.qid, '');\r\n          });\r\n        }\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error(\"获取问卷问题失败:\", error);\r\n        this.loading = false;\r\n      });\r\n    },\r\n    \r\n    // 提交问卷\r\n    submitQuestionnaire() {\r\n      // 检查是否所有问题都已回答\r\n      const unansweredQuestions = this.questionsList.filter(q => !this.answers[q.qid]);\r\n      \r\n      if (unansweredQuestions.length > 0) {\r\n        this.submitMessage = `请回答所有问题后再提交`;\r\n        this.submitSuccess = false;\r\n        return;\r\n      }\r\n      \r\n      // 准备提交数据\r\n      const results = [];\r\n      \r\n      // 将每个问题的答案转换为提交格式\r\n      this.questionsList.forEach(question => {\r\n        results.push({\r\n          qid: question.qid,\r\n          question: question.question,\r\n          status: this.answers[question.qid],\r\n          account: sessionStorage.getItem(\"lname\"),\r\n        });\r\n      });\r\n      \r\n      // 显示提交中状态\r\n      this.submitMessage = \"正在提交...\";\r\n      this.submitSuccess = true;\r\n      \r\n      // 一次性提交所有答案\r\n      let successCount = 0;\r\n      let failCount = 0;\r\n      \r\n      // 逐个提交答案并检查每个请求的响应\r\n      results.forEach(result => {\r\n        request.post(base + \"/results/add\", result)\r\n          .then(res => {\r\n            successCount++;\r\n            // 如果所有请求都已完成\r\n            if (successCount + failCount === results.length) {\r\n              if (failCount === 0) {\r\n                this.submitMessage = \"问卷提交成功，感谢您的反馈！\";\r\n                this.submitSuccess = true;\r\n                // 清空答案\r\n                this.answers = {};\r\n                this.questionsList.forEach(question => {\r\n                  this.$set(this.answers, question.qid, '');\r\n                });\r\n                // 更新完成状态\r\n                setTimeout(() => {\r\n                  this.hasCompleted = true;\r\n                  this.showQuestionnaire = false;\r\n                }, 2000);\r\n              } else {\r\n                this.submitMessage = `部分提交成功，${failCount}个问题提交失败`;\r\n                this.submitSuccess = false;\r\n              }\r\n            }\r\n          })\r\n          .catch(error => {\r\n            failCount++;\r\n            console.error(\"提交问题失败:\", error);\r\n            if (successCount + failCount === results.length) {\r\n              this.submitMessage = `部分提交成功，${failCount}个问题提交失败`;\r\n              this.submitSuccess = false;\r\n            }\r\n          });\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.questionnaire-container {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.questionnaire-title {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  font-size: 24px;\r\n}\r\n\r\n.loading {\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 16px;\r\n}\r\n\r\n.question-item {\r\n  margin-bottom: 25px;\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.question-text {\r\n  font-size: 16px;\r\n  margin-bottom: 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-left: 20px;\r\n}\r\n\r\n.option {\r\n  margin-right: 30px;\r\n  margin-bottom: 10px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.option input {\r\n  margin-right: 5px;\r\n}\r\n\r\n.submit-container {\r\n  text-align: center;\r\n  margin-top: 30px;\r\n}\r\n\r\n.submit-btn {\r\n  padding: 10px 30px;\r\n  background-color: #409EFF;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n}\r\n\r\n.submit-btn:hover {\r\n  background-color: #66b1ff;\r\n}\r\n\r\n.message {\r\n  text-align: center;\r\n  margin-top: 20px;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.success {\r\n  background-color: #f0f9eb;\r\n  color: #67c23a;\r\n  border: 1px solid #c2e7b0;\r\n}\r\n\r\n.error {\r\n  background-color: #fef0f0;\r\n  color: #f56c6c;\r\n  border: 1px solid #fbc4c4;\r\n}\r\n\r\n.login-prompt, .completed-prompt {\r\n  text-align: center;\r\n  padding: 30px;\r\n}\r\n\r\n.login-actions, .completed-actions {\r\n  margin-top: 20px;\r\n}\r\n\r\n.login-actions .el-button, .completed-actions .el-button {\r\n  margin: 0 10px;\r\n}\r\n</style>\r\n\r\n\r\n\r\n"]}]}