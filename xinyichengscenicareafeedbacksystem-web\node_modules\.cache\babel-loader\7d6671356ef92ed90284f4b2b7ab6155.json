{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\TicketInfoList.vue?vue&type=template&id=2a8a97ed", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\TicketInfoList.vue", "mtime": 1747063086545}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "_hoisted_1", "_createElementBlock", "_Fragment", "_renderList", "$data", "tilist", "item", "key", "ticketid", "href", "_hoisted_3", "_toDisplayString", "tname", "_hoisted_4", "description", "length", "substring", "_hoisted_5", "addtime", "_createVNode", "_component_el_pagination", "onCurrentChange", "$options", "handleCurrentChange", "page", "currentPage", "pageSize", "background", "layout", "total", "totalCount", "style"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\TicketInfoList.vue"], "sourcesContent": ["<template>\r\n   <div class=\"news-container1\">  \r\n  \r\n        <div class=\"news-item1\" v-for=\"item in tilist\" :key=\"item.ticketid\">  \r\n            <a :href=\"'ticketInfoView?id='+item.ticketid\">  <div class=\"news-title1\">{{item.tname}}</div>  \r\n            <div class=\"news-content1\">{{item.description.length > 70 ? item.description.substring(0,70):item.description}}</div>  \r\n            <div class=\"news-date1\">发布日期：{{item.addtime}}</div>  \r\n        </a>\r\n        </div>  \r\n  \r\n    </div> \r\n<el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\nbackground layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\r\nstyle=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"TicketInfoList\",\r\n  data() {\r\n    return {\r\n          page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 10, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n    },\r\n    tilist: \"\",\r\n \r\n    };\r\n  },\r\n  created() {\r\n    \r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {  \r\n    \r\n    // 分页\r\n    handleCurrentChange(val) {\r\n        this.page.currentPage = val;\r\n        this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n        let para = {\r\n                status:'上架',\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/ticketInfo/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\r\n        request.post(url, para).then((res) => {\r\n            if (res.resdata.length > 0) {\r\n                this.isPage = true;\r\n            } else {\r\n                this.isPage = false;\r\n            }\r\n            this.page.totalCount = res.count;\r\n            this.tilist = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n .news-container1 {  \r\n        max-width: 100%;  \r\n        margin: 0 auto;  \r\n    }  \r\n    .news-item1 {  \r\n        background-color: white;  \r\n        margin-bottom: 20px;  \r\n        padding: 15px;  \r\n     \r\n        box-shadow: 0 2px 5px rgba(0,0,0,0.1);  \r\n        transition: transform 0.3s ease;  \r\n        border-bottom: 1px solid #c3c0c0;\r\n    }  \r\n    .news-item1:hover {  \r\n        transform: translateY(-5px);  \r\n    }  \r\n\r\n    .news-title1 {  \r\n        font-size: 15px;  \r\n        font-weight: bold;  \r\n        margin-bottom: 10px;  \r\n    }  \r\n    .news-item1 a {  \r\n        color: #333;  \r\n        text-decoration: none;  \r\n    }  \r\n    .news-item1 a:hover {  \r\n        color: #007bff;  \r\n        text-decoration: underline;  \r\n    }  \r\n    .news-content1 {  \r\n        font-size: 14px;  \r\n        color: #333;  \r\n        margin-bottom: 10px;  \r\n    }  \r\n    .news-date1 {  \r\n        font-size: 12px;  \r\n        color: red;  \r\n    }  \r\n</style>\r\n\r\n\r\n\r\n"], "mappings": ";;EACQA,KAAK,EAAC;AAAiB;;;EAGkCA,KAAK,EAAC;AAAa;;EACnEA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAY;;;6DALhCC,mBAAA,CASO,OATPC,UASO,I,kBAPFC,mBAAA,CAKMC,SAAA,QAAAC,WAAA,CALiCC,KAAA,CAAAC,MAAM,EAAdC,IAAI;yBAAnCL,mBAAA,CAKM;MALDH,KAAK,EAAC,YAAY;MAAyBS,GAAG,EAAED,IAAI,CAACE;QACtDT,mBAAA,CAGA;MAHIU,IAAI,yBAAuBH,IAAI,CAACE;QAAYT,mBAAA,CAA6C,OAA7CW,UAA6C,EAAAC,gBAAA,CAAlBL,IAAI,CAACM,KAAK,kBACrFb,mBAAA,CAAqH,OAArHc,UAAqH,EAAAF,gBAAA,CAAxFL,IAAI,CAACQ,WAAW,CAACC,MAAM,QAAQT,IAAI,CAACQ,WAAW,CAACE,SAAS,UAAOV,IAAI,CAACQ,WAAW,kBAC7Gf,mBAAA,CAAmD,OAAnDkB,UAAmD,EAA3B,OAAK,GAAAN,gBAAA,CAAEL,IAAI,CAACY,OAAO,iB;oCAKvDC,YAAA,CAE4DC,wBAAA;IAF5CC,eAAc,EAAEC,QAAA,CAAAC,mBAAmB;IAAG,cAAY,EAAEnB,KAAA,CAAAoB,IAAI,CAACC,WAAW;IAAG,WAAS,EAAErB,KAAA,CAAAoB,IAAI,CAACE,QAAQ;IAC/GC,UAAU,EAAV,EAAU;IAACC,MAAM,EAAC,kCAAkC;IAAEC,KAAK,EAAEzB,KAAA,CAAAoB,IAAI,CAACM,UAAU;IAC5EC,KAA2C,EAA3C;MAAA;MAAA;IAAA"}]}