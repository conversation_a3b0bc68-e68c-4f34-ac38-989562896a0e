{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\NewsView.vue?vue&type=template&id=15c43c2d", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\NewsView.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgdG9EaXNwbGF5U3RyaW5nIGFzIF90b0Rpc3BsYXlTdHJpbmcsIGNyZWF0ZUVsZW1lbnRWTm9kZSBhcyBfY3JlYXRlRWxlbWVudFZOb2RlLCBvcGVuQmxvY2sgYXMgX29wZW5CbG9jaywgY3JlYXRlRWxlbWVudEJsb2NrIGFzIF9jcmVhdGVFbGVtZW50QmxvY2sgfSBmcm9tICJ2dWUiOwpjb25zdCBfaG9pc3RlZF8xID0gewogIHN0eWxlOiB7CiAgICAid2lkdGgiOiAiMTAwJSIKICB9Cn07CmNvbnN0IF9ob2lzdGVkXzIgPSB7CiAgc3R5bGU6IHsKICAgICJ0ZXh0LWFsaWduIjogImNlbnRlciIsCiAgICAiZm9udC1zaXplIjogIjIwcHgiCiAgfQp9Owpjb25zdCBfaG9pc3RlZF8zID0gewogIHN0eWxlOiB7CiAgICAidGV4dC1hbGlnbiI6ICJjZW50ZXIiCiAgfQp9Owpjb25zdCBfaG9pc3RlZF80ID0gewogIHN0eWxlOiB7CiAgICAidGV4dC1pbmRlbnQiOiAiMjRweCIKICB9Cn07CmNvbnN0IF9ob2lzdGVkXzUgPSBbImlubmVySFRNTCJdOwpleHBvcnQgZnVuY3Rpb24gcmVuZGVyKF9jdHgsIF9jYWNoZSwgJHByb3BzLCAkc2V0dXAsICRkYXRhLCAkb3B0aW9ucykgewogIHJldHVybiBfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soInRhYmxlIiwgX2hvaXN0ZWRfMSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoInRyIiwgbnVsbCwgW19jcmVhdGVFbGVtZW50Vk5vZGUoInRkIiwgX2hvaXN0ZWRfMiwgX3RvRGlzcGxheVN0cmluZygkZGF0YS5uZWxpc3QudGl0bGUpLCAxIC8qIFRFWFQgKi8pXSksIF9jcmVhdGVFbGVtZW50Vk5vZGUoInRyIiwgbnVsbCwgW19jcmVhdGVFbGVtZW50Vk5vZGUoInRkIiwgX2hvaXN0ZWRfMywgIuWPkeW4g+aXtumXtO+8miIgKyBfdG9EaXNwbGF5U3RyaW5nKCRkYXRhLm5lbGlzdC5wdWJsaXNodGltZSkgKyAiwqDCoCIsIDEgLyogVEVYVCAqLyldKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgidHIiLCBudWxsLCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgidGQiLCBfaG9pc3RlZF80LCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgaW5uZXJIVE1MOiAkZGF0YS5uZWxpc3QuY29udGVudAogIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIF9ob2lzdGVkXzUpXSldKV0pOwp9"}, {"version": 3, "names": ["style", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_toDisplayString", "$data", "nelist", "title", "_hoisted_3", "publishtime", "_hoisted_4", "innerHTML", "content"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\NewsView.vue"], "sourcesContent": ["<template>\r\n   \r\n<table style=\" width: 100%;\">\r\n    <tr>\r\n        <td style=\"text-align:center;    font-size: 20px;\">{{nelist.title}}</td>\r\n    </tr>\r\n    <tr>\r\n        <td style=\"text-align:center;\">发布时间：{{nelist.publishtime}}&nbsp;&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n        <td style=\"text-indent:24px;\"><div v-html=\"nelist.content\"></div></td>\r\n    </tr>\r\n</table>\r\n\r\n\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"NewsView\",\r\n  data() {\r\n    return {\r\n          nelist: \"\",\n \r\n\r\n    };\r\n  },\r\n  created() {\r\n    \n    this.getDatas();\n\r\n\r\n  },\r\n  methods: {  \r\n    \n    //获取列表数据\n    getDatas() {\n        let id = this.$route.query.id;\n        let para = {           \n        };\n        this.listLoading = true;\n        let url = base + \"/news/get?id=\" + id ;\n        request.post(url, para).then((res) => {\n            this.nelist = res.resdata;\n        });\n    },    \r\n\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n\r\n</style>\r\n\r\n\r\n\r\n"], "mappings": ";;EAEOA,KAAqB,EAArB;IAAA;EAAA;AAAqB;;EAEhBA,KAA8C,EAA9C;IAAA;IAAA;EAAA;AAA8C;;EAG9CA,KAA0B,EAA1B;IAAA;EAAA;AAA0B;;EAG1BA,KAAyB,EAAzB;IAAA;EAAA;AAAyB;;;uBARrCC,mBAAA,CAUQ,SAVRC,UAUQ,GATJC,mBAAA,CAEK,aADDA,mBAAA,CAAwE,MAAxEC,UAAwE,EAAAC,gBAAA,CAAnBC,KAAA,CAAAC,MAAM,CAACC,KAAK,iB,GAErEL,mBAAA,CAEK,aADDA,mBAAA,CAA2E,MAA3EM,UAA2E,EAA5C,OAAK,GAAAJ,gBAAA,CAAEC,KAAA,CAAAC,MAAM,CAACG,WAAW,IAAE,IAAY,gB,GAE1EP,mBAAA,CAEK,aADDA,mBAAA,CAAsE,MAAtEQ,UAAsE,GAAxCR,mBAAA,CAAmC;IAA9BS,SAAuB,EAAfN,KAAA,CAAAC,MAAM,CAACM"}]}