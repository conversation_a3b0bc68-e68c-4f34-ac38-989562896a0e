{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\BbsView.vue?vue&type=template&id=9f4da21a", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\BbsView.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["style", "width", "align", "_createElementVNode", "href", "valign", "class", "_hoisted_1", "_hoisted_3", "_toDisplayString", "$data", "postreply", "ptitle", "_hoisted_4", "_hoisted_6", "vwcnt", "_hoisted_8", "by1", "_hoisted_10", "_hoisted_11", "_hoisted_12", "src", "by2", "_hoisted_14", "_hoisted_15", "account", "_hoisted_16", "_hoisted_17", "_hoisted_18", "innerHTML", "pcontent", "_hoisted_20", "ptime", "_createElementBlock", "_Fragment", "_renderList", "item", "_hoisted_21", "_hoisted_22", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "rcontent", "_hoisted_29", "rtime", "_createVNode", "_component_el_form", "model", "formData", "ref", "rules", "_component_el_form_item", "label", "prop", "_component_WangEditor", "$event", "config", "_ctx", "editorConfig", "isClear", "onChange", "$options", "<PERSON><PERSON><PERSON><PERSON>", "height", "_component_el_button", "type", "onClick", "save", "loading", "btnLoading"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\BbsView.vue"], "sourcesContent": ["<template>\r\n  <table style=\"width:100%;line-height:27px; font-size:12px;border: 1px solid #C2D5E3;  margin-bottom:10px;\">\r\n    <tr>\r\n\r\n      <td> <span style=\"font-size:14px; font-weight:bold\">\r\n          {{ postreply.ptitle }}\r\n        </span>\r\n\r\n      </td>\r\n      <td width=\"200\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\">\r\n        查看：<b style=\"color:red;\">\r\n          {{ postreply.vwcnt }}\r\n        </b> |\r\n        回复：<b style=\"color:red;\">\r\n          {{ postreply.by1 }}\r\n        </b> |\r\n        <a href=\"/bbs\">返回列表</a>\r\n      </td>\r\n    </tr>\r\n  </table>\r\n  <table style=\"width:100%;line-height:32px; font-size:13px;border: 1px solid #C2D5E3; \">\r\n    <tr>\r\n      <td width=\"100\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\" valign=\"top\">\r\n        <div>\r\n          <img style=\"width: 80px;height: 80px;border-radius: 50%\"\r\n            :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' + postreply.by2\" />\r\n        </div>\r\n\r\n        <span class=\"cu-tag bg-gradual-green\" style=\" height:28px;\">楼主</span>\r\n        <span class=\"text-red\">\r\n          {{ postreply.account }}\r\n        </span>\r\n      </td>\r\n      <td valign=\"top\">\r\n        <table width=\"100%\">\r\n          <tr>\r\n            <td style=\" border-bottom: 1px dashed #e3e3e3;\">\r\n              <div v-html=\"postreply.pcontent\">\r\n\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td style=\"   text-align: right\">\r\n              时间：{{ postreply.ptime }}\r\n            </td>\r\n          </tr>\r\n        </table>\r\n\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n  <table style=\"width:100%;line-height:32px; font-size:13px;border: 1px solid #C2D5E3; \" v-for=\"item in postreply.postreply\">\r\n    <tr>\r\n      <td width=\"100\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\" valign=\"top\">\r\n        <div>\r\n          <img style=\"width: 80px;height: 80px;border-radius: 50%\"\r\n            :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' + item.by1\" />\r\n        </div>\r\n\r\n        <span class=\"text-red\">\r\n          {{ item.account }}\r\n        </span>\r\n      </td>\r\n      <td valign=\"top\">\r\n        <table width=\"100%\">\r\n          <tr>\r\n            <td style=\" border-bottom: 1px dashed #e3e3e3;\">\r\n              <div v-html=\"item.rcontent\">\r\n\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td style=\"   text-align: right\">\r\n              时间：{{ item.rtime }}\r\n            </td>\r\n          </tr>\r\n        </table>\r\n\r\n      </td>\r\n    </tr>\r\n  </table>\r\n\r\n  <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\" align=\"left\">\r\n\r\n    <el-form-item label=\"回复内容\" prop=\"rcontent\">\r\n      <WangEditor ref=\"wangEditorRef\" v-model=\"formData.rcontent\" :config=\"editorConfig\" :isClear=\"isClear\"\r\n        @change=\"editorChange\" height=\"200\"></WangEditor>\r\n    </el-form-item>\r\n    <el-form-item label=\"\">\r\n      <el-button type=\"primary\" @click=\"save\" :loading=\"btnLoading\">提 交</el-button>\r\n    </el-form-item>\r\n\r\n  </el-form>\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport WangEditor from \"../../components/WangEditor\";\r\n\r\nexport default {\r\n  name: \"bbsView\",\r\n  components: {\r\n    WangEditor,\r\n  },\r\n  data() {\r\n    return {\r\n      postreply: \"\",\r\n\r\n      formData: {\r\n        rcontent: \"\",\r\n      },\r\n\r\n      rules: {\r\n        rcontent: [\r\n          { required: true, message: \"请输入回复内容\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n\r\n\r\n    };\r\n  },\r\n  created() {\r\n\r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n      let id = this.$route.query.id;\r\n      let para = {\r\n      };\r\n      this.listLoading = true;\r\n      let url = base + \"/posts/get?id=\" + id;\r\n      request.post(url, para).then((res) => {\r\n        this.postreply = res.resdata;\r\n      });\r\n    },\r\n\r\n    //回复\r\n    save() {\r\n      var lname = sessionStorage.getItem(\"lname\");\r\n      if (lname == null) {\r\n        this.$message({\r\n          message: \"请先登录\",\r\n          type: \"error\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.$refs.formDataRef.validate((valid) => {\r\n        if (valid) {\r\n          this.btnLoading = true;\r\n          let para = {\r\n            psid: this.postreply.psid,\r\n            rcontent: this.formData.rcontent,\r\n            account: lname,\r\n          };\r\n          let url = base + \"/postreply/add\";\r\n          request.post(url, para).then((res) => {\r\n            this.btnLoading = false;\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                message: \"回复成功\",\r\n                type: \"success\",\r\n              });\r\n              this.getDatas();\r\n              this.formData.rcontent = \"\";\r\n\r\n              //设置富文本编辑器内容\r\n              this.$nextTick(() => {\r\n                this.$refs[\"wangEditorRef\"].editor.txt.html(\"\");\r\n              });\r\n\r\n\r\n            } else {\r\n              this.$message({\r\n                message: res.msg,\r\n                type: \"error\",\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.rcontent = val;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.text {\r\n  border: 1px solid #ccc;\r\n  min-height: 180px;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;EACSA,KAAmG,EAAnG;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;AAAmG;iDAGlG,GAAC;;EAAMA,KAAwC,EAAxC;IAAA;IAAA;EAAA;AAAwC;;EAK/CC,KAAK,EAAC,KAAK;EAACC,KAAK,EAAC,QAAQ;EAACF,KAAwC,EAAxC;IAAA;EAAA;;iDAAyC,MACnE;;EAAGA,KAAkB,EAAlB;IAAA;EAAA;AAAkB;iDAEpB,QACD;;EAAGA,KAAkB,EAAlB;IAAA;EAAA;AAAkB;iDAEpB,KACJ;iCAAAG,mBAAA,CAAuB;EAApBC,IAAI,EAAC;AAAM,GAAC,MAAI;;EAIlBJ,KAA+E,EAA/E;IAAA;IAAA;IAAA;IAAA;EAAA;AAA+E;;EAE9EC,KAAK,EAAC,KAAK;EAACC,KAAK,EAAC,QAAQ;EAACF,KAAwC,EAAxC;IAAA;EAAA,CAAwC;EAACK,MAAM,EAAC;;;iCAM7EF,mBAAA,CAAqE;EAA/DG,KAAK,EAAC,yBAAyB;EAACN,KAAqB,EAArB;IAAA;EAAA;GAAsB,IAAE;;EACxDM,KAAK,EAAC;AAAU;;EAIpBD,MAAM,EAAC;AAAK;;EACPJ,KAAK,EAAC;AAAM;;EAEXD,KAA2C,EAA3C;IAAA;EAAA;AAA2C;;;EAO3CA,KAA4B,EAA5B;IAAA;EAAA;AAA4B;;EAUnCA,KAA+E,EAA/E;IAAA;IAAA;IAAA;IAAA;EAAA;AAA+E;;EAE9EC,KAAK,EAAC,KAAK;EAACC,KAAK,EAAC,QAAQ;EAACF,KAAwC,EAAxC;IAAA;EAAA,CAAwC;EAACK,MAAM,EAAC;;;;EAMvEC,KAAK,EAAC;AAAU;;EAIpBD,MAAM,EAAC;AAAK;;EACPJ,KAAK,EAAC;AAAM;;EAEXD,KAA2C,EAA3C;IAAA;EAAA;AAA2C;;;EAO3CA,KAA4B,EAA5B;IAAA;EAAA;AAA4B;kDAiBwB,KAAG;;;;;;6DA3FrEG,mBAAA,CAkBQ,SAlBRI,UAkBQ,GAjBNJ,mBAAA,CAgBK,aAdHA,mBAAA,CAIK,a,YAJAA,mBAAA,CAEI,QAFJK,UAEI,EAAAC,gBAAA,CADFC,KAAA,CAAAC,SAAS,CAACC,MAAM,iB,GAIvBT,mBAAA,CAQK,MARLU,UAQK,G,YAPAV,mBAAA,CAEC,KAFDW,UAEC,EAAAL,gBAAA,CADCC,KAAA,CAAAC,SAAS,CAACI,KAAK,kB,YAEjBZ,mBAAA,CAEC,KAFDa,UAEC,EAAAP,gBAAA,CADCC,KAAA,CAAAC,SAAS,CAACM,GAAG,kB,YAElBC,WAAuB,C,OAI7Bf,mBAAA,CA+BQ,SA/BRgB,WA+BQ,GA9BNhB,mBAAA,CA6BK,aA5BHA,mBAAA,CAUK,MAVLiB,WAUK,GATHjB,mBAAA,CAGM,cAFJA,mBAAA,CACuF;IADlFH,KAAmD,EAAnD;MAAA;MAAA;MAAA;IAAA,CAAmD;IACrDqB,GAAG,gEAAgEX,KAAA,CAAAC,SAAS,CAACW;0CAGlFC,WAAqE,EACrEpB,mBAAA,CAEO,QAFPqB,WAEO,EAAAf,gBAAA,CADFC,KAAA,CAAAC,SAAS,CAACc,OAAO,iB,GAGxBtB,mBAAA,CAgBK,MAhBLuB,WAgBK,GAfHvB,mBAAA,CAaQ,SAbRwB,WAaQ,GAZNxB,mBAAA,CAMK,aALHA,mBAAA,CAIK,MAJLyB,WAIK,GAHHzB,mBAAA,CAEM;IAFD0B,SAA2B,EAAnBnB,KAAA,CAAAC,SAAS,CAACmB;4CAK3B3B,mBAAA,CAIK,aAHHA,mBAAA,CAEK,MAFL4B,WAEK,EAF4B,MAC5B,GAAAtB,gBAAA,CAAGC,KAAA,CAAAC,SAAS,CAACqB,KAAK,iB,8BASjCC,mBAAA,CA8BQC,SAAA,QAAAC,WAAA,CA9B8FzB,KAAA,CAAAC,SAAS,CAACA,SAAS,EAA3ByB,IAAI;yBAAlGH,mBAAA,CA8BQ,SA9BRI,WA8BQ,GA7BNlC,mBAAA,CA4BK,aA3BHA,mBAAA,CASK,MATLmC,WASK,GARHnC,mBAAA,CAGM,cAFJA,mBAAA,CACkF;MAD7EH,KAAmD,EAAnD;QAAA;QAAA;QAAA;MAAA,CAAmD;MACrDqB,GAAG,gEAAgEe,IAAI,CAACnB;4CAG7Ed,mBAAA,CAEO,QAFPoC,WAEO,EAAA9B,gBAAA,CADF2B,IAAI,CAACX,OAAO,iB,GAGnBtB,mBAAA,CAgBK,MAhBLqC,WAgBK,GAfHrC,mBAAA,CAaQ,SAbRsC,WAaQ,GAZNtC,mBAAA,CAMK,aALHA,mBAAA,CAIK,MAJLuC,WAIK,GAHHvC,mBAAA,CAEM;MAFD0B,SAAsB,EAAdO,IAAI,CAACO;8CAKtBxC,mBAAA,CAIK,aAHHA,mBAAA,CAEK,MAFLyC,WAEK,EAF4B,MAC5B,GAAAnC,gBAAA,CAAG2B,IAAI,CAACS,KAAK,iB;oCAS5BC,YAAA,CAUUC,kBAAA;IAVAC,KAAK,EAAEtC,KAAA,CAAAuC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEzC,KAAA,CAAAyC,KAAK;IAAEjD,KAAK,EAAC;;sBAElF,MAGe,CAHf4C,YAAA,CAGeM,uBAAA;MAHDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAC9B,MACmD,CADnDR,YAAA,CACmDS,qBAAA;QADvCL,GAAG,EAAC,eAAe;oBAAUxC,KAAA,CAAAuC,QAAQ,CAACN,QAAQ;mEAAjBjC,KAAA,CAAAuC,QAAQ,CAACN,QAAQ,GAAAa,MAAA;QAAGC,MAAM,EAAEC,IAAA,CAAAC,YAAY;QAAGC,OAAO,EAAEF,IAAA,CAAAE,OAAO;QACjGC,QAAM,EAAEC,QAAA,CAAAC,YAAY;QAAEC,MAAM,EAAC;;;QAElClB,YAAA,CAEeM,uBAAA;MAFDC,KAAK,EAAC;IAAE;wBACpB,MAA6E,CAA7EP,YAAA,CAA6EmB,oBAAA;QAAlEC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAEL,QAAA,CAAAM,IAAI;QAAGC,OAAO,EAAEX,IAAA,CAAAY;;0BAAY,MAAG,C"}]}