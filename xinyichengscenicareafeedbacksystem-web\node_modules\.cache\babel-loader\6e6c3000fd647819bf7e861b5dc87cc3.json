{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\QuestionnaireStatistics.vue?vue&type=template&id=35b258be&scoped=true", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\QuestionnaireStatistics.vue", "mtime": 1749041571658}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "_createElementBlock", "_hoisted_1", "_hoisted_2", "$data", "loading", "_hoisted_3", "_createVNode", "_component_el_loading", "text", "_hoisted_4", "statisticsData", "length", "_hoisted_5", "_component_el_empty", "description", "_hoisted_6", "_hoisted_7", "_Fragment", "_renderList", "question", "index", "key", "qid", "_hoisted_8", "_toDisplayString", "id", "style", "_normalizeStyle", "chartStyle", "_hoisted_10", "_component_el_button", "type", "onClick", "$options", "goBack", "retakeQuestionnaire"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\QuestionnaireStatistics.vue"], "sourcesContent": ["<template>\n  <div class=\"statistics-container\">\n    <h2 class=\"statistics-title\">您的问卷调查统计结果</h2>\n    \n    <div v-if=\"loading\" class=\"loading\">\n      <el-loading text=\"正在加载统计数据...\"></el-loading>\n    </div>\n    \n    <div v-else>\n      <div v-if=\"statisticsData.length === 0\" class=\"no-data\">\n        <el-empty description=\"暂无统计数据\"></el-empty>\n      </div>\n      \n      <div v-else>\n        <div class=\"intro-text\">\n          <p>感谢您参与我们的问卷调查！以下是所有用户的统计结果：</p>\n        </div>\n        \n        <div v-for=\"(question, index) in statisticsData\" :key=\"question.qid\" class=\"chart-container\">\n          <h3 class=\"question-title\">{{ index + 1 }}. {{ question.question }}</h3>\n          <div :id=\"'user-chart-' + question.qid\" class=\"chart\" :style=\"chartStyle\"></div>\n        </div>\n        \n        <div class=\"action-buttons\">\n          <el-button type=\"primary\" @click=\"goBack\">返回</el-button>\n          <el-button type=\"success\" @click=\"retakeQuestionnaire\">重新填写问卷</el-button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../utils/http\";\nimport * as echarts from \"echarts\";\n\nexport default {\n  name: 'QuestionnaireStatistics',\n  data() {\n    return {\n      loading: true,\n      statisticsData: [],\n      chartStyle: { \n        width: \"100%\", \n        height: \"350px\",\n        marginBottom: \"20px\"\n      },\n      charts: [] // 存储图表实例\n    };\n  },\n  \n  mounted() {\n    this.getStatisticsData();\n  },\n  \n  beforeDestroy() {\n    // 销毁所有图表实例\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.dispose();\n      }\n    });\n  },\n  \n  methods: {\n    // 获取统计数据\n    getStatisticsData() {\n      this.loading = true;\n      let url = base + \"/results/statistics\";\n      \n      request.post(url, {}).then((res) => {\n        if (res.code === 200 && res.resdata) {\n          this.statisticsData = res.resdata;\n          this.$nextTick(() => {\n            this.initAllCharts();\n          });\n        } else {\n          this.$message({\n            message: \"获取统计数据失败\",\n            type: \"error\",\n            offset: 320,\n          });\n        }\n        this.loading = false;\n      }).catch(error => {\n        console.error(\"获取统计数据失败:\", error);\n        this.$message({\n          message: \"获取统计数据失败\",\n          type: \"error\",\n          offset: 320,\n        });\n        this.loading = false;\n      });\n    },\n    \n    // 初始化所有图表\n    initAllCharts() {\n      this.statisticsData.forEach((question, index) => {\n        this.initChart(question, index);\n      });\n    },\n    \n    // 初始化单个图表\n    initChart(questionData, index) {\n      const chartId = 'user-chart-' + questionData.qid;\n      const chartDom = document.getElementById(chartId);\n      \n      if (!chartDom) {\n        console.error(`Chart container not found: ${chartId}`);\n        return;\n      }\n      \n      const chart = echarts.init(chartDom);\n      this.charts.push(chart);\n      \n      // 准备饼图数据\n      const pieData = questionData.options || [];\n      const legendData = pieData.map(item => item.name);\n      \n      // 定义颜色方案\n      const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];\n      \n      const option = {\n        title: {\n          text: `问题 ${index + 1} 统计结果`,\n          left: 'center',\n          top: 10,\n          textStyle: {\n            fontSize: 14,\n            fontWeight: 'bold'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c}人 ({d}%)'\n        },\n        legend: {\n          orient: 'horizontal',\n          left: 'center',\n          bottom: 10,\n          data: legendData\n        },\n        color: colors,\n        series: [\n          {\n            name: '选择人数',\n            type: 'pie',\n            radius: ['30%', '60%'],\n            center: ['50%', '45%'],\n            avoidLabelOverlap: false,\n            label: {\n              show: true,\n              position: 'outside',\n              formatter: '{b}\\n{c}人 ({d}%)',\n              fontSize: 12\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: '14',\n                fontWeight: 'bold'\n              }\n            },\n            labelLine: {\n              show: true\n            },\n            data: pieData\n          }\n        ]\n      };\n      \n      chart.setOption(option);\n      \n      // 响应式调整\n      window.addEventListener('resize', () => {\n        chart.resize();\n      });\n    },\n    \n    // 返回\n    goBack() {\n      this.$router.go(-1);\n    },\n    \n    // 重新填写问卷\n    retakeQuestionnaire() {\n      this.$confirm('确定要重新填写问卷吗？这将清除您之前的答案。', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      }).then(() => {\n        // 跳转到问卷页面\n        this.$router.push('/test');\n      }).catch(() => {\n        // 用户取消\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.statistics-container {\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 20px;\n  background-color: #f9f9f9;\n  min-height: 100vh;\n}\n\n.statistics-title {\n  text-align: center;\n  margin-bottom: 30px;\n  font-size: 24px;\n  color: #333;\n}\n\n.loading {\n  text-align: center;\n  padding: 50px;\n}\n\n.no-data {\n  text-align: center;\n  padding: 50px;\n}\n\n.intro-text {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 15px;\n  background-color: #e8f4fd;\n  border-radius: 8px;\n  color: #666;\n}\n\n.chart-container {\n  margin-bottom: 40px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  background-color: #fff;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.question-title {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n  font-weight: bold;\n  text-align: left;\n  padding-bottom: 10px;\n  border-bottom: 2px solid #409EFF;\n}\n\n.chart {\n  margin: 0 auto;\n}\n\n.action-buttons {\n  text-align: center;\n  margin-top: 30px;\n  padding: 20px;\n}\n\n.action-buttons .el-button {\n  margin: 0 10px;\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAsB;gEAC/BC,mBAAA,CAA4C;EAAxCD,KAAK,EAAC;AAAkB,GAAC,YAAU;;;EAEnBA,KAAK,EAAC;;;;;;;EAKgBA,KAAK,EAAC;;;;;gEAK5CC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAY,I,aACrBC,mBAAA,CAAiC,WAA9B,4BAA0B,E;;EAIzBD,KAAK,EAAC;AAAgB;;;EAIvBA,KAAK,EAAC;AAAgB;kDACiB,IAAE;kDACW,QAAM;;;;;uBAxBrEE,mBAAA,CA4BM,OA5BNC,UA4BM,GA3BJC,UAA4C,EAEjCC,KAAA,CAAAC,OAAO,I,cAAlBJ,mBAAA,CAEM,OAFNK,UAEM,GADJC,YAAA,CAA4CC,qBAAA;IAAhCC,IAAI,EAAC;EAAa,G,oBAGhCR,mBAAA,CAoBM,OAAAS,UAAA,GAnBON,KAAA,CAAAO,cAAc,CAACC,MAAM,U,cAAhCX,mBAAA,CAEM,OAFNY,UAEM,GADJN,YAAA,CAA0CO,mBAAA;IAAhCC,WAAW,EAAC;EAAQ,G,oBAGhCd,mBAAA,CAcM,OAAAe,UAAA,GAbJC,UAEM,G,kBAENhB,mBAAA,CAGMiB,SAAA,QAAAC,WAAA,CAH2Bf,KAAA,CAAAO,cAAc,GAAlCS,QAAQ,EAAEC,KAAK;yBAA5BpB,mBAAA,CAGM;MAH4CqB,GAAG,EAAEF,QAAQ,CAACG,GAAG;MAAExB,KAAK,EAAC;QACzEC,mBAAA,CAAwE,MAAxEwB,UAAwE,EAAAC,gBAAA,CAA1CJ,KAAK,QAAO,IAAE,GAAAI,gBAAA,CAAGL,QAAQ,CAACA,QAAQ,kBAChEpB,mBAAA,CAAgF;MAA1E0B,EAAE,kBAAkBN,QAAQ,CAACG,GAAG;MAAExB,KAAK,EAAC,OAAO;MAAE4B,KAAK,EAAAC,eAAA,CAAExB,KAAA,CAAAyB,UAAU;;kCAG1E7B,mBAAA,CAGM,OAHN8B,WAGM,GAFJvB,YAAA,CAAwDwB,oBAAA;IAA7CC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,QAAA,CAAAC;;sBAAQ,MAAE,C;;kCAC5C5B,YAAA,CAAyEwB,oBAAA;IAA9DC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,QAAA,CAAAE;;sBAAqB,MAAM,C"}]}