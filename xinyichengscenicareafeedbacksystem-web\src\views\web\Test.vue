<template>
  <div class="questionnaire-container">
    <h2 class="questionnaire-title">调查问卷</h2>

    <!-- 未登录提示 -->
    <div v-if="!isLoggedIn" class="login-prompt">
      <el-alert
        title="请先登录"
        description="您需要登录后才能参与问卷调查"
        type="warning"
        show-icon
        :closable="false">
      </el-alert>
      <div class="login-actions">
        <el-button type="primary" @click="goToLogin">去登录</el-button>
      </div>
    </div>

    <!-- 已完成问卷提示 -->
    <div v-else-if="hasCompleted" class="completed-prompt">
      <el-alert
        title="您已完成问卷调查"
        description="感谢您的参与！您可以查看统计结果或重新填写问卷"
        type="success"
        show-icon
        :closable="false">
      </el-alert>
      <div class="completed-actions">
        <el-button type="primary" @click="viewStatistics">查看统计结果</el-button>
        <el-button type="warning" @click="retakeQuestionnaire">重新填写问卷</el-button>
      </div>
    </div>

    <!-- 问卷表单 -->
    <div v-else-if="showQuestionnaire">
      <div v-if="loading" class="loading">加载中...</div>

      <div v-else>
        <div v-for="(question, index) in questionsList" :key="question.qid" class="question-item">
          <div class="question-text">{{ index + 1 }}. {{ question.question }}</div>
          <div class="options">
            <label class="option">
              <input type="radio" v-model="answers[question.qid]" value="非常满意" /> 非常满意
            </label>
            <label class="option">
              <input type="radio" v-model="answers[question.qid]" value="满意" /> 满意
            </label>
            <label class="option">
              <input type="radio" v-model="answers[question.qid]" value="一般" /> 一般
            </label>
            <label class="option">
              <input type="radio" v-model="answers[question.qid]" value="不满意" /> 不满意
            </label>
          </div>
        </div>

        <div class="submit-container">
          <button @click="submitQuestionnaire" class="submit-btn">提交问卷</button>
        </div>

        <div v-if="submitMessage" class="message" :class="{ 'success': submitSuccess, 'error': !submitSuccess }">
          {{ submitMessage }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import request, { base } from "../../../utils/http";
export default {
  name: "QuestionnaireView",
  data() {
    return {
      questionsList: [],
      answers: {},
      loading: true,
      submitMessage: '',
      submitSuccess: false,
      isLoggedIn: false,
      hasCompleted: false,
      showQuestionnaire: false,
      userAccount: ''
    };
  },
  created() {
    this.checkLoginStatus();
  },
  methods: {
    // 检查登录状态
    checkLoginStatus() {
      this.userAccount = sessionStorage.getItem("lname");
      this.isLoggedIn = !!this.userAccount;

      if (this.isLoggedIn) {
        this.checkCompletionStatus();
      }
    },

    // 检查用户是否已完成问卷
    checkCompletionStatus() {
      let url = base + "/results/checkCompleted?account=" + this.userAccount;

      request.post(url, {}).then((res) => {
        if (res.code === 200) {
          this.hasCompleted = res.resdata;
          if (!this.hasCompleted) {
            this.showQuestionnaire = true;
            this.getQuestions();
          }
        } else {
          // 检查失败，默认显示问卷
          this.showQuestionnaire = true;
          this.getQuestions();
        }
      }).catch(error => {
        console.error("检查完成状态失败:", error);
        // 检查失败，默认显示问卷
        this.showQuestionnaire = true;
        this.getQuestions();
      });
    },

    // 去登录
    goToLogin() {
      this.$router.push('/ulogin');
    },

    // 查看统计结果
    viewStatistics() {
      // 创建一个新的路由来显示统计结果
      this.$router.push('/questionnaireStatistics');
    },

    // 重新填写问卷
    retakeQuestionnaire() {
      this.$confirm('确定要重新填写问卷吗？这将清除您之前的答案。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.hasCompleted = false;
        this.showQuestionnaire = true;
        this.getQuestions();
      }).catch(() => {
        // 用户取消
      });
    },

    // 获取问卷问题列表
    getQuestions() {
      this.loading = true;
      let url = base + "/questions/list";
      let para = {};
      
      request.post(url, para, { params: { currentPage: 1, pageSize: 100 } }).then((res) => {
        if (res.resdata) {
          this.questionsList = res.resdata;
          // 初始化答案对象
          this.questionsList.forEach(question => {
            this.$set(this.answers, question.qid, '');
          });
        }
        this.loading = false;
      }).catch(error => {
        console.error("获取问卷问题失败:", error);
        this.loading = false;
      });
    },
    
    // 提交问卷
    submitQuestionnaire() {
      // 检查是否所有问题都已回答
      const unansweredQuestions = this.questionsList.filter(q => !this.answers[q.qid]);
      
      if (unansweredQuestions.length > 0) {
        this.submitMessage = `请回答所有问题后再提交`;
        this.submitSuccess = false;
        return;
      }
      
      // 准备提交数据
      const results = [];
      
      // 将每个问题的答案转换为提交格式
      this.questionsList.forEach(question => {
        results.push({
          qid: question.qid,
          question: question.question,
          status: this.answers[question.qid],
          account: sessionStorage.getItem("lname"),
        });
      });
      
      // 显示提交中状态
      this.submitMessage = "正在提交...";
      this.submitSuccess = true;
      
      // 一次性提交所有答案
      let successCount = 0;
      let failCount = 0;
      
      // 逐个提交答案并检查每个请求的响应
      results.forEach(result => {
        request.post(base + "/results/add", result)
          .then(res => {
            successCount++;
            // 如果所有请求都已完成
            if (successCount + failCount === results.length) {
              if (failCount === 0) {
                this.submitMessage = "问卷提交成功，感谢您的反馈！";
                this.submitSuccess = true;
                // 清空答案
                this.answers = {};
                this.questionsList.forEach(question => {
                  this.$set(this.answers, question.qid, '');
                });
                // 更新完成状态
                setTimeout(() => {
                  this.hasCompleted = true;
                  this.showQuestionnaire = false;
                }, 2000);
              } else {
                this.submitMessage = `部分提交成功，${failCount}个问题提交失败`;
                this.submitSuccess = false;
              }
            }
          })
          .catch(error => {
            failCount++;
            console.error("提交问题失败:", error);
            if (successCount + failCount === results.length) {
              this.submitMessage = `部分提交成功，${failCount}个问题提交失败`;
              this.submitSuccess = false;
            }
          });
      });
    }
  }
};
</script>

<style scoped>
.questionnaire-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.questionnaire-title {
  text-align: center;
  margin-bottom: 30px;
  font-size: 24px;
}

.loading {
  text-align: center;
  padding: 20px;
  font-size: 16px;
}

.question-item {
  margin-bottom: 25px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.question-text {
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: bold;
}

.options {
  display: flex;
  flex-wrap: wrap;
  margin-left: 20px;
}

.option {
  margin-right: 30px;
  margin-bottom: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.option input {
  margin-right: 5px;
}

.submit-container {
  text-align: center;
  margin-top: 30px;
}

.submit-btn {
  padding: 10px 30px;
  background-color: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.submit-btn:hover {
  background-color: #66b1ff;
}

.message {
  text-align: center;
  margin-top: 20px;
  padding: 10px;
  border-radius: 4px;
}

.success {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 1px solid #c2e7b0;
}

.error {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.login-prompt, .completed-prompt {
  text-align: center;
  padding: 30px;
}

.login-actions, .completed-actions {
  margin-top: 20px;
}

.login-actions .el-button, .completed-actions .el-button {
  margin: 0 10px;
}
</style>



