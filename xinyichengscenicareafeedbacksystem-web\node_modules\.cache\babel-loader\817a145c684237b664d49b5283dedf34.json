{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\Main.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\Main.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IEhlYWRlciBmcm9tICIuLi9jb21wb25lbnRzL0hlYWRlciI7CmltcG9ydCBMZWZ0TWVudSBmcm9tICIuLi9jb21wb25lbnRzL0xlZnRNZW51IjsKaW1wb3J0IHsgRWxDb25maWdQcm92aWRlciB9IGZyb20gImVsZW1lbnQtcGx1cyI7CmltcG9ydCB6aENuIGZyb20gImVsZW1lbnQtcGx1cy9saWIvbG9jYWxlL2xhbmcvemgtY24iOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIk1haW5MYXlvdXQiLAogIGNvbXBvbmVudHM6IHsKICAgIEhlYWRlciwKICAgIExlZnRNZW51LAogICAgW0VsQ29uZmlnUHJvdmlkZXIubmFtZV06IEVsQ29uZmlnUHJvdmlkZXIKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2NhbGU6IHpoQ24KICAgIH07CiAgfSwKICBtb3VudGVkKCkge30sCiAgbWV0aG9kczoge30KfTs="}, {"version": 3, "names": ["Header", "LeftMenu", "ElConfigProvider", "zhCn", "name", "components", "data", "locale", "mounted", "methods"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\Main.vue"], "sourcesContent": ["<template>\r\n\r\n<el-config-provider :locale=\"locale\">\r\n \r\n    <div id=\"wrapper\">\r\n\r\n <Header />\r\n\r\n<LeftMenu />\r\n\r\n<!-- Page Content Start -->\r\n<div class=\"content-page\">\r\n    <div class=\"content\">\r\n        <div class=\"container-fluid\">\r\n            <router-view />        \r\n\r\n        </div>\r\n    </div>\r\n</div>\r\n<!-- End Page Content-->\r\n\r\n\r\n\r\n</div>\r\n\r\n\r\n\r\n</el-config-provider>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"../components/Header\";\r\nimport LeftMenu from \"../components/LeftMenu\";\r\nimport { ElConfigProvider } from \"element-plus\";\r\nimport zhCn from \"element-plus/lib/locale/lang/zh-cn\";\r\n\r\nexport default {\r\n  name: \"MainLayout\",\r\n  components: {\r\n    Header,\r\n    LeftMenu,\r\n    [ElConfigProvider.name]: ElConfigProvider,\r\n  },\r\n  data() {\r\n    return {\r\n      locale: zhCn,\r\n    };\r\n  },\r\n  mounted() {\r\n\r\n\r\n\r\n\r\n  },\r\n\r\n  methods: {\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n\r\n<style scoped>\r\n@import url(../assets/libs/@mdi/font/css/materialdesignicons.min.css);\r\n@import url(../assets/libs/dripicons/webfont/webfont.css);\r\n@import url(../assets/css/app.css);\r\n</style>\r\n\r\n"], "mappings": "AA+BA,OAAOA,MAAK,MAAO,sBAAsB;AACzC,OAAOC,QAAO,MAAO,wBAAwB;AAC7C,SAASC,gBAAe,QAAS,cAAc;AAC/C,OAAOC,IAAG,MAAO,oCAAoC;AAErD,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,UAAU,EAAE;IACVL,MAAM;IACNC,QAAQ;IACR,CAACC,gBAAgB,CAACE,IAAI,GAAGF;EAC3B,CAAC;EACDI,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,MAAM,EAAEJ;IACV,CAAC;EACH,CAAC;EACDK,OAAOA,CAAA,EAAG,CAKV,CAAC;EAEDC,OAAO,EAAE,CAET;AACF,CAAC"}]}