{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\QuestionnaireStatistics.vue?vue&type=style&index=0&id=35b258be&scoped=true&lang=css", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\QuestionnaireStatistics.vue", "mtime": 1749041571658}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749040033032}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749040034646}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749040033671}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zdGF0aXN0aWNzLWNvbnRhaW5lciB7CiAgbWF4LXdpZHRoOiAxMDAwcHg7CiAgbWFyZ2luOiAwIGF1dG87CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjlmOWY5OwogIG1pbi1oZWlnaHQ6IDEwMHZoOwp9Cgouc3RhdGlzdGljcy10aXRsZSB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDMwcHg7CiAgZm9udC1zaXplOiAyNHB4OwogIGNvbG9yOiAjMzMzOwp9CgoubG9hZGluZyB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIHBhZGRpbmc6IDUwcHg7Cn0KCi5uby1kYXRhIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgcGFkZGluZzogNTBweDsKfQoKLmludHJvLXRleHQgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBtYXJnaW4tYm90dG9tOiAzMHB4OwogIHBhZGRpbmc6IDE1cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZjRmZDsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgY29sb3I6ICM2NjY7Cn0KCi5jaGFydC1jb250YWluZXIgewogIG1hcmdpbi1ib3R0b206IDQwcHg7CiAgcGFkZGluZzogMjBweDsKICBib3JkZXI6IDFweCBzb2xpZCAjZGRkOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOwogIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsMCwwLDAuMSk7Cn0KCi5xdWVzdGlvbi10aXRsZSB7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKICBjb2xvcjogIzMzMzsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgdGV4dC1hbGlnbjogbGVmdDsKICBwYWRkaW5nLWJvdHRvbTogMTBweDsKICBib3JkZXItYm90dG9tOiAycHggc29saWQgIzQwOUVGRjsKfQoKLmNoYXJ0IHsKICBtYXJnaW46IDAgYXV0bzsKfQoKLmFjdGlvbi1idXR0b25zIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgbWFyZ2luLXRvcDogMzBweDsKICBwYWRkaW5nOiAyMHB4Owp9CgouYWN0aW9uLWJ1dHRvbnMgLmVsLWJ1dHRvbiB7CiAgbWFyZ2luOiAwIDEwcHg7Cn0K"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\QuestionnaireStatistics.vue"], "names": [], "mappings": ";AA0MA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAChB", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/QuestionnaireStatistics.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"statistics-container\">\n    <h2 class=\"statistics-title\">您的问卷调查统计结果</h2>\n    \n    <div v-if=\"loading\" class=\"loading\">\n      <el-loading text=\"正在加载统计数据...\"></el-loading>\n    </div>\n    \n    <div v-else>\n      <div v-if=\"statisticsData.length === 0\" class=\"no-data\">\n        <el-empty description=\"暂无统计数据\"></el-empty>\n      </div>\n      \n      <div v-else>\n        <div class=\"intro-text\">\n          <p>感谢您参与我们的问卷调查！以下是所有用户的统计结果：</p>\n        </div>\n        \n        <div v-for=\"(question, index) in statisticsData\" :key=\"question.qid\" class=\"chart-container\">\n          <h3 class=\"question-title\">{{ index + 1 }}. {{ question.question }}</h3>\n          <div :id=\"'user-chart-' + question.qid\" class=\"chart\" :style=\"chartStyle\"></div>\n        </div>\n        \n        <div class=\"action-buttons\">\n          <el-button type=\"primary\" @click=\"goBack\">返回</el-button>\n          <el-button type=\"success\" @click=\"retakeQuestionnaire\">重新填写问卷</el-button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../utils/http\";\nimport * as echarts from \"echarts\";\n\nexport default {\n  name: 'QuestionnaireStatistics',\n  data() {\n    return {\n      loading: true,\n      statisticsData: [],\n      chartStyle: { \n        width: \"100%\", \n        height: \"350px\",\n        marginBottom: \"20px\"\n      },\n      charts: [] // 存储图表实例\n    };\n  },\n  \n  mounted() {\n    this.getStatisticsData();\n  },\n  \n  beforeDestroy() {\n    // 销毁所有图表实例\n    this.charts.forEach(chart => {\n      if (chart) {\n        chart.dispose();\n      }\n    });\n  },\n  \n  methods: {\n    // 获取统计数据\n    getStatisticsData() {\n      this.loading = true;\n      let url = base + \"/results/statistics\";\n      \n      request.post(url, {}).then((res) => {\n        if (res.code === 200 && res.resdata) {\n          this.statisticsData = res.resdata;\n          this.$nextTick(() => {\n            this.initAllCharts();\n          });\n        } else {\n          this.$message({\n            message: \"获取统计数据失败\",\n            type: \"error\",\n            offset: 320,\n          });\n        }\n        this.loading = false;\n      }).catch(error => {\n        console.error(\"获取统计数据失败:\", error);\n        this.$message({\n          message: \"获取统计数据失败\",\n          type: \"error\",\n          offset: 320,\n        });\n        this.loading = false;\n      });\n    },\n    \n    // 初始化所有图表\n    initAllCharts() {\n      this.statisticsData.forEach((question, index) => {\n        this.initChart(question, index);\n      });\n    },\n    \n    // 初始化单个图表\n    initChart(questionData, index) {\n      const chartId = 'user-chart-' + questionData.qid;\n      const chartDom = document.getElementById(chartId);\n      \n      if (!chartDom) {\n        console.error(`Chart container not found: ${chartId}`);\n        return;\n      }\n      \n      const chart = echarts.init(chartDom);\n      this.charts.push(chart);\n      \n      // 准备饼图数据\n      const pieData = questionData.options || [];\n      const legendData = pieData.map(item => item.name);\n      \n      // 定义颜色方案\n      const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];\n      \n      const option = {\n        title: {\n          text: `问题 ${index + 1} 统计结果`,\n          left: 'center',\n          top: 10,\n          textStyle: {\n            fontSize: 14,\n            fontWeight: 'bold'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c}人 ({d}%)'\n        },\n        legend: {\n          orient: 'horizontal',\n          left: 'center',\n          bottom: 10,\n          data: legendData\n        },\n        color: colors,\n        series: [\n          {\n            name: '选择人数',\n            type: 'pie',\n            radius: ['30%', '60%'],\n            center: ['50%', '45%'],\n            avoidLabelOverlap: false,\n            label: {\n              show: true,\n              position: 'outside',\n              formatter: '{b}\\n{c}人 ({d}%)',\n              fontSize: 12\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: '14',\n                fontWeight: 'bold'\n              }\n            },\n            labelLine: {\n              show: true\n            },\n            data: pieData\n          }\n        ]\n      };\n      \n      chart.setOption(option);\n      \n      // 响应式调整\n      window.addEventListener('resize', () => {\n        chart.resize();\n      });\n    },\n    \n    // 返回\n    goBack() {\n      this.$router.go(-1);\n    },\n    \n    // 重新填写问卷\n    retakeQuestionnaire() {\n      this.$confirm('确定要重新填写问卷吗？这将清除您之前的答案。', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      }).then(() => {\n        // 跳转到问卷页面\n        this.$router.push('/test');\n      }).catch(() => {\n        // 用户取消\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.statistics-container {\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 20px;\n  background-color: #f9f9f9;\n  min-height: 100vh;\n}\n\n.statistics-title {\n  text-align: center;\n  margin-bottom: 30px;\n  font-size: 24px;\n  color: #333;\n}\n\n.loading {\n  text-align: center;\n  padding: 50px;\n}\n\n.no-data {\n  text-align: center;\n  padding: 50px;\n}\n\n.intro-text {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 15px;\n  background-color: #e8f4fd;\n  border-radius: 8px;\n  color: #666;\n}\n\n.chart-container {\n  margin-bottom: 40px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  background-color: #fff;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.question-title {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n  font-weight: bold;\n  text-align: left;\n  padding-bottom: 10px;\n  border-bottom: 2px solid #409EFF;\n}\n\n.chart {\n  margin: 0 auto;\n}\n\n.action-buttons {\n  text-align: center;\n  margin-top: 30px;\n  padding: 20px;\n}\n\n.action-buttons .el-button {\n  margin: 0 10px;\n}\n</style>\n"]}]}