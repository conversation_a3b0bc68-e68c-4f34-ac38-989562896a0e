{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Menu.vue?vue&type=template&id=9bcc0be2", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Menu.vue", "mtime": 1747059578986}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgCjxkaXYgY2xhc3M9ImNvbC14cy0xMiBjb2wtc20tNCBjb2wtbWQtMyI+CjxoMyBjbGFzcz0ibGVmdF9oMyI+PHNwYW4+5oiR55qE6I+c5Y2VPC9zcGFuPjwvaDM+CjxkaXYgY2xhc3M9ImxlZnRfY29sdW1uIj4KICAgIDx1bCBjbGFzcz0ibGVmdF9uYXZfdWwiIGlkPSJmaXJzdHBhbmUiPgogICA8bGk+PGEgY2xhc3M9ImJpZ2xpbmsiIGhyZWY9Ii9Vd2VjbG9tZSI+5qyi6L+O6aG16Z2iPC9hPjwvbGk+Cgo8bGk+PGEgY2xhc3M9ImJpZ2xpbmsiIGhyZWY9Ii90aWNrZXRwdXJjaGFzZXNfbWFuYWdlIj7miJHnmoTotK3npajkv6Hmga88L2E+PC9saT4KPGxpPjxhIGNsYXNzPSJiaWdsaW5rIiBocmVmPSIvcmVzdWx0c19tYW5hZ2UiPuaIkeeahOmXruWNt+e7k+aenDwvYT48L2xpPgo8bGk+PGEgY2xhc3M9ImJpZ2xpbmsiIGhyZWY9Ii9VaW5mbyI+5L+u5pS55Liq5Lq65L+h5oGvPC9hPjwvbGk+CjxsaT48YSBjbGFzcz0iYmlnbGluayIgaHJlZj0iL1VwYXNzd29yZCI+5L+u5pS55a+G56CBPC9hPjwvbGk+CjxsaT48YSBjbGFzcz0iYmlnbGluayIgQGNsaWNrPSJxdWl0KCkiIHN0eWxlPSJjdXJzb3I6IHBvaW50ZXI7Ij7pgIDlh7rnmbvlvZU8L2E+PC9saT4KICAgIDwvdWw+CjwvZGl2PgoKPC9kaXY+Cgo="}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Menu.vue"], "names": [], "mappings": ";;AAEA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>;IACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC,CAAC;AACR,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEL,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/components/Menu.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  \n<div class=\"col-xs-12 col-sm-4 col-md-3\">\n<h3 class=\"left_h3\"><span>我的菜单</span></h3>\n<div class=\"left_column\">\n    <ul class=\"left_nav_ul\" id=\"firstpane\">\n   <li><a class=\"biglink\" href=\"/Uweclome\">欢迎页面</a></li>\n\n<li><a class=\"biglink\" href=\"/ticketpurchases_manage\">我的购票信息</a></li>\n<li><a class=\"biglink\" href=\"/results_manage\">我的问卷结果</a></li>\n<li><a class=\"biglink\" href=\"/Uinfo\">修改个人信息</a></li>\n<li><a class=\"biglink\" href=\"/Upassword\">修改密码</a></li>\n<li><a class=\"biglink\" @click=\"quit()\" style=\"cursor: pointer;\">退出登录</a></li>\n    </ul>\n</div>\n\n</div>\n\n</template>\n<script>\nexport default {\n  name: \"Menu\",\n  data() {\n    return {\n\n    };\n  },\n  mounted() {\n      //判断是否登录\n      var lname = sessionStorage.getItem(\"lname\");\n      if (lname == null) {\n        //弹出提示\n        this.$message({\n          message: \"请先登录\",\n          type: \"warning\",\n          offset: 320,\n        });\n        //跳转到登录页面\n        this.$router.push(\"/ulogin\");\n      }\n  },\n  methods: {\n    quit() {\n      var _this = this;\n      this.$confirm(\"确认退出吗?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n          .then(() => {\n            sessionStorage.removeItem(\"lname\");\n            _this.$router.push(\"/ulogin\");\n          })\n          .catch(() => { });\n    },\n\n  },\n};\n</script>\n\n<style></style>\n\n\n"]}]}