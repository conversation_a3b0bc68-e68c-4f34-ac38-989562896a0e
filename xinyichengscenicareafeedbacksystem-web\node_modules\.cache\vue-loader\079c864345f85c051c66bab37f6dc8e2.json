{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\Login.vue?vue&type=style&index=0&id=26084dc2&scoped=true&lang=css", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\Login.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749040033032}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749040034646}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749040033671}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoNCkBpbXBvcnQgdXJsKC4uL2Fzc2V0cy9jc3MvYXBwLmNzcyk7DQoNCg=="}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\Login.vue"], "names": [], "mappings": ";;AAuIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/Login.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n<body  class=\"bg-account-pages\">\r\n<section>\r\n            <div class=\"container\">\r\n                <div class=\"row\">\r\n                    <div class=\"col-12\">\r\n\r\n                        <div class=\"wrapper-page\">\r\n                            <div class=\"account-pages\">\r\n                                <div class=\"account-box\">\r\n\r\n                                    <!-- Logo box-->\r\n                                    <div class=\"account-logo-box\">\r\n                                        <h2 class=\"text-uppercase text-center\">\r\n                                            <a  class=\"text-success\">\r\n                                                <span><img src=\"images/logo_sm.png\" alt=\"\" height=\"28\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t&nbsp;&nbsp;辽宁心怡程景区意见反馈系统\r\n\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n                                            </a>\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n                                        </h2>\r\n                                    </div>\r\n\r\n                                    <div class=\"account-content\">\r\n                                          <form action=\"#\">\r\n                                            <div class=\"form-group mb-3\">\r\n                                                <label for=\"emailaddress\" class=\"font-weight-medium\">账号</label>\r\n                                                <input class=\"form-control\" type=\"text\"  required=\"\" placeholder=\"请输入账号\" v-model=\"loginModel.username\">\r\n                                            </div>\r\n\r\n                                            <div class=\"form-group mb-3\">\r\n                                              \r\n                                                <label for=\"password\" class=\"font-weight-medium\">密码</label>\r\n                                                <input class=\"form-control\" type=\"password\" required=\"\"  placeholder=\"请输入密码\" v-model=\"loginModel.password\">\r\n                                            </div>\r\n \r\n                                            <div class=\"form-group row text-center\">\r\n                                                <div class=\"col-12\">\r\n                                                    <button class=\"btn btn-block btn-success waves-effect waves-light\" type=\"submit\" @click.prevent=\"login\" >登录</button>\r\n                                                </div>\r\n                                            </div>\r\n                                        </form>\r\n\r\n\r\n\r\n<!--                                        <div class=\"row mt-3\">\r\n                                            <div class=\"col-12 text-center\">\r\n                                                <p class=\"text-muted\">没有账号? <a href=\"auth-register.html\" class=\"text-dark m-l-5\"><b>注册</b></a></p>\r\n                                            </div>\r\n                                        </div>-->\r\n                                    </div> <!-- end account-content -->\r\n\r\n                                </div> <!-- end account-box -->\r\n                            </div>\r\n                            <!-- end account-page-->\r\n                        </div>\r\n                        <!-- end wrapper-page -->\r\n\r\n                    </div> <!-- end col -->\r\n                </div> <!-- end row -->\r\n            </div> <!-- end container -->\r\n        </section>\r\n     </body>\r\n\r\n</template>\r\n\r\n<script>\nimport request, { base } from \"../../utils/http\";\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      year: new Date().getFullYear(),\n      loginModel: {\n        username: \"\",\n        password: \"\",\n        radio: \"管理员\",\n      },\n      loginModel2: {},\n     \n    };\n  },\n  mounted() {},\n  created() {\n    \n  },\n  methods: {\n    login() {\n      let that = this;  \n\n      if (that.loginModel.username == \"\") {\n        that.$message({\n          message: \"请输入账号\",\n          type: \"warning\",\n        });\n        return;\n      }\n      if (that.loginModel.password == \"\") {\n        that.$message({\n          message: \"请输入密码\",\n          type: \"warning\",\n        });\n        return;\n      }   \n      \n      this.loading = true;\n           let url = base + \"/admin/login\";\n      this.loginModel2.aname = this.loginModel.username;\n      this.loginModel2.loginpassword = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.aname);\n          sessionStorage.setItem(\"role\", \"管理员\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\r\n    \n     \n    },\n    \n    \n  },\n};\n</script>\r\n\r\n<style scoped>\r\n\r\n@import url(../assets/css/app.css);\r\n\r\n</style>\r\n\r\n\r\n"]}]}