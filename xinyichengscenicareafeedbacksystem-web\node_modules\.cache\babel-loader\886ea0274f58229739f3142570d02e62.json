{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\ticketInfo\\TicketInfoDetail.vue?vue&type=template&id=e2dba5d4", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\ticketInfo\\TicketInfoDetail.vue", "mtime": 1747229229553}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlRWxlbWVudFZOb2RlIGFzIF9jcmVhdGVFbGVtZW50Vk5vZGUsIHRvRGlzcGxheVN0cmluZyBhcyBfdG9EaXNwbGF5U3RyaW5nLCBjcmVhdGVUZXh0Vk5vZGUgYXMgX2NyZWF0ZVRleHRWTm9kZSwgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgd2l0aEN0eCBhcyBfd2l0aEN0eCwgY3JlYXRlVk5vZGUgYXMgX2NyZWF0ZVZOb2RlLCBGcmFnbWVudCBhcyBfRnJhZ21lbnQsIG9wZW5CbG9jayBhcyBfb3BlbkJsb2NrLCBjcmVhdGVFbGVtZW50QmxvY2sgYXMgX2NyZWF0ZUVsZW1lbnRCbG9jayB9IGZyb20gInZ1ZSI7CmNvbnN0IF9ob2lzdGVkXzEgPSB7CiAgY2xhc3M6ICJwYWdlLXRpdGxlLWJveCIKfTsKY29uc3QgX2hvaXN0ZWRfMiA9IHsKICBjbGFzczogImJyZWFkY3J1bWIgZmxvYXQtcmlnaHQiCn07CmNvbnN0IF9ob2lzdGVkXzMgPSAvKiNfX1BVUkVfXyovX2NyZWF0ZUVsZW1lbnRWTm9kZSgibGkiLCB7CiAgY2xhc3M6ICJicmVhZGNydW1iLWl0ZW0iCn0sIFsvKiNfX1BVUkVfXyovX2NyZWF0ZUVsZW1lbnRWTm9kZSgiYSIsIHsKICBocmVmOiAiamF2YXNjcmlwdDp2b2lkKDApOyIsCiAgaWQ6ICJ0aXRsZTEiCn0sICLnpajliqHkv6Hmga/nrqHnkIYiKV0sIC0xIC8qIEhPSVNURUQgKi8pOwpjb25zdCBfaG9pc3RlZF80ID0gewogIGNsYXNzOiAiYnJlYWRjcnVtYi1pdGVtIGFjdGl2ZSIsCiAgaWQ6ICJ0aXRsZTIiCn07CmNvbnN0IF9ob2lzdGVkXzUgPSB7CiAgY2xhc3M6ICJwYWdlLXRpdGxlIiwKICBpZDogInRpdGxlMyIKfTsKY29uc3QgX2hvaXN0ZWRfNiA9IHsKICBzdHlsZTogewogICAgIndpZHRoIjogIjEwMCUiLAogICAgImxpbmUtaGVpZ2h0IjogIjMwcHgiLAogICAgInRleHQtYWxpZ24iOiAibGVmdCIKICB9Cn07CmNvbnN0IF9ob2lzdGVkXzcgPSBbImlubmVySFRNTCJdOwpjb25zdCBfaG9pc3RlZF84ID0gLyojX19QVVJFX18qL19jcmVhdGVUZXh0Vk5vZGUoIui/lCDlm54iKTsKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICBjb25zdCBfY29tcG9uZW50X2VsX2Zvcm1faXRlbSA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1mb3JtLWl0ZW0iKTsKICBjb25zdCBfY29tcG9uZW50X2VsX2J1dHRvbiA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1idXR0b24iKTsKICBjb25zdCBfY29tcG9uZW50X2VsX2Zvcm0gPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtZm9ybSIpOwogIHJldHVybiBfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soX0ZyYWdtZW50LCBudWxsLCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoIm9sIiwgX2hvaXN0ZWRfMiwgW19ob2lzdGVkXzMsIF9jcmVhdGVFbGVtZW50Vk5vZGUoImxpIiwgX2hvaXN0ZWRfNCwgX3RvRGlzcGxheVN0cmluZyh0aGlzLiRyb3V0ZS5tZXRhLnRpdGxlKSwgMSAvKiBURVhUICovKV0pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJoNCIsIF9ob2lzdGVkXzUsIF90b0Rpc3BsYXlTdHJpbmcodGhpcy4kcm91dGUubWV0YS50aXRsZSksIDEgLyogVEVYVCAqLyldKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfNiwgW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm0sIHsKICAgIG1vZGVsOiAkZGF0YS5mb3JtRGF0YSwKICAgICJsYWJlbC13aWR0aCI6ICIyMCUiLAogICAgYWxpZ246ICJsZWZ0IgogIH0sIHsKICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgbGFiZWw6ICLnpajliqHkv6Hmga/nvJblj7ciCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVGV4dFZOb2RlKF90b0Rpc3BsYXlTdHJpbmcoJGRhdGEuZm9ybURhdGEudGlja2V0aWQpLCAxIC8qIFRFWFQgKi8pXSksCgogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICBsYWJlbDogIuelqOenjeWQjeensCIKICAgIH0sIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVUZXh0Vk5vZGUoX3RvRGlzcGxheVN0cmluZygkZGF0YS5mb3JtRGF0YS50bmFtZSksIDEgLyogVEVYVCAqLyldKSwKCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgIGxhYmVsOiAi5omA5bGe5pmv5Yy6IgogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVRleHRWTm9kZShfdG9EaXNwbGF5U3RyaW5nKCRkYXRhLmZvcm1EYXRhLmFuYW1lKSwgMSAvKiBURVhUICovKV0pLAoKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgbGFiZWw6ICLnpajku7ciCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVGV4dFZOb2RlKF90b0Rpc3BsYXlTdHJpbmcoJGRhdGEuZm9ybURhdGEucHJpY2UpLCAxIC8qIFRFWFQgKi8pXSksCgogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICBsYWJlbDogIuelqOenjeaPj+i/sCIsCiAgICAgIHByb3A6ICJkZXNjcmlwdGlvbiIKICAgIH0sIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgICAgICBpbm5lckhUTUw6ICRkYXRhLmZvcm1EYXRhLmRlc2NyaXB0aW9uCiAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIF9ob2lzdGVkXzcpXSksCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgIGxhYmVsOiAi5pyJ5pWI5pyfKOWkqSkiCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVGV4dFZOb2RlKF90b0Rpc3BsYXlTdHJpbmcoJGRhdGEuZm9ybURhdGEudmFsaWRpdHkpLCAxIC8qIFRFWFQgKi8pXSksCgogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICBsYWJlbDogIueKtuaAgSIKICAgIH0sIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVUZXh0Vk5vZGUoX3RvRGlzcGxheVN0cmluZygkZGF0YS5mb3JtRGF0YS5zdGF0dXMpLCAxIC8qIFRFWFQgKi8pXSksCgogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICBsYWJlbDogIuW6k+WtmCIKICAgIH0sIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVUZXh0Vk5vZGUoX3RvRGlzcGxheVN0cmluZygkZGF0YS5mb3JtRGF0YS5ubnVtKSwgMSAvKiBURVhUICovKV0pLAoKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgbGFiZWw6ICLlj5HluIPml7bpl7QiCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVGV4dFZOb2RlKF90b0Rpc3BsYXlTdHJpbmcoJGRhdGEuZm9ybURhdGEuYWRkdGltZSksIDEgLyogVEVYVCAqLyldKSwKCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCBudWxsLCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9idXR0b24sIHsKICAgICAgICB0eXBlOiAiaW5mbyIsCiAgICAgICAgc2l6ZTogInNtYWxsIiwKICAgICAgICBvbkNsaWNrOiAkb3B0aW9ucy5iYWNrLAogICAgICAgIGljb246ICJlbC1pY29uLWJhY2siCiAgICAgIH0sIHsKICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2hvaXN0ZWRfOF0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsib25DbGljayJdKV0pLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSldKSwKCiAgICBfOiAxIC8qIFNUQUJMRSAqLwogIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWwiXSldKV0sIDY0IC8qIFNUQUJMRV9GUkFHTUVOVCAqLyk7Cn0="}, {"version": 3, "names": ["class", "_createElementVNode", "href", "id", "style", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "$route", "meta", "title", "_hoisted_5", "_hoisted_6", "_createVNode", "_component_el_form", "model", "$data", "formData", "align", "_component_el_form_item", "label", "ticketid", "tname", "aname", "price", "prop", "innerHTML", "description", "validity", "status", "nnum", "addtime", "_component_el_button", "type", "size", "onClick", "$options", "back", "icon"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\ticketInfo\\TicketInfoDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-title-box\">\r\n    <ol class=\"breadcrumb float-right\">\r\n      <li class=\"breadcrumb-item\"><a href=\"javascript:void(0);\" id=\"title1\">票务信息管理</a></li>\r\n      <li class=\"breadcrumb-item active\" id=\"title2\">{{ this.$route.meta.title }}</li>\r\n    </ol>\r\n    <h4 class=\"page-title\" id=\"title3\">{{ this.$route.meta.title }}</h4>\r\n  </div>\r\n\r\n\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n<el-form-item label=\"票务信息编号\">\r\n{{formData.ticketid}}</el-form-item>\r\n<el-form-item label=\"票种名称\">\r\n{{formData.tname}}</el-form-item>\r\n<el-form-item label=\"所属景区\">\r\n{{formData.aname}}</el-form-item>\r\n<el-form-item label=\"票价\">\r\n{{formData.price}}</el-form-item>\r\n<el-form-item label=\"票种描述\" prop=\"description\">\r\n<div v-html=\"formData.description\"></div>\r\n</el-form-item>\r\n<el-form-item label=\"有效期(天)\">\r\n{{formData.validity}}</el-form-item>\r\n<el-form-item label=\"状态\">\r\n{{formData.status}}</el-form-item>\r\n<el-form-item label=\"库存\">\r\n{{formData.nnum}}</el-form-item>\r\n<el-form-item label=\"发布时间\">\r\n{{formData.addtime}}</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n\r\n    </div>\r\n\r\n</template>\r\n\r\n<script>\r\n        \r\n        import request, { base } from \"../../../../utils/http\";\r\n        export default {\r\n            name: 'TicketInfoDetail',\r\n            components: {\r\n            },\r\n            data() {\r\n                return {\r\n                    id: '',\r\n                    formData: {}, //表单数据         \r\n        \r\n                };\r\n            },\r\n            created() {\r\n                this.id = this.$route.query.id; //获取参数\r\n                this.getDatas();\r\n            },\r\n        \r\n        \r\n            methods: {\r\n        \r\n                //获取列表数据\r\n                getDatas() {\r\n                    let para = {\r\n                    };\r\n                    this.listLoading = true;\r\n                    let url = base + \"/ticketInfo/get?id=\" + this.id;\r\n                    request.post(url, para).then((res) => {\r\n                        this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n                        this.listLoading = false;\r\n                    });\r\n                },\r\n        \r\n                // 返回\r\n                back() {\r\n                    //返回上一页\r\n                    this.$router.go(-1);\r\n                },\r\n        \r\n            },\r\n        }\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EACrBA,KAAK,EAAC;AAAwB;gCAChCC,mBAAA,CAAqF;EAAjFD,KAAK,EAAC;AAAiB,I,aAACC,mBAAA,CAAoD;EAAjDC,IAAI,EAAC,qBAAqB;EAACC,EAAE,EAAC;GAAS,QAAM,E;;EACxEH,KAAK,EAAC,wBAAwB;EAACG,EAAE,EAAC;;;EAEpCH,KAAK,EAAC,YAAY;EAACG,EAAE,EAAC;;;EAIrBC,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;;iDAsBM,KAAG;;;;;6DA/BvEH,mBAAA,CAMM,OANNI,UAMM,GALJJ,mBAAA,CAGK,MAHLK,UAGK,GAFHC,UAAqF,EACrFN,mBAAA,CAAgF,MAAhFO,UAAgF,EAAAC,gBAAA,MAAzBC,MAAM,CAACC,IAAI,CAACC,KAAK,iB,GAE1EX,mBAAA,CAAoE,MAApEY,UAAoE,EAAAJ,gBAAA,MAAzBC,MAAM,CAACC,IAAI,CAACC,KAAK,iB,GAI5DX,mBAAA,CA4BM,OA5BNa,UA4BM,GA3BHC,YAAA,CAuBGC,kBAAA;IAvBOC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,KAAK,EAAC;;sBAC1D,MACoC,CADpCL,YAAA,CACoCM,uBAAA;MADtBC,KAAK,EAAC;IAAQ;wBAC5B,MAAqB,C,kCAAnBJ,KAAA,CAAAC,QAAQ,CAACI,QAAQ,iB;;;QACnBR,YAAA,CACiCM,uBAAA;MADnBC,KAAK,EAAC;IAAM;wBAC1B,MAAkB,C,kCAAhBJ,KAAA,CAAAC,QAAQ,CAACK,KAAK,iB;;;QAChBT,YAAA,CACiCM,uBAAA;MADnBC,KAAK,EAAC;IAAM;wBAC1B,MAAkB,C,kCAAhBJ,KAAA,CAAAC,QAAQ,CAACM,KAAK,iB;;;QAChBV,YAAA,CACiCM,uBAAA;MADnBC,KAAK,EAAC;IAAI;wBACxB,MAAkB,C,kCAAhBJ,KAAA,CAAAC,QAAQ,CAACO,KAAK,iB;;;QAChBX,YAAA,CAEeM,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACK,IAAI,EAAC;;wBAChC,MAAyC,CAAzC1B,mBAAA,CAAyC;QAApC2B,SAA6B,EAArBV,KAAA,CAAAC,QAAQ,CAACU;;;QAEtBd,YAAA,CACoCM,uBAAA;MADtBC,KAAK,EAAC;IAAQ;wBAC5B,MAAqB,C,kCAAnBJ,KAAA,CAAAC,QAAQ,CAACW,QAAQ,iB;;;QACnBf,YAAA,CACkCM,uBAAA;MADpBC,KAAK,EAAC;IAAI;wBACxB,MAAmB,C,kCAAjBJ,KAAA,CAAAC,QAAQ,CAACY,MAAM,iB;;;QACjBhB,YAAA,CACgCM,uBAAA;MADlBC,KAAK,EAAC;IAAI;wBACxB,MAAiB,C,kCAAfJ,KAAA,CAAAC,QAAQ,CAACa,IAAI,iB;;;QACfjB,YAAA,CACmCM,uBAAA;MADrBC,KAAK,EAAC;IAAM;wBAC1B,MAAoB,C,kCAAlBJ,KAAA,CAAAC,QAAQ,CAACc,OAAO,iB;;;QAClBlB,YAAA,CAEeM,uBAAA;wBADf,MAAqF,CAArFN,YAAA,CAAqFmB,oBAAA;QAA1EC,IAAI,EAAC,MAAM;QAACC,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAC,IAAI;QAAEC,IAAI,EAAC;;0BAAe,MAAG,C"}]}