{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\TicketInfoList.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\TicketInfoList.vue", "mtime": 1747063086545}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uL3V0aWxzL2h0dHAiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlRpY2tldEluZm9MaXN0IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcGFnZTogewogICAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICAgIC8vIOW9k+W<PERSON><PERSON>mhtQogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAvLyDmr4/pobXmmL7npLrmnaHnm67kuKrmlbAKICAgICAgICB0b3RhbENvdW50OiAwIC8vIOaAu+adoeebruaVsAogICAgICB9LAoKICAgICAgdGlsaXN0OiAiIgogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldERhdGFzKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDliIbpobUKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMucGFnZS5jdXJyZW50UGFnZSA9IHZhbDsKICAgICAgdGhpcy5nZXREYXRhcygpOwogICAgfSwKICAgIC8v6I635Y+W5YiX6KGo5pWw5o2uCiAgICBnZXREYXRhcygpIHsKICAgICAgbGV0IHBhcmEgPSB7CiAgICAgICAgc3RhdHVzOiAn5LiK5p62JwogICAgICB9OwogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3RpY2tldEluZm8vbGlzdD9jdXJyZW50UGFnZT0iICsgdGhpcy5wYWdlLmN1cnJlbnRQYWdlICsgIiZwYWdlU2l6ZT0iICsgdGhpcy5wYWdlLnBhZ2VTaXplOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5yZXNkYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHRoaXMuaXNQYWdlID0gdHJ1ZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5pc1BhZ2UgPSBmYWxzZTsKICAgICAgICB9CiAgICAgICAgdGhpcy5wYWdlLnRvdGFsQ291bnQgPSByZXMuY291bnQ7CiAgICAgICAgdGhpcy50aWxpc3QgPSByZXMucmVzZGF0YTsKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["request", "base", "name", "data", "page", "currentPage", "pageSize", "totalCount", "tilist", "created", "getDatas", "methods", "handleCurrentChange", "val", "para", "status", "listLoading", "url", "post", "then", "res", "resdata", "length", "isPage", "count"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\TicketInfoList.vue"], "sourcesContent": ["<template>\r\n   <div class=\"news-container1\">  \r\n  \r\n        <div class=\"news-item1\" v-for=\"item in tilist\" :key=\"item.ticketid\">  \r\n            <a :href=\"'ticketInfoView?id='+item.ticketid\">  <div class=\"news-title1\">{{item.tname}}</div>  \r\n            <div class=\"news-content1\">{{item.description.length > 70 ? item.description.substring(0,70):item.description}}</div>  \r\n            <div class=\"news-date1\">发布日期：{{item.addtime}}</div>  \r\n        </a>\r\n        </div>  \r\n  \r\n    </div> \r\n<el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\r\nbackground layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\r\nstyle=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n\r\n\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"TicketInfoList\",\r\n  data() {\r\n    return {\r\n          page: {\r\n        currentPage: 1, // 当前页\r\n        pageSize: 10, // 每页显示条目个数\r\n        totalCount: 0, // 总条目数\r\n    },\r\n    tilist: \"\",\r\n \r\n    };\r\n  },\r\n  created() {\r\n    \r\n    this.getDatas();\r\n\r\n  },\r\n  methods: {  \r\n    \r\n    // 分页\r\n    handleCurrentChange(val) {\r\n        this.page.currentPage = val;\r\n        this.getDatas();\r\n    },\r\n\r\n    //获取列表数据\r\n    getDatas() {\r\n        let para = {\r\n                status:'上架',\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/ticketInfo/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\r\n        request.post(url, para).then((res) => {\r\n            if (res.resdata.length > 0) {\r\n                this.isPage = true;\r\n            } else {\r\n                this.isPage = false;\r\n            }\r\n            this.page.totalCount = res.count;\r\n            this.tilist = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n .news-container1 {  \r\n        max-width: 100%;  \r\n        margin: 0 auto;  \r\n    }  \r\n    .news-item1 {  \r\n        background-color: white;  \r\n        margin-bottom: 20px;  \r\n        padding: 15px;  \r\n     \r\n        box-shadow: 0 2px 5px rgba(0,0,0,0.1);  \r\n        transition: transform 0.3s ease;  \r\n        border-bottom: 1px solid #c3c0c0;\r\n    }  \r\n    .news-item1:hover {  \r\n        transform: translateY(-5px);  \r\n    }  \r\n\r\n    .news-title1 {  \r\n        font-size: 15px;  \r\n        font-weight: bold;  \r\n        margin-bottom: 10px;  \r\n    }  \r\n    .news-item1 a {  \r\n        color: #333;  \r\n        text-decoration: none;  \r\n    }  \r\n    .news-item1 a:hover {  \r\n        color: #007bff;  \r\n        text-decoration: underline;  \r\n    }  \r\n    .news-content1 {  \r\n        font-size: 14px;  \r\n        color: #333;  \r\n        margin-bottom: 10px;  \r\n    }  \r\n    .news-date1 {  \r\n        font-size: 12px;  \r\n        color: red;  \r\n    }  \r\n</style>\r\n\r\n\r\n\r\n"], "mappings": "AAkBA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AACnD,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACDC,IAAI,EAAE;QACRC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACnB,CAAC;;MACDC,MAAM,EAAE;IAER,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IAER,IAAI,CAACC,QAAQ,CAAC,CAAC;EAEjB,CAAC;EACDC,OAAO,EAAE;IAEP;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACrB,IAAI,CAACT,IAAI,CAACC,WAAU,GAAIQ,GAAG;MAC3B,IAAI,CAACH,QAAQ,CAAC,CAAC;IACnB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACP,IAAII,IAAG,GAAI;QACHC,MAAM,EAAC;MACf,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIhB,IAAG,GAAI,+BAA8B,GAAI,IAAI,CAACG,IAAI,CAACC,WAAW,GAAE,YAAW,GAAI,IAAI,CAACD,IAAI,CAACE,QAAQ;MAC3GN,OAAO,CAACkB,IAAI,CAACD,GAAG,EAAEH,IAAI,CAAC,CAACK,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAIA,GAAG,CAACC,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UACxB,IAAI,CAACC,MAAK,GAAI,IAAI;QACtB,OAAO;UACH,IAAI,CAACA,MAAK,GAAI,KAAK;QACvB;QACA,IAAI,CAACnB,IAAI,CAACG,UAAS,GAAIa,GAAG,CAACI,KAAK;QAChC,IAAI,CAAChB,MAAK,GAAIY,GAAG,CAACC,OAAO;QACzB,IAAI,CAACL,WAAU,GAAI,KAAK;MAC5B,CAAC,CAAC;IACN;EAEF;AACF,CAAC"}]}