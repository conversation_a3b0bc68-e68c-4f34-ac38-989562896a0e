{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Leftnav.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Leftnav.vue", "mtime": 1747058734245}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAiLi4vLi4vLi4vdXRpbHMvaHR0cCI7DQppbXBvcnQgTGVmdCBmcm9tICIuLi8uLi9jb21wb25lbnRzL0xlZnQiOw0KaW1wb3J0IFRvcE1lbnUgZnJvbSAiLi4vLi4vY29tcG9uZW50cy9Ub3BNZW51IjsNCmltcG9ydCBGb290IGZyb20gIi4uLy4uL2NvbXBvbmVudHMvRm9vdCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgICBuYW1lOiAiTGVmdG5hdiIsDQogICAgY29tcG9uZW50czogew0KICAgICAgICBMZWZ0LA0KICAgICAgICBUb3BNZW51LA0KICAgICAgICBGb290LA0KICAgIH0sDQogICAgZGF0YSgpIHsNCiAgICAgICAgcmV0dXJuIHsNCg0KICAgICAgICB9Ow0KICAgIH0sDQogICAgbW91bnRlZCgpIHsNCg0KICAgIH0sDQogICAgY3JlYXRlZCgpIHsNCg0KICAgIH0sDQogICAgbWV0aG9kczogew0KDQogICAgfSwNCn07DQo="}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Leftnav.vue"], "names": [], "mappings": ";AAsCA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAExC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC;IACR,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;QAEP,CAAC;IACL,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEV,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEV,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAET,CAAC;AACL,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/Leftnav.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <TopMenu />\r\n    \r\n\r\n    <div class=\"page_bg\" :style=\"{ background: `url(${require('@/assets/images/inban.jpg')}) center top no-repeat` }\"></div>\r\n    <div class=\"bread_bg\">\r\n    \t<div class=\"container\">    \r\n    \t\t<div class=\"row\">\r\n    \t\t\t<div class=\"col-xs-12 col-sm-12 col-md-12\">\r\n    \t\t\t\t<div class=\"bread_nav\">\r\n    \t\t\t\t\t<span>您的位置：</span><a href=\"/index\">首页</a> &gt; <a href=\"\">{{ $route.meta.title }}</a>\r\n    \t\t\t\t</div>\r\n    \t\t\t</div>\r\n    \t\t</div>\r\n    \t</div>\r\n    </div>\r\n    <div class=\"container\">    \r\n    \t<div class=\"row\">\r\n    \t\t  <Left />\r\n    \t\t<!-- right -->\r\n    \t\t<div class=\"col-xs-12 col-sm-8 col-md-9\" style=\"float:right\">\r\n    \t\t\t<div class=\"right_head\">\r\n    \t\t\t\t<h2><span> {{ $route.meta.title }}</span></h2>\r\n    \t\t\t</div>\r\n    \t\t\t<div class=\"right_contents reimg\">\r\n\t\t\t\t  <router-view />\r\n\t\t\t\t    </div> \r\n    \t\t</div>\r\n    \t\t<!-- left -->\r\n    \t\t\r\n    \t</div>\r\n    </div>\r\n\r\n\r\n\r\n    <Foot />\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nimport Left from \"../../components/Left\";\r\nimport TopMenu from \"../../components/TopMenu\";\r\nimport Foot from \"../../components/Foot\";\r\n\r\nexport default {\r\n    name: \"Leftnav\",\r\n    components: {\r\n        Left,\r\n        TopMenu,\r\n        Foot,\r\n    },\r\n    data() {\r\n        return {\r\n\r\n        };\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    created() {\r\n\r\n    },\r\n    methods: {\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n\r\n<style></style>\r\n \r\n\r\n"]}]}