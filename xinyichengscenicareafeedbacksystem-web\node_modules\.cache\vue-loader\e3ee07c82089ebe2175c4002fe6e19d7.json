{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\TicketInfoView.vue?vue&type=style&index=0&id=219cc674&lang=css", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\TicketInfoView.vue", "mtime": 1747229663877}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749040033032}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749040034646}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749040033671}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoudGlja2V0LWNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDIwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQoudGlja2V0LWNhcmQgew0KICBtYXgtd2lkdGg6IDgwMHB4Ow0KICB3aWR0aDogMTAwJTsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCAxMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBwYWRkaW5nOiAzMHB4Ow0KfQ0KDQoudGlja2V0LXRpdGxlIHsNCiAgZm9udC1zaXplOiAyNHB4Ow0KICBjb2xvcjogIzMwMzEzMzsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBtYXJnaW4tdG9wOiAwOw0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQoudGlja2V0LW1ldGEgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBmb250LXNpemU6IDE0cHg7DQogIG1hcmdpbi1ib3R0b206IDMwcHg7DQp9DQoNCi50aWNrZXQtaW5mbyB7DQogIGJhY2tncm91bmQ6ICNmOGY4Zjg7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgcGFkZGluZzogMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMzBweDsNCn0NCg0KLmluZm8tdGFibGUgew0KICB3aWR0aDogMTAwJTsNCiAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsNCn0NCg0KLmluZm8tdGFibGUgdHIgew0KICBsaW5lLWhlaWdodDogMi41Ow0KfQ0KDQouaW5mby1sYWJlbCB7DQogIHdpZHRoOiAxMDBweDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICB0ZXh0LWFsaWduOiBsZWZ0Ow0KfQ0KDQouaW5mby12YWx1ZSB7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQoucHJpY2UtdmFsdWUgew0KICBjb2xvcjogI2Y1NmM2YzsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg0KLnRpY2tldC1kZXNjcmlwdGlvbiBoMyB7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzQwOUVGRjsNCiAgcGFkZGluZy1sZWZ0OiAxMHB4Ow0KfQ0KDQouZGVzY3JpcHRpb24tY29udGVudCB7DQogIGxpbmUtaGVpZ2h0OiAxLjg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICB0ZXh0LWluZGVudDogMmVtOw0KfQ0KDQoudGlja2V0LWFjdGlvbiB7DQogIG1hcmdpbi10b3A6IDMwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLnRpY2tldC1hY3Rpb24gLmVsLWJ1dHRvbiB7DQogIHBhZGRpbmc6IDEycHggMzBweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0K"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\TicketInfoView.vue"], "names": [], "mappings": ";AAmFA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/TicketInfoView.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n   <div class=\"ticket-container\">\r\n      <div class=\"ticket-card\">\r\n         <h1 class=\"ticket-title\">{{tilist.tname}}</h1>\r\n         <div class=\"ticket-meta\">发布时间：{{tilist.addtime}}</div>\r\n         \r\n         <div class=\"ticket-info\">\r\n            <table class=\"info-table\" v-if=\"tilist\">\r\n               <tr v-if=\"tilist.aname\">\r\n                  <td class=\"info-label\">所属景区：</td>\r\n                  <td class=\"info-value\">{{tilist.aname}}</td>\r\n               </tr>\r\n               <tr v-if=\"tilist.price\">\r\n                  <td class=\"info-label\">票价：</td>\r\n                  <td class=\"info-value price-value\">¥{{tilist.price}}</td>\r\n               </tr>\r\n               <tr v-if=\"tilist.validity\">\r\n                  <td class=\"info-label\">有效期：</td>\r\n                  <td class=\"info-value\">{{tilist.validity}}天</td>\r\n               </tr>\r\n               <tr v-if=\"tilist.status\">\r\n                  <td class=\"info-label\">状态：</td>\r\n                  <td class=\"info-value\">{{tilist.status}}</td>\r\n               </tr>\r\n               <tr v-if=\"tilist.nnum\">\r\n                  <td class=\"info-label\">库存：</td>\r\n                  <td class=\"info-value\">{{tilist.nnum}}</td>\r\n               </tr>\r\n            </table>\r\n         </div>\r\n         \r\n         <div class=\"ticket-description\">\r\n            <h3>票种描述</h3>\r\n            <div class=\"description-content\" v-html=\"tilist.description\"></div>\r\n         </div>\r\n         \r\n         <div class=\"ticket-action\">\r\n            <el-button type=\"primary\" v-if=\"tilist.status=='上架'\" @click=\"goToPurchase\">立即购票</el-button>\r\n            <el-button type=\"primary\" v-else>已售罄</el-button>\r\n         </div>\r\n      </div>\r\n   </div>\r\n</template>\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\nexport default {\r\n  name: \"TicketInfoView\",\r\n  data() {\r\n    return {\r\n          tilist: \"\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getDatas();\r\n  },\r\n  methods: {  \r\n    //获取列表数据\r\n    getDatas() {\r\n        let id = this.$route.query.id;\r\n        let para = {           \r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/ticketInfo/get?id=\" + id ;\r\n        request.post(url, para).then((res) => {\r\n            this.tilist = res.resdata;\r\n        });\r\n    },    \r\n\r\n    // 跳转到购票页面\r\n    goToPurchase() {\r\n        this.$router.push({\r\n            path: '/ticketpurchases_add',\r\n            query: {\r\n                id: this.tilist.ticketid,\r\n                price: this.tilist.price\r\n            }\r\n        });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.ticket-container {\r\n  padding: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.ticket-card {\r\n  max-width: 800px;\r\n  width: 100%;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 30px;\r\n}\r\n\r\n.ticket-title {\r\n  font-size: 24px;\r\n  color: #303133;\r\n  text-align: center;\r\n  margin-top: 0;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.ticket-meta {\r\n  text-align: center;\r\n  color: #909399;\r\n  font-size: 14px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.ticket-info {\r\n  background: #f8f8f8;\r\n  border-radius: 6px;\r\n  padding: 20px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.info-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n}\r\n\r\n.info-table tr {\r\n  line-height: 2.5;\r\n}\r\n\r\n.info-label {\r\n  width: 100px;\r\n  color: #606266;\r\n  font-weight: bold;\r\n  text-align: left;\r\n}\r\n\r\n.info-value {\r\n  color: #303133;\r\n}\r\n\r\n.price-value {\r\n  color: #f56c6c;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.ticket-description h3 {\r\n  font-size: 18px;\r\n  color: #303133;\r\n  margin-bottom: 15px;\r\n  border-left: 4px solid #409EFF;\r\n  padding-left: 10px;\r\n}\r\n\r\n.description-content {\r\n  line-height: 1.8;\r\n  color: #606266;\r\n  text-indent: 2em;\r\n}\r\n\r\n.ticket-action {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.ticket-action .el-button {\r\n  padding: 12px 30px;\r\n  font-size: 16px;\r\n}\r\n</style>\r\n\r\n\r\n\r\n"]}]}