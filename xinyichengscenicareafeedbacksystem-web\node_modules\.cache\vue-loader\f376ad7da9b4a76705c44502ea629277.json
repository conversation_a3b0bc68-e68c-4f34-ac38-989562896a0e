{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionnaireStatistics.vue?vue&type=template&id=438aee5a&scoped=true", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionnaireStatistics.vue", "mtime": 1749040548672}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0icXVlc3Rpb25uYWlyZS1zdGF0aXN0aWNzIj4KICAgIDxkaXYgY2xhc3M9InBhZ2UtaGVhZGVyIj4KICAgICAgPGgyPumXruWNt+iwg+afpee7n+iuoTwvaDI+CiAgICA8L2Rpdj4KICAgIAogICAgPGRpdiB2LWxvYWRpbmc9ImxvYWRpbmciIGNsYXNzPSJzdGF0aXN0aWNzLWNvbnRlbnQiPgogICAgICA8ZGl2IHYtaWY9InN0YXRpc3RpY3NEYXRhLmxlbmd0aCA+IDAiIGNsYXNzPSJjaGFydC1jb250YWluZXIiPgogICAgICAgIDxkaXYgaWQ9InN0YXRpc3RpY3NDaGFydCIgc3R5bGU9IndpZHRoOiAxMDAlOyBoZWlnaHQ6IDUwMHB4OyI+PC9kaXY+CiAgICAgIDwvZGl2PgogICAgICAKICAgICAgPGRpdiB2LWVsc2UtaWY9IiFsb2FkaW5nIiBjbGFzcz0ibm8tZGF0YSI+CiAgICAgICAgPHA+5pqC5peg6Zeu5Y2357uf6K6h5pWw5o2uPC9wPgogICAgICA8L2Rpdj4KICAgICAgCiAgICAgIDwhLS0g57uf6K6h6KGo5qC8IC0tPgogICAgICA8ZGl2IHYtaWY9InN0YXRpc3RpY3NEYXRhLmxlbmd0aCA+IDAiIGNsYXNzPSJzdGF0aXN0aWNzLXRhYmxlIiBzdHlsZT0ibWFyZ2luLXRvcDogMzBweDsiPgogICAgICAgIDxoMz7or6bnu4bnu5/orqHmlbDmja48L2gzPgogICAgICAgIDxlbC10YWJsZSA6ZGF0YT0ic3RhdGlzdGljc0RhdGEiIGJvcmRlciBzdHJpcGUgc3R5bGU9IndpZHRoOiAxMDAlIj4KICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0ibmFtZSIgbGFiZWw9Iua7oeaEj+W6piIgYWxpZ249ImNlbnRlciI+PC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9InZhbHVlIiBsYWJlbD0i5pWw6YePIiBhbGlnbj0iY2VudGVyIj48L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuWNoOavlCIgYWxpZ249ImNlbnRlciI+CiAgICAgICAgICAgIDx0ZW1wbGF0ZSAjZGVmYXVsdD0ic2NvcGUiPgogICAgICAgICAgICAgIHt7IGdldFBlcmNlbnRhZ2Uoc2NvcGUucm93LnZhbHVlKSB9fSUKICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgIDwvZWwtdGFibGU+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionnaireStatistics.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/admin/questions/QuestionnaireStatistics.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"questionnaire-statistics\">\n    <div class=\"page-header\">\n      <h2>问卷调查统计</h2>\n    </div>\n    \n    <div v-loading=\"loading\" class=\"statistics-content\">\n      <div v-if=\"statisticsData.length > 0\" class=\"chart-container\">\n        <div id=\"statisticsChart\" style=\"width: 100%; height: 500px;\"></div>\n      </div>\n      \n      <div v-else-if=\"!loading\" class=\"no-data\">\n        <p>暂无问卷统计数据</p>\n      </div>\n      \n      <!-- 统计表格 -->\n      <div v-if=\"statisticsData.length > 0\" class=\"statistics-table\" style=\"margin-top: 30px;\">\n        <h3>详细统计数据</h3>\n        <el-table :data=\"statisticsData\" border stripe style=\"width: 100%\">\n          <el-table-column prop=\"name\" label=\"满意度\" align=\"center\"></el-table-column>\n          <el-table-column prop=\"value\" label=\"数量\" align=\"center\"></el-table-column>\n          <el-table-column label=\"占比\" align=\"center\">\n            <template #default=\"scope\">\n              {{ getPercentage(scope.row.value) }}%\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nimport * as echarts from 'echarts';\n\nexport default {\n  name: \"QuestionnaireStatistics\",\n  data() {\n    return {\n      loading: false,\n      statisticsData: [],\n      totalCount: 0\n    };\n  },\n  created() {\n    this.getStatistics();\n  },\n  methods: {\n    // 获取统计数据\n    getStatistics() {\n      this.loading = true;\n      let url = base + \"/results/statistics\";\n      request.post(url).then((res) => {\n        this.loading = false;\n        if (res.resdata && res.resdata.length > 0) {\n          this.statisticsData = res.resdata;\n          this.totalCount = this.statisticsData.reduce((sum, item) => sum + item.value, 0);\n          this.$nextTick(() => {\n            this.renderChart();\n          });\n        }\n      }).catch(error => {\n        this.loading = false;\n        console.error(\"获取统计数据失败:\", error);\n        this.$message({\n          message: \"获取统计数据失败\",\n          type: \"error\",\n          offset: 320,\n        });\n      });\n    },\n\n    // 渲染饼图\n    renderChart() {\n      const chartDom = document.getElementById('statisticsChart');\n      if (!chartDom) return;\n      \n      const myChart = echarts.init(chartDom);\n      const option = {\n        title: {\n          text: '问卷调查满意度统计',\n          left: 'center',\n          textStyle: {\n            fontSize: 18,\n            fontWeight: 'bold'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 'left',\n          top: 'middle'\n        },\n        series: [\n          {\n            name: '满意度统计',\n            type: 'pie',\n            radius: ['40%', '70%'],\n            center: ['60%', '50%'],\n            data: this.statisticsData,\n            emphasis: {\n              itemStyle: {\n                shadowBlur: 10,\n                shadowOffsetX: 0,\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\n              }\n            },\n            label: {\n              show: true,\n              formatter: '{b}: {c}\\n({d}%)'\n            }\n          }\n        ],\n        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4']\n      };\n      myChart.setOption(option);\n      \n      // 响应式处理\n      window.addEventListener('resize', () => {\n        myChart.resize();\n      });\n    },\n\n    // 计算百分比\n    getPercentage(value) {\n      if (this.totalCount === 0) return 0;\n      return ((value / this.totalCount) * 100).toFixed(1);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.questionnaire-statistics {\n  padding: 20px;\n}\n\n.page-header {\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #eee;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.statistics-content {\n  background: #fff;\n  padding: 20px;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.chart-container {\n  text-align: center;\n}\n\n.no-data {\n  text-align: center;\n  padding: 50px;\n  color: #999;\n  font-size: 16px;\n}\n\n.statistics-table {\n  margin-top: 30px;\n}\n\n.statistics-table h3 {\n  margin-bottom: 15px;\n  color: #333;\n}\n</style>\n"]}]}