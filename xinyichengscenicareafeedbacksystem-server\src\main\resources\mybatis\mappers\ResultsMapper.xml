<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.ResultsMapper">
	<select id="findResultsList"  resultType="Results">
		select * from results 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Results">
	    select  *  
        from results a  left join users b on a.account=b.account  	
		<where>
      		<if test="id != null and id !=0 ">
		    and a.id = #{id}
		</if>
		<if test="qid != null and qid !=0 ">
		    and a.qid = #{qid}
		</if>
		<if test="question != null and question != ''">
		    and a.question = #{question}
		</if>
		<if test="status != null and status != ''">
		    and a.status = #{status}
		</if>
		<if test="account != null and account != ''">
		    and a.account = #{account}
		</if>
		<if test="addtime != null and addtime != ''">
		    and a.addtime = #{addtime}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} id desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from results a  left join users b on a.account=b.account  
		<where>
      		<if test="id != null and id !=0 ">
		    and a.id = #{id}
		</if>
		<if test="qid != null and qid !=0 ">
		    and a.qid = #{qid}
		</if>
		<if test="question != null and question != ''">
		    and a.question = #{question}
		</if>
		<if test="status != null and status != ''">
		    and a.status = #{status}
		</if>
		<if test="account != null and account != ''">
		    and a.account = #{account}
		</if>
		<if test="addtime != null and addtime != ''">
		    and a.addtime = #{addtime}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryResultsById" parameterType="int" resultType="Results">
    select  *  
     from results a  left join users b on a.account=b.account  	 where a.id=#{value}
  </select>
 
	<insert id="insertResults" useGeneratedKeys="true" keyProperty="id" parameterType="Results">
    insert into results
    (qid,question,status,account,addtime)
    values
    (#{qid},#{question},#{status},#{account},now());
  </insert>
	
	<update id="updateResults" parameterType="Results" >
    update results 
    <set>
		<if test="qid != null ">
		    qid = #{qid},
		</if>
		<if test="question != null and question != ''">
		    question = #{question},
		</if>
		<if test="status != null and status != ''">
		    status = #{status},
		</if>
		<if test="account != null and account != ''">
		    account = #{account},
		</if>
		<if test="addtime != null and addtime != ''">
		    addtime = #{addtime},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="id != null or id != ''">
      id=#{id}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteResults" parameterType="int">
    delete from  results where id=#{value}
  </delete>

	<!-- 获取问题统计数据 -->
	<select id="getQuestionStatistics" resultType="java.util.Map">
		SELECT DISTINCT qid, question
		FROM results
		ORDER BY qid
	</select>

	<!-- 获取选项统计数据 -->
	<select id="getOptionStatistics" parameterType="int" resultType="java.util.Map">
		SELECT status as name, COUNT(*) as value
		FROM results
		WHERE qid = #{qid}
		GROUP BY status
	</select>

	<!-- 获取用户已完成问卷数量 -->
	<select id="getUserCompletedCount" parameterType="String" resultType="int">
		SELECT COUNT(DISTINCT qid)
		FROM results
		WHERE account = #{account}
	</select>

	<!-- 获取问卷总题目数 -->
	<select id="getTotalQuestionCount" resultType="int">
		SELECT COUNT(*)
		FROM questions
	</select>

</mapper>

 
