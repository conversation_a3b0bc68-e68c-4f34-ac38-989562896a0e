{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Left.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Left.vue", "mtime": 1747058115006}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAiLi4vLi4vdXRpbHMvaHR0cCI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJMZWZ0IiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgIGxpc3QxOiIiLA0KICAgIGxpc3QyOiIiLA0KDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmdldGxpc3QxKCk7DQogICAgdGhpcy5nZXRsaXN0MigpOw0KDQogIH0sDQogIG1ldGhvZHM6IHsNCg0KICAgIC8vIOiOt+WPluaWsOmXu+i1hOiurw0KICAgIGdldGxpc3QxKCkgew0KICAgICAgICBsZXQgcGFyYSA9IHt9Ow0KICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL25ld3MvbGlzdD9jdXJyZW50UGFnZT0xJnBhZ2VTaXplPTYiOw0KICAgICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKChyZXMpID0+IHsgICAgICAgICANCiAgICAgICAgICAgIHRoaXMubGlzdDEgPSByZXMucmVzZGF0YTsNCiAgICAgICAgICAgIHRoaXMubGlzdExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfSk7DQogICAgfSwgICAgDQoNCiAgICAvLyDojrflj5bnpajliqHkv6Hmga8NCiAgICBnZXRsaXN0MigpIHsNCiAgICAgICAgbGV0IHBhcmEgPSB7fTsNCiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7DQogICAgICAgIGxldCB1cmwgPSBiYXNlICsgIi90aWNrZXRJbmZvL2xpc3Q/Y3VycmVudFBhZ2U9MSZwYWdlU2l6ZT02IjsNCiAgICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbigocmVzKSA9PiB7ICAgICAgICAgDQogICAgICAgICAgICB0aGlzLmxpc3QyID0gcmVzLnJlc2RhdGE7DQogICAgICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pOw0KICAgIH0sICAgIA0KDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Left.vue"], "names": [], "mappings": ";AAkCA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>;;<PERSON><PERSON><PERSON>,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC;IACN,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC;IACN,CAAC;;EAEH,CAAC;AACH,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/components/Left.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  \r\n\r\n<div class=\"col-xs-12 col-sm-4 col-md-3\">\r\n       <div class=\"left_contact\">\r\n    <h3 class=\"left_h3\" style=\"margin-bottom:20px;\"><span>新闻资讯</span></h3>\r\n     <ul class=\"product-list\">\r\n\r\n        <li class=\"product-item\"  v-for=\"(item,index) in list1\" :key=\"index\" >\r\n            <a :href=\"'newsView?id='+item.id\">\r\n                <div class=\"product-image-wrapper\">\r\n                    <img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' +item.nimage\" class=\"product-image\" style=\"width: 230px;height: 140px;\" />\r\n                </div>\r\n                <div class=\"product-info\">\r\n                    <span class=\"product-name\">{{item.title}}</span>\r\n              \r\n                </div>\r\n            </a>\r\n        </li>       \r\n\r\n    </ul>\r\n</div>\r\n<div class=\"left_news\">\r\n    <h3 class=\"left_h3\"><span>票务信息</span></h3>\r\n    <ul class=\"left_news\">\r\n        <li  v-for=\"(item,index) in list2\" :key=\"index\" ><a :href=\"'ticketInfoView?id='+item.ticketid\">{{item.tname}}</a></li>       \r\n\r\n    </ul>\r\n</div>\r\n\r\n </div>\r\n \r\n</template>\r\n<script>\r\nimport request, { base } from \"../../utils/http\";\r\nexport default {\r\n  name: \"Left\",\r\n  data() {\r\n    return {\r\n    list1:\"\",\r\n    list2:\"\",\r\n\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getlist1();\r\n    this.getlist2();\r\n\r\n  },\r\n  methods: {\r\n\r\n    // 获取新闻资讯\r\n    getlist1() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/news/list?currentPage=1&pageSize=6\";\r\n        request.post(url, para).then((res) => {         \r\n            this.list1 = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n    // 获取票务信息\r\n    getlist2() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/ticketInfo/list?currentPage=1&pageSize=6\";\r\n        request.post(url, para).then((res) => {         \r\n            this.list2 = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n  },\r\n};\r\n</script>\r\n\r\n\r\n\r\n\r\n"]}]}