{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\users\\UsersEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\users\\UsersEdit.vue", "mtime": 1747055628000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "id", "isClear", "uploadVisible", "btnLoading", "formData", "add<PERSON><PERSON>", "account", "required", "message", "trigger", "password", "uname", "gender", "phone", "pattern", "email", "address", "avatar", "created", "$route", "query", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "save", "$refs", "validate", "valid", "code", "$message", "type", "offset", "$router", "push", "path", "msg", "goBack", "showUpload", "hideUpload", "handleRemove", "file", "fileList", "handlePreview", "console", "log", "handleExceed", "files", "duration", "fileListChange", "extendFileName", "extendFileNames", "split", "regExpRules", "i", "length", "RegExp", "fileNames", "that", "for<PERSON>ach", "key", "val", "ret", "test", "indexOf", "multiFiles", "handleConfirm", "filePath", "FormData", "append", "raw", "furl"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\users\\UsersEdit.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-title-box\">\r\n    <ol class=\"breadcrumb float-right\">\r\n      <li class=\"breadcrumb-item\"><a href=\"javascript:void(0);\" id=\"title1\">用户管理</a></li>\r\n      <li class=\"breadcrumb-item active\" id=\"title2\">{{ this.$route.meta.title }}</li>\r\n    </ol>\r\n    <h4 class=\"page-title\" id=\"title3\">{{ this.$route.meta.title }}</h4>\r\n  </div>\r\n\r\n\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"账号\" prop=\"account\">\r\n<el-input v-model=\"formData.account\" placeholder=\"账号\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"密码\" prop=\"password\">\r\n<el-input v-model=\"formData.password\" placeholder=\"密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"姓名\" prop=\"uname\">\r\n<el-input v-model=\"formData.uname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"性别\" prop=\"gender\">\r\n<el-radio-group v-model=\"formData.gender\">\r\n<el-radio label=\"男\">\r\n男\r\n</el-radio>\r\n<el-radio label=\"女\">\r\n女\r\n</el-radio>\r\n</el-radio-group>\r\n</el-form-item>\r\n<el-form-item label=\"年龄\" prop=\"age\">\r\n<el-input v-model=\"formData.age\" placeholder=\"年龄\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"手机号码\" prop=\"phone\">\r\n<el-input v-model=\"formData.phone\" placeholder=\"手机号码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"电子邮箱\" prop=\"email\">\r\n<el-input v-model=\"formData.email\" placeholder=\"电子邮箱\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系地址\" prop=\"address\">\r\n<el-input v-model=\"formData.address\" placeholder=\"联系地址\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item prop=\"avatar\" label=\"个人头像\"  min-width=\"20%\">\r\n<el-input  v-model=\"formData.avatar\" placeholder=\"个人头像\"  readonly=\"true\" style=\"width:50%;\" ></el-input>\r\n<el-button type=\"primary\" size=\"small\" @click=\"showUpload\">上传</el-button>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n     <el-dialog\n        v-model=\"uploadVisible\"\n        title=\"附件上传\"\n        custom-class=\"el-dialog-widthSmall\"\n        @close=\"closeDialog\"\n      >\n        <div>\n          <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>\n        </div>\n        <el-upload\n          action=\"http://localhost:8088/gouwu/api/common/uploadFile\"\n          style=\"\n            margin: auto;\n            margin-top: 10px;\n            border: 1px solid #dcdfe6;\n            border-radius: 4px;\n          \"\n          drag\n          :limit=\"1\"\n          :on-preview=\"handlePreview\"\n          :on-remove=\"handleRemove\"\n          :file-list=\"fileList\"\n          :on-exceed=\"handleExceed\"\n          :auto-upload=\"false\"\n          name=\"file\"\n          :on-change=\"fileListChange\"\n        >\n          <i class=\"el-icon-upload\"></i>\n          <div class=\"el-upload__text\">\n            将数据文件拖到此处，或<em>点击上传</em>\n          </div>\n          <div class=\"el-upload__tip\">\n            <div\n              style=\"display: inline; color: #d70000; font-size: 14px\"\n              class=\"uploadFileWarning\"\n              id=\"uploadFileWarning\"\n            ></div>\n          </div>\n        </el-upload>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"hideUpload\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"handleConfirm\">提 交</el-button>\n        </span>\n      </el-dialog>\r\n\r\n\r\n\r\n    </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'UsersEdit',\n  components: {\n    \n  },  \n    data() {\n      return {   \n        id: '',\n        isClear: false,\n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        addrules: {\r\n          account: [{ required: true, message: '请输入账号', trigger: 'blur' },\r\n],          password: [{ required: true, message: '请输入密码', trigger: 'blur' },\r\n],          uname: [{ required: true, message: '请输入姓名', trigger: 'blur' },\r\n],          gender: [{ required: true, message: '请输入性别', trigger: 'blur' },\r\n],          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },\r\n        { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },\r\n],          email: [{ required: true, message: '请输入电子邮箱', trigger: 'blur' },\r\n        { pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/, message: '电子邮箱格式不正确', trigger: 'blur' },\r\n],          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },\r\n],          avatar: [{ required: true, message: '请输入个人头像', trigger: 'blur' },\r\n],        },\r\n\n      };\n    },\n    created() {\r\n    this.id = this.$route.query.id;\r\n      this.getDatas();\r\n    },\r\n\r\n \n    methods: {    \n\n//获取列表数据\n        getDatas() {\n          let para = {\n          };\n          this.listLoading = true;\n          let url = base + \"/users/get?id=\" + this.id;\n          request.post(url, para).then((res) => {\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\n            this.listLoading = false;\n            \n            \n          });\n        },\n    \n        // 添加\n        save() {\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n            if (valid) {\n              let url = base + \"/users/update\";\n              this.btnLoading = true;\n              \n              request.post(url, this.formData).then((res) => { //发送请求         \n                if (res.code == 200) {\n                  this.$message({\n                    message: \"操作成功\",\n                    type: \"success\",\n                    offset: 320,\n                  });\n                  this.$router.push({\n                    path: \"/UsersManage\",\n                  });\n                } else {\n                  this.$message({\n                    message:res.msg,\n                    type: \"error\",\n                    offset: 320,\n                  });\n                }\n                this.btnLoading = false;\n              });\n            }\n    \n          });\n        },\n        \n       // 返回\n        goBack() {\n          this.$router.push({\n            path: \"/UsersManage\",\n          });\n        },       \n              \n          \n            //显示上传框\n    showUpload() {\n      this.uploadVisible = true;\n    },\n\n    //隐藏上传框\n    hideUpload() {\n      this.uploadVisible = false;\n    },\n    //上传\n    handleRemove(file, fileList) {\n      this.fileList = fileList;\n    },\n    handlePreview(file) {\n      console.log(file);\n    },\n    handleExceed(files, fileList) {\n      this.$message({\n        duration: 1000,\n        message: \"只能上传一个文件\",\n        type: \"error\",\n        offset: 320,\n      });\n    },\n    // 判断上传文件后缀\n    fileListChange(file, fileList) {\n      let extendFileName = \"png,jpg\";\n      let extendFileNames = extendFileName.split(\",\");\n      let regExpRules = [];\n      for (let i = 0; i < extendFileNames.length; i++) {\n        regExpRules.push(\n          new RegExp(\"(.*).(\" + extendFileNames[i] + \")$\", \"gim\")\n        );\n      }\n      let fileNames = [];\n      let files = [];\n      let that = this;\n      fileList.forEach(function (key, val) {\n        let ret = false;\n        for (let i = 0; i < regExpRules.length; i++) {\n          ret = ret || regExpRules[i].test(key[\"name\"]);\n        }\n        if (!ret) {\n          console.log(key[\"name\"] + \":\" + ret);\n          that.$message({\n            duration: 1000,\n            message: \"上传的文件后缀必须为\" + extendFileName + \"格式！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        if (fileNames.indexOf(key[\"name\"]) != -1) {\n          that.$message({\n            duration: 1000,\n            message: \"上传的文件重复！\",\n            type: \"error\",\n            offset: 320,\n          });\n          return false;\n        }\n        //只能上传一个文件，用最后上传的覆盖\n        if (!that.multiFiles) {\n          files = [];\n          fileNames = [];\n        }\n        files.push(key);\n        fileNames.push(key[\"name\"]);\n        if (fileNames !== \"\") {\n          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');\n        }\n        //$(\".uploadFileWarning\").text(\"\");\n      });\n      this.files = fileNames;\n      this.fileList = files;\n    },\n    /**\n     * 确认按钮\n     */\n    handleConfirm() {\n      let filePath = this.fileList;\n      if (filePath.length === 0) {\n        this.$message({\n          duration: 1000,\n          message: \"请选择文件！\",\n          type: \"error\",\n          offset: 320,\n        });\n        return false;\n      }\n      let formData = new FormData();\n      this.fileList.forEach((file) => {\n        formData.append(\"file\", file.raw, file.raw.name);\n      });\n      let url = base + \"/common/uploadFile\";\n      console.log(\"url=\" + url);\n      request.post(url, formData).then((res) => {\n        console.log(res);\n        let furl = res.resdata.filePath;\n        this.formData.avatar = furl;  // 上传文件的路径\n        this.hideUpload();\n        console.log(res);\n      });\n    },\n\r\n   \n           \n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";AAwGA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AAEtD,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,QAAQ,EAAE;QACRC,OAAO,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CACxE;QAAWC,QAAQ,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC3E;QAAWE,KAAK,EAAE,CAAC;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CACxE;QAAWG,MAAM,EAAE,CAAC;UAAEL,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CACzE;QAAWI,KAAK,EAAE,CAAC;UAAEN,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EACnE;UAAEK,OAAO,EAAE,mBAAmB;UAAEN,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC9E;QAAWM,KAAK,EAAE,CAAC;UAAER,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EACnE;UAAEK,OAAO,EAAE,qDAAqD;UAAEN,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC,CAChH;QAAWO,OAAO,EAAE,CAAC;UAAET,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC5E;QAAWQ,MAAM,EAAE,CAAC;UAAEV,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MAClE;IAEJ,CAAC;EACH,CAAC;EACDS,OAAOA,CAAA,EAAG;IACV,IAAI,CAAClB,EAAC,GAAI,IAAI,CAACmB,MAAM,CAACC,KAAK,CAACpB,EAAE;IAC5B,IAAI,CAACqB,QAAQ,CAAC,CAAC;EACjB,CAAC;EAGDC,OAAO,EAAE;IAEb;IACQD,QAAQA,CAAA,EAAG;MACT,IAAIE,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAI7B,IAAG,GAAI,gBAAe,GAAI,IAAI,CAACI,EAAE;MAC3CL,OAAO,CAAC+B,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACxB,QAAO,GAAIyB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;MAG1B,CAAC,CAAC;IACJ,CAAC;IAED;IACAS,IAAIA,CAAA,EAAG;MACL,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAAE;QAC9C,IAAIA,KAAK,EAAE;UACT,IAAIX,GAAE,GAAI7B,IAAG,GAAI,eAAe;UAChC,IAAI,CAACO,UAAS,GAAI,IAAI;UAEtBR,OAAO,CAAC+B,IAAI,CAACD,GAAG,EAAE,IAAI,CAACrB,QAAQ,CAAC,CAACuB,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACS,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZ9B,OAAO,EAAE,MAAM;gBACf+B,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;gBAChBC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACL,QAAQ,CAAC;gBACZ9B,OAAO,EAACoB,GAAG,CAACgB,GAAG;gBACfL,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAACrC,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MAEF,CAAC,CAAC;IACJ,CAAC;IAEF;IACC0C,MAAMA,CAAA,EAAG;MACP,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAGG;IACRG,UAAUA,CAAA,EAAG;MACX,IAAI,CAAC5C,aAAY,GAAI,IAAI;IAC3B,CAAC;IAED;IACA6C,UAAUA,CAAA,EAAG;MACX,IAAI,CAAC7C,aAAY,GAAI,KAAK;IAC5B,CAAC;IACD;IACA8C,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MAC3B,IAAI,CAACA,QAAO,GAAIA,QAAQ;IAC1B,CAAC;IACDC,aAAaA,CAACF,IAAI,EAAE;MAClBG,OAAO,CAACC,GAAG,CAACJ,IAAI,CAAC;IACnB,CAAC;IACDK,YAAYA,CAACC,KAAK,EAAEL,QAAQ,EAAE;MAC5B,IAAI,CAACZ,QAAQ,CAAC;QACZkB,QAAQ,EAAE,IAAI;QACdhD,OAAO,EAAE,UAAU;QACnB+B,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IACD;IACAiB,cAAcA,CAACR,IAAI,EAAEC,QAAQ,EAAE;MAC7B,IAAIQ,cAAa,GAAI,SAAS;MAC9B,IAAIC,eAAc,GAAID,cAAc,CAACE,KAAK,CAAC,GAAG,CAAC;MAC/C,IAAIC,WAAU,GAAI,EAAE;MACpB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIH,eAAe,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/CD,WAAW,CAACnB,IAAI,CACd,IAAIsB,MAAM,CAAC,QAAO,GAAIL,eAAe,CAACG,CAAC,IAAI,IAAI,EAAE,KAAK,CACxD,CAAC;MACH;MACA,IAAIG,SAAQ,GAAI,EAAE;MAClB,IAAIV,KAAI,GAAI,EAAE;MACd,IAAIW,IAAG,GAAI,IAAI;MACfhB,QAAQ,CAACiB,OAAO,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;QACnC,IAAIC,GAAE,GAAI,KAAK;QACf,KAAK,IAAIR,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,WAAW,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3CQ,GAAE,GAAIA,GAAE,IAAKT,WAAW,CAACC,CAAC,CAAC,CAACS,IAAI,CAACH,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C;QACA,IAAI,CAACE,GAAG,EAAE;UACRlB,OAAO,CAACC,GAAG,CAACe,GAAG,CAAC,MAAM,IAAI,GAAE,GAAIE,GAAG,CAAC;UACpCJ,IAAI,CAAC5B,QAAQ,CAAC;YACZkB,QAAQ,EAAE,IAAI;YACdhD,OAAO,EAAE,YAAW,GAAIkD,cAAa,GAAI,KAAK;YAC9CnB,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA,IAAIyB,SAAS,CAACO,OAAO,CAACJ,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;UACxCF,IAAI,CAAC5B,QAAQ,CAAC;YACZkB,QAAQ,EAAE,IAAI;YACdhD,OAAO,EAAE,UAAU;YACnB+B,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;UACF,OAAO,KAAK;QACd;QACA;QACA,IAAI,CAAC0B,IAAI,CAACO,UAAU,EAAE;UACpBlB,KAAI,GAAI,EAAE;UACVU,SAAQ,GAAI,EAAE;QAChB;QACAV,KAAK,CAACb,IAAI,CAAC0B,GAAG,CAAC;QACfH,SAAS,CAACvB,IAAI,CAAC0B,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAIH,SAAQ,KAAM,EAAE,EAAE;UACpB;QAAA;QAEF;MACF,CAAC,CAAC;;MACF,IAAI,CAACV,KAAI,GAAIU,SAAS;MACtB,IAAI,CAACf,QAAO,GAAIK,KAAK;IACvB,CAAC;IACD;;;IAGAmB,aAAaA,CAAA,EAAG;MACd,IAAIC,QAAO,GAAI,IAAI,CAACzB,QAAQ;MAC5B,IAAIyB,QAAQ,CAACZ,MAAK,KAAM,CAAC,EAAE;QACzB,IAAI,CAACzB,QAAQ,CAAC;UACZkB,QAAQ,EAAE,IAAI;UACdhD,OAAO,EAAE,QAAQ;UACjB+B,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;QACF,OAAO,KAAK;MACd;MACA,IAAIpC,QAAO,GAAI,IAAIwE,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAC1B,QAAQ,CAACiB,OAAO,CAAElB,IAAI,IAAK;QAC9B7C,QAAQ,CAACyE,MAAM,CAAC,MAAM,EAAE5B,IAAI,CAAC6B,GAAG,EAAE7B,IAAI,CAAC6B,GAAG,CAACjF,IAAI,CAAC;MAClD,CAAC,CAAC;MACF,IAAI4B,GAAE,GAAI7B,IAAG,GAAI,oBAAoB;MACrCwD,OAAO,CAACC,GAAG,CAAC,MAAK,GAAI5B,GAAG,CAAC;MACzB9B,OAAO,CAAC+B,IAAI,CAACD,GAAG,EAAErB,QAAQ,CAAC,CAACuB,IAAI,CAAEC,GAAG,IAAK;QACxCwB,OAAO,CAACC,GAAG,CAACzB,GAAG,CAAC;QAChB,IAAImD,IAAG,GAAInD,GAAG,CAACI,OAAO,CAAC2C,QAAQ;QAC/B,IAAI,CAACvE,QAAQ,CAACa,MAAK,GAAI8D,IAAI,EAAG;QAC9B,IAAI,CAAChC,UAAU,CAAC,CAAC;QACjBK,OAAO,CAACC,GAAG,CAACzB,GAAG,CAAC;MAClB,CAAC,CAAC;IACJ;EAIE;AACN"}]}