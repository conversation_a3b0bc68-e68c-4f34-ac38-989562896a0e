{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionnaireStatistics.vue?vue&type=template&id=438aee5a&scoped=true", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionnaireStatistics.vue", "mtime": 1749040548672}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "id", "style", "_hoisted_5", "_hoisted_8", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "$data", "statisticsData", "length", "_hoisted_4", "_hoisted_6", "loading", "_hoisted_7", "_hoisted_9", "_createCommentVNode", "_hoisted_10", "_hoisted_11", "_createVNode", "_component_el_table", "data", "border", "stripe", "_component_el_table_column", "prop", "label", "align", "default", "_withCtx", "scope", "$options", "getPercentage", "row", "value"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\admin\\questions\\QuestionnaireStatistics.vue"], "sourcesContent": ["<template>\n  <div class=\"questionnaire-statistics\">\n    <div class=\"page-header\">\n      <h2>问卷调查统计</h2>\n    </div>\n    \n    <div v-loading=\"loading\" class=\"statistics-content\">\n      <div v-if=\"statisticsData.length > 0\" class=\"chart-container\">\n        <div id=\"statisticsChart\" style=\"width: 100%; height: 500px;\"></div>\n      </div>\n      \n      <div v-else-if=\"!loading\" class=\"no-data\">\n        <p>暂无问卷统计数据</p>\n      </div>\n      \n      <!-- 统计表格 -->\n      <div v-if=\"statisticsData.length > 0\" class=\"statistics-table\" style=\"margin-top: 30px;\">\n        <h3>详细统计数据</h3>\n        <el-table :data=\"statisticsData\" border stripe style=\"width: 100%\">\n          <el-table-column prop=\"name\" label=\"满意度\" align=\"center\"></el-table-column>\n          <el-table-column prop=\"value\" label=\"数量\" align=\"center\"></el-table-column>\n          <el-table-column label=\"占比\" align=\"center\">\n            <template #default=\"scope\">\n              {{ getPercentage(scope.row.value) }}%\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nimport * as echarts from 'echarts';\n\nexport default {\n  name: \"QuestionnaireStatistics\",\n  data() {\n    return {\n      loading: false,\n      statisticsData: [],\n      totalCount: 0\n    };\n  },\n  created() {\n    this.getStatistics();\n  },\n  methods: {\n    // 获取统计数据\n    getStatistics() {\n      this.loading = true;\n      let url = base + \"/results/statistics\";\n      request.post(url).then((res) => {\n        this.loading = false;\n        if (res.resdata && res.resdata.length > 0) {\n          this.statisticsData = res.resdata;\n          this.totalCount = this.statisticsData.reduce((sum, item) => sum + item.value, 0);\n          this.$nextTick(() => {\n            this.renderChart();\n          });\n        }\n      }).catch(error => {\n        this.loading = false;\n        console.error(\"获取统计数据失败:\", error);\n        this.$message({\n          message: \"获取统计数据失败\",\n          type: \"error\",\n          offset: 320,\n        });\n      });\n    },\n\n    // 渲染饼图\n    renderChart() {\n      const chartDom = document.getElementById('statisticsChart');\n      if (!chartDom) return;\n      \n      const myChart = echarts.init(chartDom);\n      const option = {\n        title: {\n          text: '问卷调查满意度统计',\n          left: 'center',\n          textStyle: {\n            fontSize: 18,\n            fontWeight: 'bold'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 'left',\n          top: 'middle'\n        },\n        series: [\n          {\n            name: '满意度统计',\n            type: 'pie',\n            radius: ['40%', '70%'],\n            center: ['60%', '50%'],\n            data: this.statisticsData,\n            emphasis: {\n              itemStyle: {\n                shadowBlur: 10,\n                shadowOffsetX: 0,\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\n              }\n            },\n            label: {\n              show: true,\n              formatter: '{b}: {c}\\n({d}%)'\n            }\n          }\n        ],\n        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4']\n      };\n      myChart.setOption(option);\n      \n      // 响应式处理\n      window.addEventListener('resize', () => {\n        myChart.resize();\n      });\n    },\n\n    // 计算百分比\n    getPercentage(value) {\n      if (this.totalCount === 0) return 0;\n      return ((value / this.totalCount) * 100).toFixed(1);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.questionnaire-statistics {\n  padding: 20px;\n}\n\n.page-header {\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #eee;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.statistics-content {\n  background: #fff;\n  padding: 20px;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.chart-container {\n  text-align: center;\n}\n\n.no-data {\n  text-align: center;\n  padding: 50px;\n  color: #999;\n  font-size: 16px;\n}\n\n.statistics-table {\n  margin-top: 30px;\n}\n\n.statistics-table h3 {\n  margin-bottom: 15px;\n  color: #333;\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAA0B;gEACnCC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAa,I,aACtBC,mBAAA,CAAe,YAAX,QAAM,E;;EAGaD,KAAK,EAAC;AAAoB;;;EACXA,KAAK,EAAC;;gEAC1CC,mBAAA,CAAoE;EAA/DC,EAAE,EAAC,iBAAiB;EAACC,KAAmC,EAAnC;IAAA;IAAA;EAAA;;oBAA1BC,UAAoE,C;;;EAG5CJ,KAAK,EAAC;;gEAC9BC,mBAAA,CAAe,WAAZ,UAAQ;oBAAXI,UAAe,C;;;EAIqBL,KAAK,EAAC,kBAAkB;EAACG,KAAyB,EAAzB;IAAA;EAAA;;iEAC7DF,mBAAA,CAAe,YAAX,QAAM;;;;;;uBAhBhBK,mBAAA,CA4BM,OA5BNC,UA4BM,GA3BJC,UAEM,E,gBAENP,mBAAA,CAsBM,OAtBNQ,UAsBM,GArBOC,KAAA,CAAAC,cAAc,CAACC,MAAM,Q,cAAhCN,mBAAA,CAEM,OAFNO,UAEM,EAAAC,UAAA,K,CAEWJ,KAAA,CAAAK,OAAO,I,cAAxBT,mBAAA,CAEM,OAFNU,UAEM,EAAAC,UAAA,K,mCAENC,mBAAA,UAAa,EACFR,KAAA,CAAAC,cAAc,CAACC,MAAM,Q,cAAhCN,mBAAA,CAWM,OAXNa,WAWM,GAVJC,WAAe,EACfC,YAAA,CAQWC,mBAAA;IARAC,IAAI,EAAEb,KAAA,CAAAC,cAAc;IAAEa,MAAM,EAAN,EAAM;IAACC,MAAM,EAAN,EAAM;IAACtB,KAAmB,EAAnB;MAAA;IAAA;;sBAC7C,MAA0E,CAA1EkB,YAAA,CAA0EK,0BAAA;MAAzDC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC,KAAK;MAACC,KAAK,EAAC;QAC/CR,YAAA,CAA0EK,0BAAA;MAAzDC,IAAI,EAAC,OAAO;MAACC,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;QAC/CR,YAAA,CAIkBK,0BAAA;MAJDE,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;;MACrBC,OAAO,EAAAC,QAAA,CAAEC,KAAK,K,kCACpBC,QAAA,CAAAC,aAAa,CAACF,KAAK,CAACG,GAAG,CAACC,KAAK,KAAI,IACtC,gB;;;;;;oHAlBQ1B,KAAA,CAAAK,OAAO,E"}]}