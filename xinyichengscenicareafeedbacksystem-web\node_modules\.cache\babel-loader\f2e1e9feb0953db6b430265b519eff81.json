{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Header.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Header.vue", "mtime": 1747056915467}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["$", "data", "activeIndex", "activeIndex2", "showexist", "userLname", "role", "mounted", "sessionStorage", "getItem", "$router", "push", "methods", "handleSelect", "key", "keyP<PERSON>", "console", "log", "toggleShowExist", "removeClass", "addClass", "exit", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "removeItem", "catch", "toggleFullScreen", "elem", "document", "documentElement", "requestFullscreen", "mozRequestFullScreen", "webkitRequestFullscreen", "msRequestFullscreen"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Header.vue"], "sourcesContent": ["<template>\r\n  <header id=\"topnav\">\r\n            <nav class=\"navbar-custom\">\r\n                <ul class=\"list-unstyled topbar-right-menu float-right mb-0\">\r\n                  \r\n                    <li class=\"dropdown notification-list\">\r\n                        <a class=\"nav-link dropdown-toggle nav-user\" data-toggle=\"dropdown\" href=\"#\" role=\"button\"\r\n                            aria-haspopup=\"false\" aria-expanded=\"false\" @click=\"toggleShowExist\">\r\n                            <img src=\"../assets/images/users/avatar-1.jpg\" alt=\"user\" class=\"rounded-circle\"> <span class=\"ml-1\">\r\n                                【<b style=\"color: red;\">{{role}}</b>】{{userLname}}<i class=\"mdi mdi-chevron-down\"></i> </span>\r\n                        </a>\r\n                        <div class=\"dropdown-menu dropdown-menu-right dropdown-menu-animated profile-dropdown \" v-show=\"showexist\">\r\n                            <!-- item-->\r\n                            <div class=\"dropdown-item noti-title\">\r\n                                <h6 class=\"text-overflow m-0\">Welcome !</h6>\r\n                            </div>\r\n\r\n<!--\r\n                           \r\n                            <a href=\"/\" class=\"dropdown-item notify-item\" target=\"_blank\">\r\n                                <i class=\"dripicons-home\"></i> <span>网站首页</span>\r\n                            </a>\r\n-->\r\n\r\n\r\n                            <!-- item-->\r\n                          \r\n                              <router-link to=\"/Password\" class=\"dropdown-item notify-item\">\r\n                              <i class=\"dripicons-lock\"></i> <span>修改密码</span>\r\n                            </router-link>\r\n\r\n                            <!-- item-->\r\n                            <a href=\"javascript:void(0);\" class=\"dropdown-item notify-item\" @click=\"exit\">\r\n                                <i class=\"dripicons-power\"></i> <span>退出登录</span>\r\n                            </a>\r\n\r\n                        </div>\r\n                    </li>\r\n                   \r\n                </ul>\r\n\r\n                <ul class=\"list-unstyled menu-left mb-0\">\r\n                    <li class=\"float-left\">\r\n                        <a href=\"/main\" class=\"logo\">\r\n                            <span class=\"logo-lg\" style=\"font-size: 18px;color: #fff;\">\r\n                                <img src=\"../assets/images/logo_sm.png\" alt=\"\" height=\"30\">\r\n                                心怡程景区意见反馈系统\r\n                            </span>\r\n                            <span class=\"logo-sm\">\r\n                                <img src=\"../assets/images/logo_sm.png\" alt=\"\" height=\"28\">\r\n                            </span>\r\n                        </a>\r\n                    </li>\r\n               \r\n                 \r\n                </ul>\r\n            </nav>\r\n            <!-- end navbar-custom -->\r\n        </header>      \r\n\r\n</template>\r\n<script>\r\nimport $ from \"jquery\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      activeIndex: \"1\",\r\n      activeIndex2: \"1\",\r\n      showexist: false,\r\n      userLname: \"\",\r\n      role: \"\",\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");  \r\n\r\n    //判断是否登录\r\n    if(this.userLname == null){     \r\n       this.$router.push(\"/login\");\r\n    }\r\n\r\n  },\r\n  methods: {\r\n    handleSelect(key, keyPath) {\r\n      console.log(key, keyPath);\r\n    },\r\n    toggleShowExist() {    \r\n      this.showexist = !this.showexist;\r\n\r\n      if(this.showexist){\r\n        $(\".dropdown-menu\").removeClass(\"show\");\r\n      }else{\r\n        $(\".dropdown-menu\").addClass(\"show\");\r\n      }\r\n\r\n   \r\n\r\n    },\r\n\r\n    exit: function() {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem(\"userLname\");\r\n          sessionStorage.removeItem(\"role\");\r\n          _this.$router.push(\"/login\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    toggleFullScreen() {\r\n      const elem = document.documentElement;\r\n      if (elem.requestFullscreen) {\r\n        elem.requestFullscreen();\r\n      } else if (elem.mozRequestFullScreen) {\r\n        elem.mozRequestFullScreen();\r\n      } else if (elem.webkitRequestFullscreen) {\r\n        elem.webkitRequestFullscreen();\r\n      } else if (elem.msRequestFullscreen) {\r\n        elem.msRequestFullscreen();\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n"], "mappings": ";AA8DA,OAAOA,CAAA,MAAO,QAAQ;AACtB,eAAe;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE,GAAG;MAChBC,YAAY,EAAE,GAAG;MACjBC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE;IACR,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACF,SAAQ,GAAIG,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAI,CAACH,IAAG,GAAIE,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;;IAE1C;IACA,IAAG,IAAI,CAACJ,SAAQ,IAAK,IAAI,EAAC;MACvB,IAAI,CAACK,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;IAC9B;EAEF,CAAC;EACDC,OAAO,EAAE;IACPC,YAAYA,CAACC,GAAG,EAAEC,OAAO,EAAE;MACzBC,OAAO,CAACC,GAAG,CAACH,GAAG,EAAEC,OAAO,CAAC;IAC3B,CAAC;IACDG,eAAeA,CAAA,EAAG;MAChB,IAAI,CAACd,SAAQ,GAAI,CAAC,IAAI,CAACA,SAAS;MAEhC,IAAG,IAAI,CAACA,SAAS,EAAC;QAChBJ,CAAC,CAAC,gBAAgB,CAAC,CAACmB,WAAW,CAAC,MAAM,CAAC;MACzC,CAAC,MAAI;QACHnB,CAAC,CAAC,gBAAgB,CAAC,CAACoB,QAAQ,CAAC,MAAM,CAAC;MACtC;IAIF,CAAC;IAEDC,IAAI,EAAE,SAAAA,CAAA,EAAW;MACf,IAAIC,KAAI,GAAI,IAAI;MAChB,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC5BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACVnB,cAAc,CAACoB,UAAU,CAAC,WAAW,CAAC;QACtCpB,cAAc,CAACoB,UAAU,CAAC,MAAM,CAAC;QACjCN,KAAK,CAACZ,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;MAC9B,CAAC,EACAkB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC;IACDC,gBAAgBA,CAAA,EAAG;MACjB,MAAMC,IAAG,GAAIC,QAAQ,CAACC,eAAe;MACrC,IAAIF,IAAI,CAACG,iBAAiB,EAAE;QAC1BH,IAAI,CAACG,iBAAiB,CAAC,CAAC;MAC1B,OAAO,IAAIH,IAAI,CAACI,oBAAoB,EAAE;QACpCJ,IAAI,CAACI,oBAAoB,CAAC,CAAC;MAC7B,OAAO,IAAIJ,IAAI,CAACK,uBAAuB,EAAE;QACvCL,IAAI,CAACK,uBAAuB,CAAC,CAAC;MAChC,OAAO,IAAIL,IAAI,CAACM,mBAAmB,EAAE;QACnCN,IAAI,CAACM,mBAAmB,CAAC,CAAC;MAC5B;IACF;EACF;AACF,CAAC"}]}