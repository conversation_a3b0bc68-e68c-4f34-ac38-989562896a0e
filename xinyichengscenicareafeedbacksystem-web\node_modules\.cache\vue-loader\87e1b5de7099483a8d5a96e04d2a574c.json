{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Board.vue?vue&type=template&id=14f98bbe", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Board.vue", "mtime": 1748665016744}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\views\\web\\Board.vue"], "names": [], "mappings": ";;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzH,CAAC,CAAC,CAAC;QACC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACpE,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1E,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC;oBACC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/G,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC;gBACJ,CAAC,CAAC,CAAC;oBACC,CAAC,CAAC,CAAC;wBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;4BAGX,CAAC,CAAC,CAAC;4BACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;oBAG3F,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC;;YAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEX,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC;AACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC;oBACC,CAAC,CAAC,CAAC;wBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5F,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/modify/B7818/B7818xinyichengscenicareafeedbacksystem/xinyichengscenicareafeedbacksystem-web/src/views/web/Board.vue", "sourceRoot": "", "sourcesContent": ["<template>\n   \n<table style=\"width:100%;line-height:32px; font-size:13px;border: 1px solid #C2D5E3; \"  v-for=\"item in bolist\" :key=\"item.id\">\n    <tr>\n        <td width=\"100\" align=\"center\" style=\"border-right: 1px solid #C2D5E3;\" valign=\"top\">\n            <div v-if=\"item.isniming=='否'\">\n                <img style=\"width: 80px;height: 80px;border-radius: 35px\" \n :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' + item.by1\" />\n            </div>\n            <div v-else>\n                <img style=\"width: 80px;height: 80px;border-radius: 35px\" \n src=\"http://localhost:8088/xinyichengscenicareafeedbacksystem/upload/anonymous.png\" />\n            </div>\n\n            <span class=\"text-red\" v-if=\"item.isniming=='否'\"> {{item.account}}</span>\n            <span class=\"text-red\" v-else>匿名用户</span>\n        </td>\n        <td valign=\"top\">\n            <table width=\"100%\">\n                <tr>\n                    <td style=\"    border-bottom: 1px dashed #e3e3e3;\">\n                        <b>反馈标题：{{item.title}}</b>\n                        <span v-if=\"item.catname\" style=\"margin-left: 10px;\">[分类：{{item.catname}}]</span>\n                        <span style=\"float: right\"><i class=\"glyphicon glyphicon-time text-cyan\"></i>   反馈时间：{{item.addtime}}</span>\n                    </td>\n                </tr>\n                <tr>\n                    <td>\n                        {{item.content}}\n\n                      \n                            <br>\n                            <b style=\"color: red\"   v-if=\"item.adminreply != null\">管理员回复：{{item.adminreply}}</b>\n                       \n\n                    </td>\n                </tr>\n\n            </table>\n\n        </td>\n    </tr>\n</table>\n\n    <table style=\"width: 100%;\">\n                <tr>\n                    <td>\n                        <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\"\n                            :page-size=\"page.pageSize\" background layout=\"total, prev, pager, next, jumper\"\n                            :total=\"page.totalCount\" style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n                    </td>\n                </tr>\n            </table>\n\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\n        <el-form-item label=\"反馈标题\" prop=\"title\">\n        <el-input v-model=\"formData.title\" placeholder=\"反馈标题\" style=\"width:50%;\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"意见类别\" prop=\"catid\">\n        <el-select v-model=\"formData.catid\" placeholder=\"请选择意见类别\" style=\"width:50%;\">\n            <el-option v-for=\"item in categoryList\" :key=\"item.catid\" :label=\"item.catname\" :value=\"item.catid\"></el-option>\n        </el-select>\n    </el-form-item>\n    <el-form-item label=\"反馈内容\" prop=\"content\">\n        <el-input type=\"textarea\" :rows=\"5\" v-model=\"formData.content\" placeholder=\"反馈内容\"\n        size=\"small\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"是否匿名\">\n        <el-switch v-model=\"formData.isniming\" active-text=\"是\" inactive-text=\"否\"></el-switch>\n    </el-form-item>\n    <el-form-item>\n        <el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\"    icon=\"el-icon-upload\" >发 表</el-button>\n    </el-form-item>\n    </el-form>\n\n\n</template>\n<script>\nimport request, { base } from \"../../../utils/http\";\nexport default {\n  name: \"Board\",\n  data() {\n    return {\n          page: {\n        currentPage: 1, // 当前页\n        pageSize: 10, // 每页显示条目个数\n        totalCount: 0, // 总条目数\n    },\n    bolist: \"\",\n    categoryList: [], // 意见类别列表\n    formData: {\n      isniming: false\n    },\n    addrules: {\n        title: [\n            { required: true, message: \"请输入留言标题\", trigger: \"blur\" },\n        ],\n        catid: [\n            { required: true, message: \"请选择意见类别\", trigger: \"change\" },\n        ],\n        content: [\n          { required: true, message: \"请输入留言内容\", trigger: \"blur\" },\n        ],\n    },\n    btnLoading: false,\n \n    };\n  },\n  created() {\n    \n    this.getDatas();\n    this.getCategoryList(); // 获取分类列表\n\n  },\n  methods: {  \n    \n    // 分页\n    handleCurrentChange(val) {\n        this.page.currentPage = val;\n        this.getDatas();\n    },\n\n    //获取列表数据\n    getDatas() {\n        let para = {\n            status: \"审核通过\"\n        };\n        this.listLoading = true;\n        let url = base + \"/board/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\n        request.post(url, para).then((res) => {\n            if (res.resdata.length > 0) {\n                this.isPage = true;\n            } else {\n                this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.bolist = res.resdata;\n            this.listLoading = false;\n        });\n    },\n\n    // 获取分类列表\n    getCategoryList() {\n        let para = {};\n        let url = base + \"/category/list?currentPage=1&pageSize=1000\";\n        request.post(url, para).then((res) => {\n            this.categoryList = res.resdata;\n        });\n    },\n\n    //提交留言\n    save() {\n        //如果没有ID就新增数据\n        this.$refs[\"formDataRef\"].validate((valid) => {\n\n            if (valid) {\n\n                var lname = sessionStorage.getItem(\"lname\"); //获取登录名\n                if (lname == null) {\n                    this.$message({\n                        message: \"请先登录\",\n                        type: \"error\",\n                        offset: 320,\n                    });\n                    return;\n                }\n\n                let url = base + \"/board/add\"; //提交地址\n                this.btnLoading = true; //打开按钮加载状态\n                this.formData.account = lname; //留言人\n                this.formData.status = \"待审核\";\n                request.post(url, this.formData).then((res) => { //提交数据\n\n                    if (res.code == 200) {\n\n                        this.formData={}; //清空表单\n                        this.btnLoading = false; //关闭按钮加载状态\n                        this.getDatas(); //刷新列表\n\n                        this.$message({\n                            message: \"恭喜您，留言成功，请等待管理员的回复！\",\n                            type: \"success\",\n                            offset: 320,\n                        });\n\n                    }\n                    else {\n                        this.$message({\n                            message: \"服务器错误\",\n                            type: \"error\",\n                            offset: 320,\n                        });\n                        this.btnLoading = false;\n                    }\n                });\n            }\n        });\n    },\n    \n  },\n};\n</script>\n\n<style>\n\n</style>\n\n\n\n"]}]}