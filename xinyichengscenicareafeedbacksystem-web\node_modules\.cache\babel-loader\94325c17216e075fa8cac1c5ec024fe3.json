{"remainingRequest": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Left.vue?vue&type=template&id=3c83f0b7", "dependencies": [{"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Left.vue", "mtime": 1747058115006}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\babel.config.js", "mtime": 1747055624000}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749040033680}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749040034697}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749040032596}, {"path": "I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749040033934}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "style", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_Fragment", "_renderList", "$data", "list1", "item", "index", "key", "href", "id", "_hoisted_6", "src", "nimage", "_hoisted_8", "_hoisted_9", "_toDisplayString", "title", "_hoisted_10", "_hoisted_11", "_hoisted_12", "list2", "ticketid", "tname", "_hoisted_13"], "sources": ["I:\\modify\\B7818\\B7818xinyichengscenicareafeedbacksystem\\xinyichengscenicareafeedbacksystem-web\\src\\components\\Left.vue"], "sourcesContent": ["<template>\r\n  \r\n\r\n<div class=\"col-xs-12 col-sm-4 col-md-3\">\r\n       <div class=\"left_contact\">\r\n    <h3 class=\"left_h3\" style=\"margin-bottom:20px;\"><span>新闻资讯</span></h3>\r\n     <ul class=\"product-list\">\r\n\r\n        <li class=\"product-item\"  v-for=\"(item,index) in list1\" :key=\"index\" >\r\n            <a :href=\"'newsView?id='+item.id\">\r\n                <div class=\"product-image-wrapper\">\r\n                    <img :src=\"'http://localhost:8088/xinyichengscenicareafeedbacksystem/' +item.nimage\" class=\"product-image\" style=\"width: 230px;height: 140px;\" />\r\n                </div>\r\n                <div class=\"product-info\">\r\n                    <span class=\"product-name\">{{item.title}}</span>\r\n              \r\n                </div>\r\n            </a>\r\n        </li>       \r\n\r\n    </ul>\r\n</div>\r\n<div class=\"left_news\">\r\n    <h3 class=\"left_h3\"><span>票务信息</span></h3>\r\n    <ul class=\"left_news\">\r\n        <li  v-for=\"(item,index) in list2\" :key=\"index\" ><a :href=\"'ticketInfoView?id='+item.ticketid\">{{item.tname}}</a></li>       \r\n\r\n    </ul>\r\n</div>\r\n\r\n </div>\r\n \r\n</template>\r\n<script>\r\nimport request, { base } from \"../../utils/http\";\r\nexport default {\r\n  name: \"Left\",\r\n  data() {\r\n    return {\r\n    list1:\"\",\r\n    list2:\"\",\r\n\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getlist1();\r\n    this.getlist2();\r\n\r\n  },\r\n  methods: {\r\n\r\n    // 获取新闻资讯\r\n    getlist1() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/news/list?currentPage=1&pageSize=6\";\r\n        request.post(url, para).then((res) => {         \r\n            this.list1 = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n    // 获取票务信息\r\n    getlist2() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/ticketInfo/list?currentPage=1&pageSize=6\";\r\n        request.post(url, para).then((res) => {         \r\n            this.list2 = res.resdata;\r\n            this.listLoading = false;\r\n        });\r\n    },    \r\n\r\n  },\r\n};\r\n</script>\r\n\r\n\r\n\r\n\r\n"], "mappings": ";;EAGKA,KAAK,EAAC;AAA6B;;EAC5BA,KAAK,EAAC;AAAc;gCAC5BC,mBAAA,CAAsE;EAAlED,KAAK,EAAC,SAAS;EAACE,KAA2B,EAA3B;IAAA;EAAA;iBAA4BD,mBAAA,CAAiB,cAAX,MAAI,E;;EACrDD,KAAK,EAAC;AAAc;;;EAIRA,KAAK,EAAC;AAAuB;;;EAG7BA,KAAK,EAAC;AAAc;;EACfA,KAAK,EAAC;AAAc;;EAQzCA,KAAK,EAAC;AAAW;iCAClBC,mBAAA,CAA0C;EAAtCD,KAAK,EAAC;AAAS,I,aAACC,mBAAA,CAAiB,cAAX,MAAI,E;;EAC1BD,KAAK,EAAC;AAAW;;;uBArBzBG,mBAAA,CA2BO,OA3BPC,UA2BO,GA1BAH,mBAAA,CAiBD,OAjBCI,UAiBD,GAhBFC,UAAsE,EACrEL,mBAAA,CAcI,MAdJM,UAcI,I,kBAZDJ,mBAAA,CAUKK,SAAA,QAAAC,WAAA,CAV4CC,KAAA,CAAAC,KAAK,GAApBC,IAAI,EAACC,KAAK;yBAA5CV,mBAAA,CAUK;MAVDH,KAAK,EAAC,cAAc;MAAiCc,GAAG,EAAED;QAC1DZ,mBAAA,CAQI;MARAc,IAAI,mBAAiBH,IAAI,CAACI;QAC1Bf,mBAAA,CAEM,OAFNgB,UAEM,GADFhB,mBAAA,CAAiJ;MAA3IiB,GAAG,gEAA+DN,IAAI,CAACO,MAAM;MAAEnB,KAAK,EAAC,eAAe;MAACE,KAAmC,EAAnC;QAAA;QAAA;MAAA;2CAE/GD,mBAAA,CAGM,OAHNmB,UAGM,GAFFnB,mBAAA,CAAgD,QAAhDoB,UAAgD,EAAAC,gBAAA,CAAnBV,IAAI,CAACW,KAAK,iB;sCAQ3DtB,mBAAA,CAMM,OANNuB,WAMM,GALFC,WAA0C,EAC1CxB,mBAAA,CAGK,MAHLyB,WAGK,I,kBAFDvB,mBAAA,CAAsHK,SAAA,QAAAC,WAAA,CAA1FC,KAAA,CAAAiB,KAAK,GAApBf,IAAI,EAACC,KAAK;yBAAvBV,mBAAA,CAAsH;MAAlFW,GAAG,EAAED;IAAK,IAAGZ,mBAAA,CAAgE;MAA5Dc,IAAI,yBAAuBH,IAAI,CAACgB;wBAAYhB,IAAI,CAACiB,KAAK,wBAAAC,WAAA,E"}]}